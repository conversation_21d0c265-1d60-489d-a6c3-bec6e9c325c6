---
type: "always_apply"
---

# backend-fule.md

# Project Setup Rules

## 🐳 Docker & Container Conventions

- **Use `docker compose`** not `docker-compose` (Docker Compose V2 syntax)

## 🐍 Python Development Environment

- **Use `uv` for Python package management** - Modern, fast Python package installer
- **Python version**: 3.12+ (as specified in pyproject.toml)
- **Virtual environment**: Use `uv venv` to create `.venv` directory
- **Dependency installation**: Use `uv pip install -e ".[all]"` for development
- **Python execution**: Use `uv run python` instead of bare `python` command
- **Package structure**: Use `src/` layout with `PYTHONPATH=/app/src:$PYTHONPATH`

## 🗄️ Database & Migrations

- **Database**: PostgreSQL 15+ with asyncpg driver
- **Migrations**: Use Alembic with `alembic revision --autogenerate -m "message"`
- **Migration application**: `alembic upgrade head`
- **Connection pooling**: Use asyncpg for high-performance async connections

## 🔄 Background Jobs & Queues

- **Queue system**: BullMQ with Redis backend
- **Worker pattern**: Unified worker handling all queues
- **Queue configuration**: JSON-based configuration in `queue_config.json`
- **Job processors**: Modular processor files in `processors/` directory

## 📁 Project Structure Conventions

- **Backend structure**: `src/` layout with modules under `src/modules/`
- **Configuration**: Environment files (`.env`) with example files (`.env.example`)
- **Logs**: Centralized logging to `logs/` directory
- **Storage**: Local storage in `storage/` directory for development

## 🔧 Development Workflow

- **Setup script**: Use `scripts/setup.sh` for initial environment setup
- **Local development**: Use Docker Compose for full stack development
- **Database admin**: pgweb for database administration (port 8081)
- **API documentation**: Auto-generated docs at `/docs` endpoint

## 📦 Dependency Management

- **Backend**: pyproject.toml with categorized dependencies (main, dev, optional)
- **Frontend**: package.json with standard Next.js dependencies
- **Lock files**: Maintain `uv.lock` and `package-lock.json`
- **Version pinning**: Pin major versions for stability

## 🔒 Security & Configuration

- **Environment variables**: Never commit `.env` files, use `.env.example` as template
- **Secrets management**: Use environment variables for all sensitive data
- **CORS**: Configure CORS properly for frontend-backend communication
- **API authentication**: JWT-based authentication with proper token handling

## 📊 Monitoring & Observability

- **Health checks**: Implement `/health` endpoints for all services
- **Logging**: Structured JSON logging with proper log levels
- **Metrics**: Prepare for Prometheus/Grafana integration (commented in compose)
- **Error handling**: Comprehensive error handling with proper HTTP status codes

## 🚀 Deployment Conventions

- **Container registry**: Use descriptive image names and tags
- **Environment separation**: Clear separation between dev/staging/prod
- **Configuration management**: Environment-specific configuration files
- **Rollback strategy**: Versioned deployments with rollback capability

These rules capture your project's established patterns and ensure consistency across development, testing, and deployment workflows.

## Documentations

- **Document updates** After every operation have a look at `/docs` and update existing docs to keep them updated or create new if it is entirely a new feature.
