#!/bin/bash

# E-commerce Hub Deployment Script
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ID=${1:-"your-gcp-project-id"}
REGION=${2:-"us-central1"}

echo -e "${GREEN}🚀 Starting E-commerce Hub deployment...${NC}"

# Check if required tools are installed
check_dependencies() {
    echo -e "${YELLOW}📋 Checking dependencies...${NC}"

    if ! command -v gcloud &> /dev/null; then
        echo -e "${RED}❌ Google Cloud CLI not found. Please install it first.${NC}"
        exit 1
    fi

    if ! command -v docker &> /dev/null; then
        echo -e "${RED}❌ Docker not found. Please install it first.${NC}"
        exit 1
    fi

    if ! command -v terraform &> /dev/null; then
        echo -e "${RED}❌ Terraform not found. Please install it first.${NC}"
        exit 1
    fi

    if ! command -v uv &> /dev/null; then
        echo -e "${YELLOW}⚠️  uv not found. Installing...${NC}"
        curl -LsSf https://astral.sh/uv/install.sh | sh
        export PATH="$HOME/.cargo/bin:$PATH"
    fi

    echo -e "${GREEN}✅ All dependencies found${NC}"
}

# Set up GCP project
setup_gcp() {
    echo -e "${YELLOW}🔧 Setting up GCP project...${NC}"

    gcloud config set project $PROJECT_ID
    gcloud auth configure-docker

    echo -e "${GREEN}✅ GCP project configured${NC}"
}

# Build and push Docker images
build_and_push() {
    echo -e "${YELLOW}🐳 Building and pushing Docker images...${NC}"

    # Build backend image
    echo "Building backend image..."
    cd backend
    docker build -t gcr.io/$PROJECT_ID/ecommerce-hub-api:latest .
    docker push gcr.io/$PROJECT_ID/ecommerce-hub-api:latest
    cd ..

    # Build frontend image
    echo "Building frontend image..."
    cd frontend
    docker build -t gcr.io/$PROJECT_ID/ecommerce-hub-frontend:latest .
    docker push gcr.io/$PROJECT_ID/ecommerce-hub-frontend:latest
    cd ..

    echo -e "${GREEN}✅ Docker images built and pushed${NC}"
}

# Deploy infrastructure
deploy_infrastructure() {
    echo -e "${YELLOW}🏗️  Deploying infrastructure...${NC}"

    cd infrastructure

    # Initialize Terraform if needed
    if [ ! -d ".terraform" ]; then
        terraform init
    fi

    # Plan and apply
    terraform plan -var="project_id=$PROJECT_ID"
    terraform apply -var="project_id=$PROJECT_ID" -auto-approve

    cd ..

    echo -e "${GREEN}✅ Infrastructure deployed${NC}"
}

# Deploy frontend to Cloud Storage and CDN
deploy_frontend() {
    echo -e "${YELLOW}🌐 Deploying frontend...${NC}"

    cd frontend

    # Build the frontend
    npm run build

    # Upload to Cloud Storage
    gsutil -m rsync -r -d dist/ gs://$PROJECT_ID-ecommerce-hub-assets/

    # Set public read permissions
    gsutil -m acl ch -r -u AllUsers:R gs://$PROJECT_ID-ecommerce-hub-assets/

    cd ..

    echo -e "${GREEN}✅ Frontend deployed${NC}"
}

# Main deployment flow
main() {
    echo -e "${GREEN}🎯 Deploying E-commerce Hub to GCP${NC}"
    echo -e "${YELLOW}Project ID: $PROJECT_ID${NC}"
    echo -e "${YELLOW}Region: $REGION${NC}"
    echo ""

    check_dependencies
    setup_gcp
    build_and_push
    deploy_infrastructure
    deploy_frontend

    echo ""
    echo -e "${GREEN}🎉 Deployment completed successfully!${NC}"
    echo -e "${YELLOW}📝 Next steps:${NC}"
    echo "1. Update your frontend environment variables with the API URL"
    echo "2. Set up your Shopify Partner account"
    echo "3. Configure your WooCommerce test site"
    echo "4. Test the application"
    echo ""
    echo -e "${GREEN}🔗 Your API URL:${NC}"
    cd infrastructure && terraform output api_url && cd ..
}

# Run main function
main
