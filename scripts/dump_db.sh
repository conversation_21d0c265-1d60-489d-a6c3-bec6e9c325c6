#!/bin/bash

# <PERSON><PERSON>t to dump the PostgreSQL database from the Docker container
set -e

DB_CONTAINER="e-commerce-db-1" # Or whatever your db service container name is (e.g., e-commerce-db-1)
DB_NAME="ecommerce_db"
DB_USER="app_user"
DUMP_FILE="data/ecommerce_dev_data_$(date +%Y%m%d_%H%M%S).sql"

echo "Dumping database '$DB_NAME' from container '$DB_CONTAINER' to '$DUMP_FILE'..."

docker exec -t $DB_CONTAINER pg_dump -U $DB_USER -d $DB_NAME > $DUMP_FILE

echo "Database dump complete: $DUMP_FILE"
echo "You can now commit this file to Git if you wish to version control this data snapshot."
