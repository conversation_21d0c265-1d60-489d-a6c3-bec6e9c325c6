#!/bin/bash

# E-commerce Hub Setup Script
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PYTHON_VERSION="3.11"
NODE_VERSION="18"

echo -e "${GREEN}🚀 E-commerce Hub Setup Script${NC}"
echo -e "${BLUE}This script will set up your local development environment${NC}"
echo ""

# Check if running on supported OS
check_os() {
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        OS="linux"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macos"
    elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "cygwin" ]]; then
        OS="windows"
    else
        echo -e "${RED}❌ Unsupported operating system: $OSTYPE${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ Detected OS: $OS${NC}"
}

# Check if required tools are installed
check_dependencies() {
    echo -e "${YELLOW}📋 Checking dependencies...${NC}"

    # Check Python
    if command -v python3 &> /dev/null; then
        PYTHON_VER=$(python3 --version | cut -d' ' -f2 | cut -d'.' -f1,2)
        echo -e "${GREEN}✅ Python $PYTHON_VER found${NC}"
    else
        echo -e "${RED}❌ Python 3 not found. Please install Python $PYTHON_VERSION or higher.${NC}"
        exit 1
    fi

    # Check uv (modern Python package manager)
    if command -v uv &> /dev/null; then
        echo -e "${GREEN}✅ uv $(uv --version | cut -d' ' -f2) found${NC}"
        UV_AVAILABLE=true
    else
        echo -e "${YELLOW}⚠️  uv not found. Will install it or use pip as fallback.${NC}"
        UV_AVAILABLE=false
    fi

    # Check Node.js
    if command -v node &> /dev/null; then
        NODE_VER=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
        if [ "$NODE_VER" -ge "$NODE_VERSION" ]; then
            echo -e "${GREEN}✅ Node.js v$(node --version | cut -d'v' -f2) found${NC}"
        else
            echo -e "${RED}❌ Node.js version $NODE_VERSION or higher required. Found: $(node --version)${NC}"
            exit 1
        fi
    else
        echo -e "${RED}❌ Node.js not found. Please install Node.js $NODE_VERSION or higher.${NC}"
        exit 1
    fi

    # Check npm
    if command -v npm &> /dev/null; then
        echo -e "${GREEN}✅ npm $(npm --version) found${NC}"
    else
        echo -e "${RED}❌ npm not found. Please install npm.${NC}"
        exit 1
    fi

    # Check PostgreSQL
    if command -v psql &> /dev/null; then
        echo -e "${GREEN}✅ PostgreSQL found${NC}"
    else
        echo -e "${YELLOW}⚠️  PostgreSQL not found. You'll need to install it or use Docker.${NC}"
    fi

    # Check Docker (optional)
    if command -v docker &> /dev/null; then
        echo -e "${GREEN}✅ Docker found${NC}"
        DOCKER_AVAILABLE=true
    else
        echo -e "${YELLOW}⚠️  Docker not found. Docker setup will be skipped.${NC}"
        DOCKER_AVAILABLE=false
    fi

    # Check Git
    if command -v git &> /dev/null; then
        echo -e "${GREEN}✅ Git found${NC}"
    else
        echo -e "${RED}❌ Git not found. Please install Git.${NC}"
        exit 1
    fi
}

# Install uv if not available
install_uv() {
    if [ "$UV_AVAILABLE" = false ]; then
        echo -e "${YELLOW}📦 Installing uv...${NC}"
        curl -LsSf https://astral.sh/uv/install.sh | sh
        export PATH="$HOME/.cargo/bin:$PATH"
        UV_AVAILABLE=true
        echo -e "${GREEN}✅ uv installed${NC}"
    fi
}

# Setup backend
setup_backend() {
    echo -e "${YELLOW}🐍 Setting up backend...${NC}"

    cd backend

    if [ "$UV_AVAILABLE" = true ]; then
        echo "Using uv for Python dependency management..."

        # Create virtual environment with uv
        if [ ! -d ".venv" ]; then
            echo "Creating Python virtual environment with uv..."
            uv venv
        fi

        # Install dependencies with uv
        echo "Installing Python dependencies with uv..."
        uv pip install -e ".[all]"

    else
        echo "Using pip for Python dependency management..."

        # Create virtual environment
        if [ ! -d "venv" ]; then
            echo "Creating Python virtual environment..."
            python3 -m venv venv
        fi

        # Activate virtual environment
        source venv/bin/activate

        # Upgrade pip
        pip install --upgrade pip

        # Install dependencies
        echo "Installing Python dependencies..."
        pip install -r requirements.txt
    fi

    # Create .env file if it doesn't exist
    if [ ! -f ".env" ]; then
        echo "Creating backend .env file..."
        cp .env.example .env
        echo -e "${YELLOW}📝 Please edit backend/.env with your configuration${NC}"
    fi

    cd ..
    echo -e "${GREEN}✅ Backend setup complete${NC}"
}

# Setup frontend
setup_frontend() {
    echo -e "${YELLOW}⚛️  Setting up frontend...${NC}"

    cd frontend

    # Install dependencies
    echo "Installing Node.js dependencies..."
    npm install

    # Create .env file if it doesn't exist
    if [ ! -f ".env" ]; then
        echo "Creating frontend .env file..."
        cp .env.example .env
        echo -e "${YELLOW}📝 Please edit frontend/.env with your configuration${NC}"
    fi

    cd ..
    echo -e "${GREEN}✅ Frontend setup complete${NC}"
}

# Setup Docker environment
setup_docker() {
    if [ "$DOCKER_AVAILABLE" = true ]; then
        echo -e "${YELLOW}🐳 Setting up Docker environment...${NC}"

        # Check if docker-compose is available
        if command -v docker-compose &> /dev/null; then
            COMPOSE_CMD="docker-compose"
        elif docker compose version &> /dev/null; then
            COMPOSE_CMD="docker compose"
        else
            echo -e "${RED}❌ Docker Compose not found${NC}"
            return 1
        fi

        echo "Building Docker containers..."
        $COMPOSE_CMD build

        echo -e "${GREEN}✅ Docker setup complete${NC}"
        echo -e "${BLUE}💡 You can now run: $COMPOSE_CMD up -d${NC}"
    fi
}

# Run database migrations
run_migrations() {
    echo -e "${YELLOW}🔄 Running database migrations...${NC}"

    cd backend

    if [ "$UV_AVAILABLE" = true ]; then
        # Activate uv environment
        if [ -d ".venv" ]; then
            source .venv/bin/activate
        else
            echo -e "${YELLOW}⚠️ UV virtual environment not found at expected location${NC}"
            return 1
        fi
    else
        # Activate standard venv
        if [ -d "venv" ]; then
            source venv/bin/activate
        else
            echo -e "${YELLOW}⚠️ Python virtual environment not found at expected location${NC}"
            return 1
        fi
    fi

    # Check if we have any migration scripts
    if [ -d "migrations/versions" ] && [ "$(ls -A migrations/versions 2>/dev/null)" ]; then
        echo "Migration scripts exist. Running migrations..."
    else
        echo "No migration scripts found. Generating initial migration..."
        alembic revision --autogenerate -m "Initial migration"
    fi
    
    # Apply all migrations
    echo "Applying migrations..."
    alembic upgrade head
    
    # Verify database tables were created
    echo "Verifying database tables..."
    python -c "
import sqlite3
import os

# Connect to the database
conn = sqlite3.connect('ecommerce.db')
cursor = conn.cursor()

# Get list of tables
cursor.execute(\"SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%';\")
tables = cursor.fetchall()

print(f'Found {len(tables)} tables in the database:')
for table in tables:
    print(f'- {table[0]}')

conn.close()
"

    echo -e "${GREEN}✅ Database migrations complete${NC}"
    cd ..
}


# Display next steps
show_next_steps() {
    echo ""
    echo -e "${GREEN}🎉 Setup completed successfully!${NC}"
    echo ""
    echo -e "${BLUE}📋 Next steps:${NC}"
    echo "1. Configure your environment variables:"
    echo "   - Edit backend/.env with your database and API credentials"
    echo "   - Edit frontend/.env with your API URL"
    echo ""
    echo "2. Start the development servers:"
    echo "   ${YELLOW}Backend:${NC}"
    echo "   cd backend && source venv/bin/activate && uvicorn main:app --reload"
    echo ""
    echo "   ${YELLOW}Frontend:${NC}"
    echo "   cd frontend && npm run dev"
    echo ""
    echo "3. Or use Docker:"
    echo "   docker compose up"
    echo ""
    echo "4. Access the application:"
    echo "   - Frontend: http://localhost:5173"
    echo "   - Backend API: http://localhost:8000"
    echo "   - API Docs: http://localhost:8000/docs"
    echo ""
    echo "5. Set up your e-commerce integrations:"
    echo "   - Follow docs/SHOPIFY_SETUP.md for Shopify"
    echo "   - Follow docs/WOOCOMMERCE_SETUP.md for WooCommerce"
    echo ""
    echo -e "${GREEN}Happy coding! 🚀${NC}"
}

# Main setup flow
main() {
    check_os
    check_dependencies
    install_uv
    setup_backend
    setup_frontend
    setup_docker
    run_migrations
    show_next_steps
}

# Run main function
main
