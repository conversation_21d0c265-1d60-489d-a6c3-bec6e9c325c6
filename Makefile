# Makefile for E-commerce Project

# Use bash for all commands
SHELL := /bin/bash

.PHONY: help setup up down stop rebuild logs sync test shell clean dump restore

help:
	@echo "Usage: make [target]"
	@echo ""
	@echo "Targets:"
	@echo "  help        Show this help message."
	@echo "  setup       Set up the development environment and build Docker images."
	@echo "  up          Start all services in the background."
	@echo "  down        Stop all services."
	@echo "  stop        <PERSON><PERSON> for down."
	@echo "  rebuild     Stop, remove containers, and rebuild all services."
	@echo "  logs        Follow logs of all services. Use 'make logs s=<service>' for a specific service."
	@echo "  sync        Install/update backend dependencies, including dev extras."
	@echo "  test        Sync environment and run backend Python tests locally."
	@echo "  shell       Get a shell into a running service. Use 'make shell s=<service>' (e.g., api, db)."
	@echo "  clean       Remove temporary files like __pycache__ and .pytest_cache."
	@echo "  dump        Dump the database to data/ directory"
	@echo "  restore     Restore the database. Usage: make restore DUMP_FILE=<path_to_sql_dump_file>"

setup:
	@echo "Setting up environment..."
	@if [ ! -f backend/.env ]; then \
		echo "Creating backend/.env from example..."; \
		cp backend/.env.example backend/.env; \
	fi
	@if [ ! -f frontend/.env ]; then \
		echo "Creating frontend/.env from example..."; \
		cp frontend/.env.example frontend/.env; \
	fi
	@echo "Building Docker images..."
	docker compose build

logs:
	@echo "Following logs... (Press Ctrl+C to stop)"
	docker compose logs -f $(s)

sync:
	@echo "Installing/syncing backend dev environment..."
	(cd backend && uv pip install -e ".[dev]")

test: sync
	@echo "Running backend tests locally with uv..."
	(cd backend && TESTING=1 uv run python -m pytest)

# Example: make shell s=api
shell:
	@echo "Opening shell in service: $(s)..."
	docker compose exec $(s) bash

clean:
	@echo "Cleaning up temporary files..."
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -exec rm -rf {} +
	find . -type d -name ".pytest_cache" -exec rm -rf {} +


dump:
	@echo "Dumping database..."
	./scripts/dump_db.sh

restore:
	@echo "Restoring database from $(DUMP_FILE)..."
	./scripts/restore_db.sh $(DUMP_FILE)
