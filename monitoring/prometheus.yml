global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert-rules.yml"

scrape_configs:
  - job_name: "prometheus"
    static_configs:
      - targets: ["localhost:9090"]

  - job_name: "api"
    static_configs:
      - targets: ["api:8123"]
    metrics_path: "/metrics"
    scrape_interval: 30s

  - job_name: "worker"
    static_configs:
      - targets: ["worker:9091"]
    metrics_path: "/metrics"
    scrape_interval: 30s

  - job_name: "video-worker"
    static_configs:
      - targets: ["video-worker:8000"]
    metrics_path: "/metrics"
    scrape_interval: 30s

  # Airbyte Sync Services
  - job_name: "webhook-receiver"
    static_configs:
      - targets: ["webhook-receiver:9090"]
    metrics_path: "/metrics"
    scrape_interval: 15s

  - job_name: "orchestration-worker"
    static_configs:
      - targets: ["orchestration-worker:9091"]
    metrics_path: "/metrics"
    scrape_interval: 15s

  - job_name: "consumer-service"
    static_configs:
      - targets: ["consumer-service:9092"]
    metrics_path: "/metrics"
    scrape_interval: 15s

  # Airbyte OSS Services
  - job_name: "airbyte-server"
    static_configs:
      - targets: ["airbyte-server:8001"]
    metrics_path: "/api/v1/health"
    scrape_interval: 30s

  # Infrastructure
  - job_name: "postgres"
    static_configs:
      - targets: ["db:5432"]
    scrape_interval: 30s

  - job_name: "redis"
    static_configs:
      - targets: ["redis:6379"]
    scrape_interval: 30s
