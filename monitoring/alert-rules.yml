groups:
  - name: airbyte_shopify_sync
    rules:
      # Webhook receiver alerts
      - alert: WebhookReceiverDown
        expr: up{job="webhook-receiver"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Webhook receiver service is down"
          description: "The webhook receiver service has been down for more than 1 minute."

      - alert: HighWebhookErrorRate
        expr: rate(webhook_requests_total{status!="success"}[5m]) / rate(webhook_requests_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High webhook error rate detected"
          description: "Webhook error rate is {{ $value | humanizePercentage }} for shop {{ $labels.shop_domain }}"

      - alert: WebhookHMACFailures
        expr: rate(webhook_hmac_failures_total[5m]) > 0.1
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "HMAC verification failures detected"
          description: "HMAC verification failures detected for shop {{ $labels.shop_domain }}"

      - alert: WebhookProcessingLag
        expr: webhook_processing_lag_seconds > 300
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High webhook processing lag"
          description: "Webhook processing lag is {{ $value }}s for {{ $labels.shop_domain }}/{{ $labels.topic }}"

      # Orchestration worker alerts
      - alert: OrchestrationWorkerDown
        expr: up{job="orchestration-worker"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Orchestration worker service is down"
          description: "The orchestration worker service has been down for more than 1 minute."

      - alert: HighSyncJobFailureRate
        expr: rate(sync_jobs_total{status="failed"}[10m]) / rate(sync_jobs_total[10m]) > 0.2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High sync job failure rate"
          description: "Sync job failure rate is {{ $value | humanizePercentage }} for {{ $labels.shop_domain }}/{{ $labels.entity }}"

      - alert: SyncJobsStuck
        expr: active_syncs > 0 and rate(sync_jobs_total{status=~"completed|failed"}[30m]) == 0
        for: 30m
        labels:
          severity: warning
        annotations:
          summary: "Sync jobs appear to be stuck"
          description: "Active syncs detected but no completed/failed jobs in 30 minutes for {{ $labels.shop_domain }}"

      - alert: TooManyActiveSyncs
        expr: active_syncs > 5
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "Too many active syncs"
          description: "{{ $value }} active syncs detected for {{ $labels.shop_domain }}, may indicate stuck jobs"

      # Consumer service alerts
      - alert: ConsumerServiceDown
        expr: up{job="consumer-service"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Consumer service is down"
          description: "The consumer service has been down for more than 1 minute."

      - alert: HighStagingRecordsBacklog
        expr: staging_records_pending > 1000
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "High staging records backlog"
          description: "{{ $value }} unprocessed {{ $labels.entity }} records for {{ $labels.shop_domain }}"

      - alert: ProcessingErrorsHigh
        expr: rate(processing_errors_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High processing error rate"
          description: "Processing error rate is {{ $value }}/sec for {{ $labels.shop_domain }}/{{ $labels.entity }}"

      - alert: NoRecordsProcessed
        expr: rate(records_processed_total[30m]) == 0 and staging_records_pending > 0
        for: 30m
        labels:
          severity: warning
        annotations:
          summary: "No records being processed"
          description: "No records processed in 30 minutes but {{ $labels.staging_records_pending }} records pending for {{ $labels.shop_domain }}/{{ $labels.entity }}"

      # Airbyte API alerts
      - alert: AirbyteAPIErrors
        expr: rate(airbyte_api_requests_total{status!~"2.."}[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "Airbyte API errors detected"
          description: "Airbyte API error rate is {{ $value }}/sec for endpoint {{ $labels.endpoint }}"

      # Infrastructure alerts
      - alert: DatabaseConnectionFailures
        expr: up{job="postgres"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Database connection failures"
          description: "Cannot connect to PostgreSQL database"

      - alert: RedisConnectionFailures
        expr: up{job="redis"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Redis connection failures"
          description: "Cannot connect to Redis"

      # Performance alerts
      - alert: HighSyncDuration
        expr: sync_duration_seconds > 3600
        for: 0m
        labels:
          severity: warning
        annotations:
          summary: "Sync taking too long"
          description: "Sync for {{ $labels.shop_domain }}/{{ $labels.entity }} took {{ $value }}s"

      - alert: HighProcessingDuration
        expr: processing_duration_seconds > 300
        for: 0m
        labels:
          severity: warning
        annotations:
          summary: "Processing taking too long"
          description: "Processing batch for {{ $labels.shop_domain }}/{{ $labels.entity }} took {{ $value }}s"

  - name: airbyte_system
    rules:
      # Airbyte service health
      - alert: AirbyteServerDown
        expr: up{job="airbyte-server"} == 0
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "Airbyte server is down"
          description: "Airbyte server has been unreachable for more than 2 minutes"

      - alert: AirbyteWorkerDown
        expr: up{job="airbyte-worker"} == 0
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "Airbyte worker is down"
          description: "Airbyte worker has been unreachable for more than 2 minutes"
