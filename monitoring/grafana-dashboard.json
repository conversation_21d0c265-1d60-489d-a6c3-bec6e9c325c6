{"dashboard": {"id": null, "title": "E-commerce Platform Monitoring Dashboard", "tags": ["ecommerce", "media", "analytics", "monitoring"], "style": "dark", "timezone": "browser", "panels": [{"id": 1, "title": "Media Generation Success Rate", "type": "stat", "targets": [{"expr": "rate(media_generation_duration_seconds_count{status=\"success\"}[5m]) / rate(media_generation_duration_seconds_count[5m]) * 100", "legendFormat": "Success Rate %"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "unit": "percent", "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 80}, {"color": "green", "value": 95}]}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}}, {"id": 2, "title": "Media Push Success Rate", "type": "stat", "targets": [{"expr": "rate(media_push_duration_seconds_count{status=\"success\"}[5m]) / rate(media_push_duration_seconds_count[5m]) * 100", "legendFormat": "Success Rate %"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "unit": "percent", "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 80}, {"color": "green", "value": 95}]}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}}, {"id": 3, "title": "Media Generation Duration (95th percentile)", "type": "stat", "targets": [{"expr": "histogram_quantile(0.95, rate(media_generation_duration_seconds_bucket[5m]))", "legendFormat": "95th percentile"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "unit": "s", "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 30}, {"color": "red", "value": 60}]}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 4, "title": "Media Push Duration (95th percentile)", "type": "stat", "targets": [{"expr": "histogram_quantile(0.95, rate(media_push_duration_seconds_bucket[5m]))", "legendFormat": "95th percentile"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "unit": "s", "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 10}, {"color": "red", "value": 30}]}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}, {"id": 5, "title": "Media Generation Requests", "type": "graph", "targets": [{"expr": "rate(media_generation_duration_seconds_count[5m])", "legendFormat": "{{media_type}} - {{status}}"}], "yAxes": [{"label": "Requests/sec", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}}, {"id": 6, "title": "Media Push Requests", "type": "graph", "targets": [{"expr": "rate(media_push_duration_seconds_count[5m])", "legendFormat": "{{platform}} - {{status}}"}], "yAxes": [{"label": "Requests/sec", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}}, {"id": 7, "title": "Analytics Processing", "type": "graph", "targets": [{"expr": "rate(analytics_processing_duration_seconds_count[5m])", "legendFormat": "Processing Rate"}], "yAxes": [{"label": "Events/sec", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}}, {"id": 8, "title": "Error Rates", "type": "graph", "targets": [{"expr": "rate(media_generation_failures_total[5m])", "legendFormat": "Media Generation - {{media_type}} - {{failure_reason}}"}, {"expr": "rate(media_push_failures_total[5m])", "legendFormat": "Media Push - {{platform}} - {{failure_reason}}"}, {"expr": "rate(analytics_processing_failures_total[5m])", "legendFormat": "Analytics - {{failure_reason}}"}], "yAxes": [{"label": "Errors/sec", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}}, {"id": 9, "title": "System Health", "type": "table", "targets": [{"expr": "up", "legendFormat": "{{job}}"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"0": {"text": "DOWN", "color": "red"}, "1": {"text": "UP", "color": "green"}}, "type": "value"}], "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "green", "value": 1}]}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 32}}, {"id": 10, "title": "Queue Statistics", "type": "table", "targets": [{"expr": "celery_active_tasks", "legendFormat": "{{queue}}"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 32}}, {"id": 11, "title": "Sync Operations Rate", "type": "graph", "targets": [{"expr": "rate(sync_triggered_total[5m])", "legendFormat": "{{trigger_type}} - {{entity_type}} - {{store_domain}}"}], "yAxes": [{"label": "Syncs/sec", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 40}}, {"id": 12, "title": "Sync Success Rate", "type": "stat", "targets": [{"expr": "rate(sync_completed_total{status=\"success\"}[5m]) / rate(sync_triggered_total[5m]) * 100", "legendFormat": "Success Rate %"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "unit": "percent", "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 80}, {"color": "green", "value": 95}]}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 40}}, {"id": 13, "title": "Webhook Sync Triggers", "type": "graph", "targets": [{"expr": "rate(webhook_sync_triggers_total[5m])", "legendFormat": "{{shop_domain}} - {{topic}} - {{entity_type}}"}], "yAxes": [{"label": "Triggers/sec", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 48}}, {"id": 14, "title": "Direct Sync API Calls", "type": "graph", "targets": [{"expr": "rate(direct_sync_api_calls_total[5m])", "legendFormat": "{{store_domain}} - {{mode}} - {{status}}"}], "yAxes": [{"label": "API Calls/sec", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 48}}, {"id": 15, "title": "Sync Progress Updates", "type": "graph", "targets": [{"expr": "rate(sync_progress_updates_total[5m])", "legendFormat": "{{entity_type}} - {{stage}}"}], "yAxes": [{"label": "Updates/sec", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 56}}, {"id": 16, "title": "Airbyte Sync Jobs", "type": "graph", "targets": [{"expr": "rate(airbyte_sync_jobs_created_total[5m])", "legendFormat": "Created - {{store_domain}}"}, {"expr": "rate(airbyte_sync_jobs_completed_total{status=\"success\"}[5m])", "legendFormat": "Completed - {{store_domain}}"}], "yAxes": [{"label": "Jobs/sec", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 56}}, {"id": 17, "title": "Sync Completion Status", "type": "stat", "targets": [{"expr": "rate(sync_completed_total{status=\"success\"}[5m]) / rate(sync_triggered_total[5m]) * 100", "legendFormat": "Overall Success Rate %"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "unit": "percent", "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 90}, {"color": "green", "value": 95}]}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 64}}, {"id": 18, "title": "Sync Checkpoint Updates", "type": "graph", "targets": [{"expr": "rate(sync_checkpoint_updates_total[5m])", "legendFormat": "{{entity_type}} - {{stage}}"}], "yAxes": [{"label": "Updates/sec", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 64}}, {"id": 19, "title": "Sync Trigger Sources", "type": "piechart", "targets": [{"expr": "sum(rate(sync_triggered_total[1h])) by (trigger_type)", "legendFormat": "{{trigger_type}}"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 72}}, {"id": 20, "title": "Entity Sync Activity", "type": "barchart", "targets": [{"expr": "sum(rate(sync_triggered_total[1h])) by (entity_type)", "legendFormat": "{{entity_type}}"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 72}}, {"id": 21, "title": "Queue Length", "type": "graph", "targets": [{"expr": "celery_queue_length", "legendFormat": "{{queue}}"}], "yAxes": [{"label": "Tasks in Queue", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 80}}, {"id": 22, "title": "Active Tasks by Worker", "type": "graph", "targets": [{"expr": "celery_active_tasks", "legendFormat": "{{worker}} - {{queue}}"}], "yAxes": [{"label": "Active Tasks", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 80}}, {"id": 23, "title": "Worker Pool Size", "type": "stat", "targets": [{"expr": "sum(celery_worker_pool_size)", "legendFormat": "Total Pool Size"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1}, {"color": "red", "value": 10}]}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 88}}, {"id": 24, "title": "Worker Status", "type": "table", "targets": [{"expr": "celery_worker_status", "legendFormat": "{{worker}} - {{status}}"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 88}}, {"id": 25, "title": "Task Processing Rate", "type": "graph", "targets": [{"expr": "rate(celery_task_rate[5m])", "legendFormat": "{{queue}} - {{task_name}} - {{status}}"}], "yAxes": [{"label": "Tasks/sec", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 96}}, {"id": 26, "title": "Scheduled Tasks", "type": "stat", "targets": [{"expr": "sum(celery_scheduled_tasks)", "legendFormat": "Total Scheduled"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 96}}, {"id": 27, "title": "Task Processing Duration (95th percentile)", "type": "stat", "targets": [{"expr": "histogram_quantile(0.95, rate(celery_task_processing_duration_seconds_bucket[5m]))", "legendFormat": "95th percentile"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "unit": "s", "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 30}, {"color": "red", "value": 120}]}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 104}}, {"id": 28, "title": "Task Failure Rate", "type": "stat", "targets": [{"expr": "rate(celery_task_failures_total[5m]) / rate(celery_task_rate[5m]) * 100", "legendFormat": "Failure Rate %"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "unit": "percent", "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 5}, {"color": "red", "value": 10}]}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 104}}, {"id": 29, "title": "Task Failures by Type", "type": "barchart", "targets": [{"expr": "rate(celery_task_failures_total[1h])", "legendFormat": "{{queue}} - {{task_name}} - {{failure_reason}}"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 112}}, {"id": 30, "title": "Queue Throughput", "type": "graph", "targets": [{"expr": "rate(celery_task_rate{status=\"success\"}[5m])", "legendFormat": "{{queue}} - Success"}, {"expr": "rate(celery_task_rate{status=\"failure\"}[5m])", "legendFormat": "{{queue}} - Failure"}], "yAxes": [{"label": "Tasks/sec", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 112}}], "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "templating": {"list": []}, "annotations": {"list": []}, "refresh": "30s", "schemaVersion": 27, "version": 0, "links": []}}