services:
  # PostgreSQL Database (Enhanced for Airbyte)
  db:
    image: postgres:15
    environment:
      POSTGRES_DB: ecommerce_db
      POSTGRES_USER: app_user
      POSTGRES_PASSWORD: dev_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/src/core/db/init-airbyte-db.sql:/docker-entrypoint-initdb.d/init-airbyte-db.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U app_user -d ecommerce_db"]
      interval: 10s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    networks:
      - api_network

  # Redis for job queue and caching
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
    restart: unless-stopped
    networks:
      - api_network

  # ProductVideo Backend API (Enhanced)
  api:
    build:
      context: ./backend
      dockerfile: Dockerfile.api
    ports:
      - "8123:8123"
    env_file:
      - ./backend/.env
    extra_hosts:
      - "host.docker.internal:host-gateway"
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./backend:/app
      - ./logs:/app/logs
      - ./storage:/app/storage
    environment:
      - PYTHONUNBUFFERED=1
      - PYTHONDONTWRITEBYTECODE=1
      - PYTHONPATH=/app/src:$PYTHONPATH
    command: uvicorn src.servers.api.main:app --host 0.0.0.0 --port 8123 --reload
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8123/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    networks:
      - api_network

  # Unified Worker for all queues (Enhanced)
  worker:
    build:
      context: ./backend
      dockerfile: Dockerfile.worker
    env_file:
      - ./backend/.env
    environment:
      - WORKER_ID=worker-${HOSTNAME:-default}
      - PYTHONDONTWRITEBYTECODE=1
      # QUEUE_TYPE is intentionally not set, so the worker handles all queues
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./backend:/app
      - ./logs:/app/logs
      - ./storage:/app/storage
    user: worker
    command: celery -A servers.worker.main worker --loglevel=info --concurrency=4 -Q bulk-sync,sync-status,sync-monitoring,sync-cleanup,webhook-processing,webhook-cleanup,webhook-retry,media-generation,media-push,media-batch,media-cleanup,system,deprecated,celery
    restart: on-failure
    networks:
      - api_network
    deploy:
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s

  # Celery Beat Scheduler for periodic tasks (Enhanced)
  worker-beat:
    build:
      context: ./backend
      dockerfile: Dockerfile.worker
    env_file:
      - ./backend/.env
    environment:
      - WORKER_ID=beat-${HOSTNAME:-default}
      - PYTHONDONTWRITEBYTECODE=1
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./backend:/app
      - ./logs:/app/logs
      - ./storage:/app/storage
    user: worker
    command: celery -A servers.worker.main beat --loglevel=info --scheduler celery.beat.PersistentScheduler --schedule /tmp/celerybeat-schedule
    restart: on-failure
    networks:
      - api_network
    deploy:
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s

  # App (Enhanced)
  app:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    ports:
      - "3000:3000"
    env_file:
      - ./frontend/.env
    volumes:
      - ./frontend:/app
      - app_data:/app/node_modules
    depends_on:
      - api
    command: npm run dev -- --port 3000
    restart: unless-stopped
    networks:
      - api_network

  # Database Admin (Development)
  pgweb:
    image: sosedoff/pgweb
    ports:
      - "8081:8081"
    environment:
      DATABASE_URL: ****************************************/ecommerce_db?sslmode=disable
    depends_on:
      db:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - api_network

  # Flower Dashboard for Celery (Development)
  flower:
    image: mher/flower:latest
    ports:
      - "5555:5555"
    environment:
      CELERY_BROKER_URL: redis://redis:6379/0
      CELERY_RESULT_BACKEND: redis://redis:6379/0
    depends_on:
      redis:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - api_network

  # # Prometheus for monitoring (Enhanced)
  # prometheus:
  #   image: prom/prometheus:latest
  #   ports:
  #     - "9090:9090"
  #   volumes:
  #     - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
  #     - prometheus_data:/prometheus
  #   command:
  #     - "--config.file=/etc/prometheus/prometheus.yml"
  #     - "--storage.tsdb.path=/prometheus"
  #     - "--web.console.libraries=/etc/prometheus/console_libraries"
  #     - "--web.console.templates=/etc/prometheus/consoles"
  #     - "--storage.tsdb.retention.time=200h"
  #     - "--web.enable-lifecycle"
  #   restart: unless-stopped
  #   networks:
  #     - api_network

  # grafana:
  #   image: grafana/grafana:latest
  #   ports:
  #     - "3030:3000"
  #   environment:
  #     - GF_SECURITY_ADMIN_PASSWORD=admin
  #   volumes:
  #     - grafana_data:/var/lib/grafana
  #   restart: unless-stopped
  #   networks:
  #     - api_network

  # # Mailhog for email testing
  # mailhog:
  #   image: mailhog/mailhog:latest
  #   ports:
  #     - "1025:1025" # SMTP
  #     - "8025:8025" # Web UI
  #   restart: unless-stopped
  #   networks:
  #     - api_network

  # # Nginx for reverse proxy and static files
  # nginx:
  #   image: nginx:alpine
  #   ports:
  #     - "80:80"
  #     - "443:443" # For HTTPS, if configured
  #   volumes:
  #     - ./nginx.conf:/etc/nginx/nginx.conf
  #     # Mount backend static files if needed by Nginx
  #     - ./backend/storage:/var/www/static # Assuming backend static files are in backend/storage
  #     # Mount .htpasswd if used for admin panel
  #     # - ./nginx/.htpasswd:/etc/nginx/.htpasswd # You'll need to create this file
  #   depends_on:
  #     - api
  #     - app
  #   healthcheck:
  #     test: ["CMD", "curl", "-f", "http://localhost/health"]
  #     interval: 30s
  #     timeout: 10s
  #     retries: 3
  #   restart: unless-stopped
  #   networks:
  #     - api_network

volumes:
  postgres_data:
  redis_data:
  app_data:
  prometheus_data:
  grafana_data:

networks:
  api_network:
    name: api_network
    driver: bridge
