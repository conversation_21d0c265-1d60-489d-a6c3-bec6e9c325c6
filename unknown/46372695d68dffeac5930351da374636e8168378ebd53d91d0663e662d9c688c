"use client";

import { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Search,
  Filter,
  ChevronLeft,
  ChevronRight,
  Package,
  Tag,
} from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import ShopifyService from "@/services/shopifyService";

interface Product {
  id: string;
  title: string;
  handle: string;
  images: Array<{
    id: string;
    src: string;
    alt?: string;
  }>;
  variants: Array<{
    id: string;
    title: string;
    price: string;
    sku?: string;
  }>;
  tags: string[];
  productType: string;
  vendor: string;
  status: string;
}

interface ProductSelectGridProps {
  selectedProducts: string[];
  onSelectionChange: (productIds: string[]) => void;
}

export function ProductSelectGrid({
  selectedProducts,
  onSelectionChange,
}: ProductSelectGridProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedTag, setSelectedTag] = useState<string>("");
  const [selectedCollection, setSelectedCollection] = useState<string>("");
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedVariants, setSelectedVariants] = useState<
    Record<string, string[]>
  >({});

  const pageSize = 12;

  // Fetch products from Shopify
  const {
    data: productsData,
    isLoading,
    error,
  } = useQuery({
    queryKey: [
      "products",
      searchQuery,
      selectedTag,
      selectedCollection,
      currentPage,
    ],
    queryFn: () =>
      ShopifyService.getProducts({
        search: searchQuery,
        tag: selectedTag,
        collection: selectedCollection,
        page: currentPage,
        limit: pageSize,
      }),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Fetch collections and tags for filters
  const { data: collectionsData } = useQuery({
    queryKey: ["collections"],
    queryFn: () => ShopifyService.getCollections(),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  const { data: tagsData } = useQuery({
    queryKey: ["product-tags"],
    queryFn: () => ShopifyService.getProductTags(),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  const products = productsData?.products || [];
  const totalPages = Math.ceil((productsData?.total || 0) / pageSize);
  const collections = collectionsData?.collections || [];
  const tags = tagsData?.tags || [];

  const handleProductSelect = (productId: string, checked: boolean) => {
    if (checked) {
      onSelectionChange([...selectedProducts, productId]);
    } else {
      onSelectionChange(selectedProducts.filter((id) => id !== productId));
      // Remove variant selections for this product
      const newSelectedVariants = { ...selectedVariants };
      delete newSelectedVariants[productId];
      setSelectedVariants(newSelectedVariants);
    }
  };

  const handleVariantSelect = (
    productId: string,
    variantId: string,
    checked: boolean
  ) => {
    const currentVariants = selectedVariants[productId] || [];

    if (checked) {
      setSelectedVariants({
        ...selectedVariants,
        [productId]: [...currentVariants, variantId],
      });
    } else {
      setSelectedVariants({
        ...selectedVariants,
        [productId]: currentVariants.filter((id) => id !== variantId),
      });
    }
  };

  const handleSelectAll = () => {
    const allProductIds = products.map((p) => p.id);
    onSelectionChange(allProductIds);
  };

  const handleDeselectAll = () => {
    onSelectionChange([]);
    setSelectedVariants({});
  };

  const isProductSelected = (productId: string) =>
    selectedProducts.includes(productId);
  const isAllSelected =
    products.length > 0 && products.every((p) => isProductSelected(p.id));
  const isSomeSelected = products.some((p) => isProductSelected(p.id));

  if (error) {
    return (
      <Card className="p-6">
        <div className="text-center text-red-600">
          <Package className="mx-auto h-12 w-12 mb-4 opacity-50" />
          <p>Failed to load products. Please try again.</p>
        </div>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search products..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        <Select
          value={selectedCollection}
          onValueChange={setSelectedCollection}
        >
          <SelectTrigger className="w-full sm:w-48">
            <SelectValue placeholder="All Collections" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="">All Collections</SelectItem>
            {collections.map((collection) => (
              <SelectItem key={collection.id} value={collection.id}>
                {collection.title}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={selectedTag} onValueChange={setSelectedTag}>
          <SelectTrigger className="w-full sm:w-48">
            <SelectValue placeholder="All Tags" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="">All Tags</SelectItem>
            {tags.map((tag) => (
              <SelectItem key={tag} value={tag}>
                <div className="flex items-center">
                  <Tag className="mr-2 h-3 w-3" />
                  {tag}
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Selection Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Checkbox
            checked={isAllSelected}
            onCheckedChange={(checked) => {
              if (checked) {
                handleSelectAll();
              } else {
                handleDeselectAll();
              }
            }}
            className="data-[state=indeterminate]:bg-primary data-[state=indeterminate]:text-primary-foreground"
            ref={(el) => {
              if (el) {
                el.indeterminate = isSomeSelected && !isAllSelected;
              }
            }}
          />
          <span className="text-sm text-muted-foreground">
            {selectedProducts.length > 0
              ? `${selectedProducts.length} product${selectedProducts.length !== 1 ? "s" : ""} selected`
              : "Select products"}
          </span>
        </div>

        {selectedProducts.length > 0 && (
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={handleDeselectAll}>
              Clear Selection
            </Button>
          </div>
        )}
      </div>

      {/* Products Grid */}
      {isLoading ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {Array.from({ length: pageSize }).map((_, i) => (
            <Card key={i}>
              <CardContent className="p-4">
                <Skeleton className="aspect-square w-full mb-4" />
                <Skeleton className="h-4 w-3/4 mb-2" />
                <Skeleton className="h-3 w-1/2" />
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {products.map((product) => (
            <Card
              key={product.id}
              className={`cursor-pointer transition-all hover:shadow-md ${
                isProductSelected(product.id) ? "ring-2 ring-primary" : ""
              }`}
            >
              <CardContent className="p-4">
                <div className="relative">
                  <img
                    src={product.images[0]?.src || "/placeholder-product.png"}
                    alt={product.images[0]?.alt || product.title}
                    className="aspect-square w-full object-cover rounded-md mb-4"
                  />
                  <Checkbox
                    checked={isProductSelected(product.id)}
                    onCheckedChange={(checked) =>
                      handleProductSelect(product.id, checked as boolean)
                    }
                    className="absolute top-2 right-2 bg-white shadow-sm"
                  />
                </div>

                <h3 className="font-medium text-sm mb-2 line-clamp-2">
                  {product.title}
                </h3>

                <div className="flex items-center justify-between text-xs text-muted-foreground mb-2">
                  <span>
                    {product.variants.length} variant
                    {product.variants.length !== 1 ? "s" : ""}
                  </span>
                  <Badge variant="outline" className="text-xs">
                    {product.productType || "Product"}
                  </Badge>
                </div>

                {product.tags.length > 0 && (
                  <div className="flex flex-wrap gap-1 mb-2">
                    {product.tags.slice(0, 2).map((tag) => (
                      <Badge key={tag} variant="secondary" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                    {product.tags.length > 2 && (
                      <Badge variant="outline" className="text-xs">
                        +{product.tags.length - 2}
                      </Badge>
                    )}
                  </div>
                )}

                {/* Variant Selection (shown when product is selected) */}
                {isProductSelected(product.id) &&
                  product.variants.length > 1 && (
                    <div className="mt-3 pt-3 border-t">
                      <p className="text-xs font-medium mb-2">
                        Select variants:
                      </p>
                      <div className="space-y-1 max-h-24 overflow-y-auto">
                        {product.variants.map((variant) => (
                          <div
                            key={variant.id}
                            className="flex items-center space-x-2"
                          >
                            <Checkbox
                              checked={(
                                selectedVariants[product.id] || []
                              ).includes(variant.id)}
                              onCheckedChange={(checked) =>
                                handleVariantSelect(
                                  product.id,
                                  variant.id,
                                  checked as boolean
                                )
                              }
                              className="scale-75"
                            />
                            <span className="text-xs truncate flex-1">
                              {variant.title} - ${variant.price}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-muted-foreground">
            Page {currentPage} of {totalPages}
          </p>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
            >
              <ChevronLeft className="h-4 w-4" />
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() =>
                setCurrentPage(Math.min(totalPages, currentPage + 1))
              }
              disabled={currentPage === totalPages}
            >
              Next
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}

      {products.length === 0 && !isLoading && (
        <Card className="p-12">
          <div className="text-center text-muted-foreground">
            <Package className="mx-auto h-12 w-12 mb-4 opacity-50" />
            <p>No products found matching your criteria.</p>
            <p className="text-sm mt-2">
              Try adjusting your search or filters.
            </p>
          </div>
        </Card>
      )}
    </div>
  );
}
