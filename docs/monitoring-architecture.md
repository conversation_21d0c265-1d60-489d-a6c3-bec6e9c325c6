# Monitoring & Observability Architecture

## Overview

This document describes the comprehensive monitoring and observability system implemented for the E-commerce Platform. The system provides real-time insights into application performance, queue health, sync operations, and business metrics using Prometheus, Grafana, and custom instrumentation.

## Architecture

### Components

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Application   │    │   Prometheus    │    │     Grafana     │
│                 │    │                 │    │                 │
│ • API Server    │───▶│ • Metrics       │───▶│ • Dashboards    │
│ • Workers       │    │   Collection    │    │ • Alerts        │
│ • Background    │    │ • Time Series   │    │ • Visualizations │
│   Tasks         │    │   Storage       │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 ▼
                    ┌─────────────────┐
                    │   Alerting      │
                    │   (Optional)    │
                    └─────────────────┘
```

### Data Flow

1. **Instrumentation**: Application code emits metrics using Prometheus client libraries
2. **Collection**: Prometheus scrapes metrics from application endpoints (`/metrics`)
3. **Storage**: Metrics are stored in Prometheus time-series database
4. **Visualization**: Graf<PERSON> queries Prometheus and displays dashboards
5. **Alerting**: AlertManager can trigger notifications based on metric thresholds

## Metrics Collection

### Application Metrics

#### 1. Media Processing Metrics

**Location**: `backend/src/core/metrics.py`

```python
# Success rates
media_duration_seconds{media_type="video", status="success"}
media_push_duration_seconds{platform="shopify", status="success"}

# Performance tracking
histogram_quantile(0.95, rate(media_duration_seconds_bucket[5m]))
```

**Collection Points**:
- `backend/src/core/services/queue_service.py` - Task enqueue
- Worker task completion callbacks

#### 2. Sync Operation Metrics

**Location**: `backend/src/core/metrics.py`

```python
# Sync operations
sync_triggered_total{trigger_type="webhook", entity_type="products"}
sync_completed_total{status="success"}

# Airbyte integration
airbyte_sync_jobs_created_total{store_domain="store.com"}
airbyte_sync_jobs_completed_total{status="success"}
```

**Collection Points**:
- `backend/src/servers/worker/tasks/webhook_tasks.py` - Webhook-triggered syncs
- `backend/src/modules/stores/router.py` - Direct API sync calls
- `backend/src/core/services/airbyte_service.py` - Airbyte job tracking

#### 3. Queue & Worker Metrics

**Location**: `backend/src/core/metrics.py`

```python
# Queue health
celery_active_tasks{queue="media-generation", worker="worker-1"}
celery_queue_length{queue="media-push"}

# Worker status
celery_worker_status{worker="worker-1", status="active"}
celery_worker_pool_size{worker="worker-1", pool_type="processes"}
```

**Collection Points**:
- `backend/src/core/services/queue_service.py` - Queue stats collection
- Automatic collection via `get_queue_stats()` method

#### 4. Analytics Metrics

**Location**: `backend/src/core/metrics.py`

```python
# Event processing
analytics_processing_duration_seconds{status="success"}
analytics_processing_failures_total{failure_reason="timeout"}
```

**Collection Points**:
- `backend/src/core/services/queue_service.py` - Task enqueue
- Analytics processing workers

### Custom Metrics Implementation

#### Metric Types

| Type | Use Case | Example |
|------|----------|---------|
| **Counter** | Cumulative values | `sync_triggered_total` |
| **Gauge** | Point-in-time values | `celery_active_tasks` |
| **Histogram** | Duration distributions | `media_generation_duration_seconds` |

#### Labels

```python
# Consistent labeling strategy
{
    "queue": "media-generation",
    "task_name": "generate_media",
    "status": "success",
    "worker": "worker-1",
    "store_domain": "store.com"
}
```

## Monitoring Stack Setup

### Docker Compose Configuration

**File**: `docker-compose.yml`

```yaml
prometheus:
  image: prom/prometheus:latest
  ports:
    - "9090:9090"
  volumes:
    - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
  command:
    - '--config.file=/etc/prometheus/prometheus.yml'

grafana:
  image: grafana/grafana:latest
  ports:
    - "3001:3000"
  environment:
    - GF_SECURITY_ADMIN_PASSWORD=admin
  volumes:
    - grafana_data:/var/lib/grafana
    - ./monitoring/grafana-dashboard.json:/etc/grafana/provisioning/dashboards/dashboard.json

mailhog:
  image: mailhog/mailhog:latest
  ports:
    - "8025:8025"
    - "1025:1025"
```

### Prometheus Configuration

**File**: `monitoring/prometheus.yml`

```yaml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: "api"
    static_configs:
      - targets: ["api:8123"]
    metrics_path: "/metrics"
    scrape_interval: 30s

  - job_name: "worker"
    static_configs:
      - targets: ["worker:9091"]
    metrics_path: "/metrics"
    scrape_interval: 30s
```

### Nginx Configuration

**File**: `nginx.conf`

```nginx
# Metrics endpoint access control
location /metrics {
    proxy_pass http://backend;
    allow 127.0.0.1;
    allow 10.0.0.0/8;
    deny all;
}
```

## Dashboard Structure

### Main Dashboard Panels

#### 1. Media Processing
- **Success Rate**: `rate(media_duration_seconds_count{status="success"}[5m]) / rate(media_duration_seconds_count[5m]) * 100`
- **Duration (95th percentile)**: `histogram_quantile(0.95, rate(media_duration_seconds_bucket[5m]))`
- **Request Rate**: `rate(media_duration_seconds_count[5m])`

#### 2. Sync Operations
- **Sync Trigger Rate**: `rate(sync_triggered_total[5m])`
- **Sync Success Rate**: `rate(sync_completed_total{status="success"}[5m]) / rate(sync_triggered_total[5m]) * 100`
- **Webhook Triggers**: `rate(webhook_sync_triggers_total[5m])`

#### 3. Queue Health
- **Queue Length**: `celery_queue_length`
- **Active Tasks**: `celery_active_tasks`
- **Worker Status**: `celery_worker_status`
- **Task Failure Rate**: `rate(celery_task_failures_total[5m]) / rate(celery_task_rate[5m]) * 100`

#### 4. System Health
- **Service Status**: `up{job="api"}`
- **Worker Pool Size**: `celery_worker_pool_size`

## Alerting Rules

### Example Alert Rules

**File**: `monitoring/alert-rules.yml`

```yaml
groups:
  - name: application_alerts
    rules:
      - alert: HighQueueLength
        expr: celery_queue_length > 100
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Queue length is too high"
          description: "Queue {{ $labels.queue }} has {{ $value }} pending tasks"

      - alert: MediaFailureRate
        expr: rate(media_failures_total[5m]) / rate(media_duration_seconds_count[5m]) > 0.1
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "High media generation failure rate"
          description: "{{ $value }}% of media generation tasks are failing"
```

## Usage Guide

### Starting the Monitoring Stack

```bash
# Start all monitoring services
docker-compose up -d prometheus grafana mailhog nginx

# View services
docker-compose ps
```

### Accessing Services

| Service | URL | Credentials |
|---------|-----|-------------|
| **Grafana** | http://localhost:3001 | admin/admin |
| **Prometheus** | http://localhost:9090 | - |
| **Mailhog** | http://localhost:8025 | - |
| **API Metrics** | http://localhost:8123/metrics | - |

### Dashboard Setup

1. **Import Dashboard**:
   - Go to Grafana → Dashboards → Import
   - Upload `monitoring/grafana-dashboard.json`

2. **Configure Data Source**:
   - Go to Configuration → Data Sources → Add
   - Select Prometheus, URL: `http://prometheus:9090`

### Monitoring API Endpoints

#### Queue Statistics
```bash
curl http://localhost:8123/api/v1/queue/stats
```

#### Application Metrics
```bash
curl http://localhost:8123/metrics
```

#### Prometheus Queries
```bash
# Active tasks
curl "http://localhost:9090/api/v1/query?query=celery_active_tasks"

# Queue length
curl "http://localhost:9090/api/v1/query?query=celery_queue_length"
```

## Troubleshooting

### Common Issues

#### 1. Metrics Not Appearing
```bash
# Check if application is exposing metrics
curl http://localhost:8123/metrics

# Check Prometheus targets
curl http://localhost:9090/api/v1/targets
```

#### 2. Dashboard Shows No Data
```bash
# Verify Prometheus is scraping correctly
curl "http://localhost:9090/api/v1/query?query=up"

# Check Grafana data source configuration
# Go to Configuration → Data Sources → Prometheus
```

#### 3. High Memory Usage
```bash
# Adjust Prometheus retention
# Edit monitoring/prometheus.yml
storage:
  tsdb:
    retention.time: 30d
```

### Performance Tuning

#### Prometheus Configuration
```yaml
global:
  scrape_interval: 15s        # How often to scrape
  evaluation_interval: 15s    # How often to evaluate rules

# Reduce memory usage
storage:
  tsdb:
    retention.time: 7d        # Keep data for 7 days
    retention.size: 1GB       # Limit storage size
```

#### Application Metrics
```python
# Use appropriate metric types
# Counter for cumulative values
# Gauge for point-in-time values
# Histogram for distributions
```

## Best Practices

### 1. Metric Naming
- Use consistent prefixes: `media_`, `sync_`, `celery_`
- Include units in metric names when applicable
- Use snake_case for metric names

### 2. Label Usage
- Keep label cardinality low
- Use meaningful label values
- Avoid high-cardinality labels (e.g., user IDs, timestamps)

### 3. Dashboard Organization
- Group related metrics together
- Use consistent color schemes
- Set appropriate refresh intervals

### 4. Alert Configuration
- Set meaningful thresholds
- Include runbook URLs in alerts
- Use appropriate severity levels

## Security Considerations

### 1. Metrics Endpoint Protection
```nginx
location /metrics {
    allow 127.0.0.1;
    allow 10.0.0.0/8;
    allow 172.16.0.0/12;
    deny all;
}
```

### 2. Grafana Security
- Change default admin password
- Enable authentication
- Configure HTTPS

### 3. Network Security
- Run monitoring stack in isolated network
- Use internal load balancers
- Implement proper firewall rules

## Future Enhancements

### 1. Advanced Alerting
- Integration with Slack, PagerDuty
- Alert correlation and grouping
- Auto-remediation workflows

### 2. Custom Metrics
- Business KPI tracking
- User experience metrics
- Performance profiling

### 3. Distributed Tracing
- Integration with Jaeger/OpenTelemetry
- End-to-end request tracing
- Service mesh integration

### 4. Log Aggregation
- Centralized logging with ELK stack
- Log-based metrics
- Anomaly detection

---

This monitoring architecture provides comprehensive observability for the E-commerce Platform, enabling proactive issue detection, performance optimization, and reliable operation.