import { api } from "./api";

export interface Order {
  id: number;
  customer: string;
  status: string;
  total: number;
  items: number;
  created_at: string;
}

export const orderService = {
  async getOrders(): Promise<Order[]> {
    // TODO: Replace with actual backend endpoint when available
    // const response = await api.get("/api/orders/");
    // return response.data;

    // For now, return empty array - will be populated when backend is ready
    return [];
  },
};