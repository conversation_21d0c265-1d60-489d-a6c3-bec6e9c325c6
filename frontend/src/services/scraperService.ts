import { api } from './api';

export interface ScrapedDocument {
  id: string;
  url: string;
  domain: string;
  title: string;
  status: "pending" | "scraping" | "completed" | "failed";
  progress: number;
  productCount: number;
  collectionCount: number;
  createdAt: string;
  updatedAt: string;
  error?: string;
}

export interface ScrapingJob {
  id: string;
  url: string;
  status: "queued" | "running" | "completed" | "failed";
  progress: number;
  startedAt?: string;
  completedAt?: string;
  error?: string;
  metadata?: {
    domain: string;
    total_pages?: number;
    current_page?: number;
    products_found?: number;
    collections_found?: number;
  };
}

export interface ScrapedProduct {
  id: string;
  product_id: string;
  title: string;
  handle: string;
  description?: string;
  vendor?: string;
  product_type?: string;
  tags?: string[];
  images: Array<{
    id: string;
    src_url: string;
    alt?: string;
    position?: number;
  }>;
  variants: Array<{
    id: string;
    title: string;
    price: string;
    compare_at_price?: string;
    sku?: string;
    inventory_quantity?: number;
  }>;
  collections: Array<{
    id: string;
    name: string;
    slug: string;
  }>;
  url: string;
  domain: string;
  scraped_at: string;
}

export interface ScrapedCollection {
  id: string;
  slug: string;
  title: string;
  description?: string;
  image_url?: string;
  product_count: number;
  domain: string;
  scraped_at: string;
}

export interface ScraperStats {
  totalDocuments: number;
  totalProducts: number;
  totalDomains: number;
  activeJobs: number;
  domains: Array<{
    domain: string;
    count: number;
    last_scraped: string;
  }>;
}

export class ScraperService {
  /**
   * Get scraper statistics
   */
  async getStats(): Promise<ScraperStats> {
    const response = await api.get('/scraper/stats');
    return response.data;
  }

  /**
   * Get scraped documents
   */
  async getDocuments(params?: {
    limit?: number;
    offset?: number;
    domain?: string;
    status?: string;
    search?: string;
  }): Promise<{
    items: ScrapedDocument[];
    total: number;
    has_more: boolean;
  }> {
    const searchParams = new URLSearchParams();
    
    if (params?.limit) searchParams.set('limit', params.limit.toString());
    if (params?.offset) searchParams.set('offset', params.offset.toString());
    if (params?.domain) searchParams.set('domain', params.domain);
    if (params?.status) searchParams.set('status', params.status);
    if (params?.search) searchParams.set('search', params.search);

    const response = await api.get(`/scraper/documents?${searchParams.toString()}`);
    return response.data;
  }

  /**
   * Get active scraping jobs
   */
  async getActiveJobs(): Promise<{
    items: ScrapingJob[];
  }> {
    const response = await api.get('/scraper/jobs');
    return response.data;
  }

  /**
   * Start scraping a URL
   */
  async startScraping(url: string, options?: {
    deep_scrape?: boolean;
    max_pages?: number;
    include_variants?: boolean;
    include_images?: boolean;
  }): Promise<{
    job_id: string;
    message: string;
  }> {
    const response = await api.post('/scraper/scrape', {
      url,
      ...options,
    });
    return response.data;
  }

  /**
   * Get job status
   */
  async getJobStatus(jobId: string): Promise<ScrapingJob> {
    const response = await api.get(`/scraper/jobs/${jobId}`);
    return response.data;
  }

  /**
   * Cancel a scraping job
   */
  async cancelJob(jobId: string): Promise<void> {
    await api.post(`/scraper/jobs/${jobId}/cancel`);
  }

  /**
   * Delete a scraped document
   */
  async deleteDocument(documentId: string): Promise<void> {
    await api.delete(`/scraper/documents/${documentId}`);
  }

  /**
   * Get scraped products
   */
  async getScrapedProducts(params?: {
    limit?: number;
    cursor?: string;
    domain?: string;
    collections?: string[];
    search?: string;
  }): Promise<{
    items: ScrapedProduct[];
    next_cursor?: string;
    total_count?: number;
  }> {
    const searchParams = new URLSearchParams();
    
    if (params?.limit) searchParams.set('limit', params.limit.toString());
    if (params?.cursor) searchParams.set('cursor', params.cursor);
    if (params?.domain) searchParams.set('domain', params.domain);
    if (params?.collections?.length) searchParams.set('collections', params.collections.join(','));
    if (params?.search) searchParams.set('search', params.search);

    const response = await api.get(`/scraper/products?${searchParams.toString()}`);
    return response.data;
  }

  /**
   * Get scraped collections
   */
  async getScrapedCollections(params?: {
    limit?: number;
    offset?: number;
    domain?: string;
  }): Promise<{
    items: ScrapedCollection[];
    total: number;
  }> {
    const searchParams = new URLSearchParams();
    
    if (params?.limit) searchParams.set('limit', params.limit.toString());
    if (params?.offset) searchParams.set('offset', params.offset.toString());
    if (params?.domain) searchParams.set('domain', params.domain);

    const response = await api.get(`/scraper/collections?${searchParams.toString()}`);
    return response.data;
  }

  /**
   * Get product count for scraped data
   */
  async getScrapedProductCount(params?: {
    domain?: string;
    collections?: string[];
  }): Promise<{
    count: number;
    meta?: {
      total_products: number;
      domains: Array<{ domain: string; count: number }>;
    };
  }> {
    const searchParams = new URLSearchParams();
    if (params?.domain) searchParams.set('domain', params.domain);
    if (params?.collections?.length) searchParams.set('collections', params.collections.join(','));

    const response = await api.get(`/scraper/products/count?${searchParams.toString()}`);
    return response.data;
  }

  /**
   * Import scraped data to main database
   */
  async importScrapedData(params: {
    document_id: string;
    shop_id?: string;
    collection_mapping?: Record<string, string>;
  }): Promise<{
    imported_products: number;
    imported_collections: number;
    skipped: number;
    errors: string[];
  }> {
    const response = await api.post('/scraper/import', params);
    return response.data;
  }

  /**
   * Validate URL before scraping
   */
  async validateUrl(url: string): Promise<{
    valid: boolean;
    domain: string;
    platform?: string;
    estimated_products?: number;
    warnings?: string[];
    errors?: string[];
  }> {
    const response = await api.post('/scraper/validate', { url });
    return response.data;
  }

  /**
   * Get supported platforms
   */
  async getSupportedPlatforms(): Promise<Array<{
    name: string;
    domains: string[];
    features: string[];
    limitations?: string[];
  }>> {
    const response = await api.get('/scraper/platforms');
    return response.data.items || [];
  }
}

export const scraperService = new ScraperService();
