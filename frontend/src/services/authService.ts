import { api } from './api';

interface LoginResponse {
  access_token: string;
  token_type: string;
}

interface User {
  id: number;
  email: string;
  full_name: string;
  is_active: boolean;
}

export const authService = {
  async login(email: string, password: string): Promise<LoginResponse> {
    const response = await api.post('/api/auth/login', { email, password });
    return response.data;
  },

  async register(email: string, password: string, full_name: string): Promise<User> {
    const response = await api.post('/api/auth/register', { 
      email, 
      password, 
      full_name 
    });
    return response.data;
  },

  async getCurrentUser(): Promise<User> {
    const response = await api.get('/api/auth/me');
    return response.data;
  },

  async forgotPassword(email: string): Promise<{ message: string }> {
    const response = await api.post('/api/auth/forgot-password', { email });
    return response.data;
  },

  async resetPassword(token: string, newPassword: string): Promise<{ message: string }> {
    const response = await api.post('/api/auth/reset-password', {
      token,
      new_password: newPassword
    });
    return response.data;
  },
};
