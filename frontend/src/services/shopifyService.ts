/**
 * Shopify Integration Service - Frontend API client
 */

import { api } from "./api";

export interface ShopifyProduct {
  id: string;
  title: string;
  description?: string;
  handle?: string;
  product_type?: string;
  vendor?: string;
  status?: string;
  tags?: string;
  featured_image_url?: string;
  images?: string[];
  created_at?: string;
  updated_at?: string;
}

export interface ShopifyStore {
  id: number;
  shop_domain: string;
  shop_name?: string;
  is_active: boolean;
  last_sync?: string;
}

export interface ShopifyOAuthResponse {
  install_url: string;
  message: string;
}

export interface ShopifyCallbackResponse {
  message: string;
  store: {
    id: number;
    shop_domain: string;
    shop_name: string;
  };
}

/**
 * Shopify Integration API Service
 */
export class ShopifyService {
  /**
   * Get Shopify OAuth URL for app installation
   */
  static async getInstallUrl(
    shopDomain: string
  ): Promise<ShopifyOAuthResponse> {
    const response = await api.get(
      `/api/plugins/shopify/install?shop=${encodeURIComponent(shopDomain)}`
    );
    return response.data;
  }

  /**
   * Handle OAuth callback (called by backend redirect)
   */
  static async handleOAuthCallback(
    code: string,
    shop: string,
    state?: string
  ): Promise<ShopifyCallbackResponse> {
    const response = await api.get(
      `/api/plugins/shopify/oauth/callback?code=${encodeURIComponent(code)}&shop=${encodeURIComponent(shop)}${state ? `&state=${encodeURIComponent(state)}` : ''}`
    );
    return response.data;
  }

  /**
   * Get required Shopify scopes
   */
  static async getRequiredScopes(): Promise<{
    scopes: string[];
    description: string;
  }> {
    const response = await api.get("/api/plugins/shopify/scopes");
    return response.data;
  }



}

export default ShopifyService;
