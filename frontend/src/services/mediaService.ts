import { api } from "./api";

export interface MediaJob {
  id: string;
  status: string;
  productCount: number;
  variantCount: number;
  createdAt: string;
  completedAt?: string;
  progress?: number;
}

export interface UsageStats {
  videosGenerated: number;
  monthlyLimit: number;
  storageUsed: number;
  storageLimit: number;
}

export interface Asset {
  id: string;
  productId: string;
  url: string;
  type: "image" | "video";
  filename: string;
  displayName: string;
  fileUrl?: string;
  previewUrl?: string;
  prompt?: string;
}

export interface Product {
  id: string;
  title: string;
  variants: any[];
  collections: Array<{ id: string; name: string; color: string }>;
  assets: Asset[];
}

export interface GenerationSettings {
  mode: "image" | "video";
  model: string;
  aspectRatio: string;
  quality: string;
  size?: string;
  duration?: number;
  fps?: number;
  resolution?: string;
  motionStrength?: number;
  guidance?: number;
  steps?: number;
  strength?: number;
  seed?: number;
  upscale?: boolean;
  safety?: boolean;
  audio?: boolean;
}

export interface GenerationRequest {
  mode: "image" | "video";
  model: string;
  items: Array<{
    product_id: string;
    variant_id?: string;
    prompt: string;
    params: GenerationSettings;
  }>;
  settings: GenerationSettings;
}

export interface GenerationBatch {
  batch_id: string;
  workspace_id: string;
  mode: string;
  status: string;
  requested_count: number;
  completed_count: number;
  failed_count: number;
  created_at: string;
  updated_at: string;
}

export interface BatchStatus {
  batch_id: string;
  total: number;
  completed: number;
  failed: number;
  status: string;
  requests?: Array<{
    id: string;
    product_id: string;
    status: string;
    result_url?: string;
    error?: string;
  }>;
}

export class MediaService {
  async getRecentJobs(): Promise<MediaJob[]> {
    try {
      const response = await api.get("/api/media/batches?limit=5");
      return (
        response.data?.map((batch: any) => ({
          id: batch.batch_id,
          status: batch.status,
          productCount: batch.requested_count,
          variantCount: 0, // Not tracked in new schema
          createdAt: batch.created_at,
          completedAt: batch.updated_at,
          progress: (batch.completed_count / batch.requested_count) * 100,
        })) || []
      );
    } catch (err) {
      console.error("Failed to fetch recent jobs:", err);
      return [];
    }
  }

  async getUsageStats(): Promise<UsageStats> {
    try {
      const response = await api.get("/api/media/usage");
      return {
        videosGenerated: response.data.videos_generated || 0,
        monthlyLimit: response.data.monthly_limit || 500,
        storageUsed: response.data.storage_used || 0,
        storageLimit: response.data.storage_limit || 10,
      };
    } catch (err) {
      console.error("Failed to fetch usage stats:", err);
      // Return default stats on error
      return {
        videosGenerated: 0,
        monthlyLimit: 500,
        storageUsed: 0,
        storageLimit: 10,
      };
    }
  }

  /**
   * Get products for media studio with assets
   */
  async getProducts(params?: {
    limit?: number;
    cursor?: string;
    shop_id?: string;
    collections?: string[];
    search?: string;
  }): Promise<{
    items: Product[];
    next_cursor?: string;
    total_count?: number;
  }> {
    const searchParams = new URLSearchParams();
    
    if (params?.limit) searchParams.set('limit', params.limit.toString());
    if (params?.cursor) searchParams.set('cursor', params.cursor);
    if (params?.shop_id) searchParams.set('shop_id', params.shop_id);
    if (params?.collections?.length) searchParams.set('collections', params.collections.join(','));
    if (params?.search) searchParams.set('search', params.search);

    const response = await api.get(`/api/products?${searchParams.toString()}`);
    
    // Transform products to include assets
    const transformedItems = response.data.items?.map((item: any) => ({
      id: item.id,
      title: item.title || item.name,
      variants: item.variants || [],
      collections: item.collections || [],
      assets: (item.images || []).map((img: any, index: number) => ({
        id: `asset_${item.id}_${index}`,
        productId: item.id,
        url: img.src || img.url,
        type: "image" as const,
        filename: img.filename || `image_${index}.jpg`,
        displayName: `${item.title} - Image ${index + 1}`,
      })),
    })) || [];

    return {
      items: transformedItems,
      next_cursor: response.data.next_cursor,
      total_count: response.data.total_count,
    };
  }

  /**
   * Get generated assets for products (delegated to assetService)
   */
  async getGeneratedAssets(params?: {
    limit?: number;
    cursor?: string;
    product_ids?: string[];
  }): Promise<{
    items: Asset[];
    next_cursor?: string;
  }> {
    // Import assetService dynamically to avoid circular dependencies
    const { assetService } = await import('./assetService');

    if (params?.product_ids?.length) {
      const assets = await assetService.getAssetsForProducts(params.product_ids, { limit: params.limit });
      return {
        items: assets,
        next_cursor: undefined, // Asset service doesn't use cursor pagination
      };
    } else {
      const response = await assetService.getAssets({ limit: params?.limit });
      return {
        items: response.items.map((item: any) => ({
          id: item.id,
          productId: item.data?.product_id || '',
          url: item.src,
          type: item.type as "image" | "video",
          filename: item.src?.split("/").pop() || "asset.jpg",
          displayName: item.alt,
          fileUrl: item.src,
          previewUrl: item.data?.preview_uri,
          prompt: item.data?.prompt,
        })),
        next_cursor: undefined,
      };
    }
  }

  /**
   * Start a generation batch
   */
  async startGeneration(request: {
    mode: "image" | "video";
    model: string;
    settings: any;
    items: Array<{
      productId: string;
      prompt: string;
      referenceImageUrls: string[];
      storeId?: number; // Add storeId to the item interface
    }>;
  }): Promise<{
    batch_id: string;
    request_ids: string[];
  }> {
    // Get shop_id from the first product (all products should belong to the same store)
    const firstItem = request.items[0];
    if (!firstItem || !firstItem.storeId) {
      throw new Error('Store ID not found in product data. Please ensure products are properly loaded.');
    }

    const shopId = firstItem.storeId;

    // Include the required backend fields while preserving the original payload
    const enhancedRequest = {
      ...request,
      media_type: request.mode,
      shop_id: shopId,
      product_ids: request.items.map(item => item.productId),
    };

    const response = await api.post('/api/media/generate', enhancedRequest);
    return response.data;
  }

  /**
   * Get batch status
   */
  async getBatchStatus(batchId: string): Promise<BatchStatus> {
    const response = await api.get(`/api/media/batches/${batchId}`);
    return response.data;
  }

  /**
   * Get batch details
   */
  async getBatchDetails(batchId: string): Promise<GenerationBatch> {
    const response = await api.get(`/api/media/batches/${batchId}`);
    return response.data;
  }

  /**
   * List all batches
   */
  async getBatches(params?: {
    limit?: number;
    cursor?: string;
    status?: string;
  }): Promise<{
    items: GenerationBatch[];
    next_cursor?: string;
  }> {
    const searchParams = new URLSearchParams();
    
    if (params?.limit) searchParams.set('limit', params.limit.toString());
    if (params?.cursor) searchParams.set('cursor', params.cursor);
    if (params?.status) searchParams.set('status', params.status);

    const response = await api.get(`/api/media/batches?${searchParams.toString()}`);
    return response.data;
  }

  /**
   * Cancel a job
   */
  async cancelJob(jobId: string): Promise<void> {
    await api.delete(`/api/media/jobs/${jobId}`);
  }

  /**
   * Get collections for filtering
   */
  async getCollections(params?: {
    shop_id?: string;
  }): Promise<Array<{
    id: string;
    name: string;
    color: string;
    product_count?: number;
  }>> {
    const searchParams = new URLSearchParams();
    if (params?.shop_id) searchParams.set('shop_id', params.shop_id);

    const response = await api.get(`/api/products/collections?${searchParams.toString()}`);
    return response.data.items || [];
  }

  /**
   * Get product count
   */
  async getProductCount(params?: {
    shop_id?: string;
    collections?: string[];
  }): Promise<{ count: number }> {
    const searchParams = new URLSearchParams();
    if (params?.shop_id) searchParams.set('shop_id', params.shop_id);
    if (params?.collections?.length) searchParams.set('collections', params.collections.join(','));

    const response = await api.get(`/api/products/count?${searchParams.toString()}`);
    return response.data;
  }
}

export const mediaService = new MediaService();