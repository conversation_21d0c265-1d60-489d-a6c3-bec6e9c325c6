import { api } from "./api";

export interface User {
  id: number;
  name: string;
  email: string;
  phone?: string;
  orders: number;
  status: string;
  created_at: string;
}

export const userService = {
  async getUsers(): Promise<User[]> {
    // TODO: Replace with actual backend endpoint when available
    // const response = await api.get("/api/admin/users/");
    // return response.data;

    // For now, return empty array - will be populated when backend is ready
    return [];
  },

  async getCurrentUser(): Promise<User> {
    const response = await api.get("/api/auth/me");
    return response.data;
  },
};