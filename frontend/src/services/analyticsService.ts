import { api } from "./api";

export interface DashboardMetrics {
  total_products: number;
  total_orders: number;
  total_users: number;
  total_revenue: number;
  recent_orders: Array<{
    id: number;
    customer: string;
    total: number;
    status: string;
    created_at: string;
  }>;
  top_products: Array<{
    name: string;
    category: string;
    sold: number;
    revenue: number;
  }>;
}

export const analyticsService = {
  async getDashboardMetrics(): Promise<DashboardMetrics> {
    const response = await api.get("/api/analytics/dashboard");
    return response.data;
  },
};