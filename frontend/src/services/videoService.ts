/**
 * Video Generation Service - ProductVideo API client
 */

import { api } from "./api";

// Additional types for the new components
export interface VideoGenerationRequest {
  productIds: string[];
  templateId: string;
  voiceId: string;
  aspectRatio: string;
  ctaText: string;
  customScript?: string;
}

// Types for ProductVideo API
export interface GenerateVideoRequest {
  shop_id: number;
  product_ids: string[];
  template_id?: string;
  voice_id?: string;
  aspect_ratio?: string;
  locale?: string;
}

export interface VideoVariant {
  variant_id: number;
  variant_name: string;
  status: string;
  video_url?: string;
  thumbnail_url?: string;
  duration?: number;
}

export interface VideoJob {
  job_id: number;
  status: string;
  progress: number;
  variants: VideoVariant[];
}

export interface PushToShopifyRequest {
  shop_id: number;
  product_id: string;
  variant_id: number;
  publish_targets?: string[];
  publish_options?: {
    alt_text?: string;
    position?: number;
  };
}

export interface Template {
  id: string;
  name: string;
  description?: string;
  preview_url?: string;
  category?: string;
}

export interface Voice {
  id: string;
  name: string;
  gender?: string;
  accent?: string;
  language: string;
  sample_url?: string;
}

export interface AnalyticsMetrics {
  views: number;
  plays: number;
  completion_rate: number;
  avg_watch_time: number;
  ctr: number;
  conversions: number;
  conversion_lift?: number;
}

export interface ProductAnalytics {
  product_id: string;
  variant_id?: number;
  metrics: AnalyticsMetrics;
  period: {
    from: string;
    to: string;
  };
}

/**
 * Video Generation API Service
 */
export class VideoService {
  /**
   * Generate videos for selected products
   */
  static async generateVideos(request: GenerateVideoRequest) {
    const response = await api.post("/api/video/generate", request);
    return response.data;
  }

  /**
   * Get job status and variants
   */
  static async getJobStatus(jobId: number): Promise<VideoJob> {
    const response = await api.get(`/api/video/jobs/${jobId}`);
    return response.data;
  }

  /**
   * Regenerate a specific video variant
   */
  static async regenerateVariant(
    jobId: number,
    variantId: number,
    overrideParams?: any
  ) {
    const response = await api.post("/api/video/regenerate", {
      job_id: jobId,
      variant_id: variantId,
      override_params: overrideParams,
    });
    return response.data;
  }

  /**
   * Push video variant to Shopify
   */
  static async pushToShopify(request: PushToShopifyRequest) {
    const response = await api.post("/api/video/push", request);
    return response.data;
  }

  /**
   * Get available video templates
   */
  static async getTemplates(): Promise<{ templates: Template[] }> {
    const response = await api.get("/api/video/templates");
    return response.data;
  }

  /**
   * Get available voices
   */
  static async getVoices(): Promise<{ voices: Voice[] }> {
    const response = await api.get("/api/video/voices");
    return response.data;
  }

  /**
   * Get product analytics
   */
  static async getProductAnalytics(
    productId: string,
    fromDate?: string,
    toDate?: string,
    variantId?: number
  ): Promise<ProductAnalytics> {
    const params = new URLSearchParams();
    if (fromDate) params.append("from_date", fromDate);
    if (toDate) params.append("to_date", toDate);
    if (variantId) params.append("variant_id", variantId.toString());

    const response = await api.get(
      `/api/analytics/product/${productId}?${params}`
    );
    return response.data;
  }

  /**
   * Track analytics event
   */
  static async trackEvent(eventData: {
    variantId: number;
    eventType: string;
    userSessionId?: string;
    viewport?: string;
    device?: string;
  }) {
    const response = await api.post("/api/analytics/events", eventData);
    return response.data;
  }

  /**
   * Get dashboard metrics
   */
  static async getDashboardMetrics(days: number = 30) {
    const response = await api.get(`/api/analytics/dashboard?days=${days}`);
    return response.data;
  }

  /**
   * Poll job status until completion
   */
  static async pollJobStatus(
    jobId: number,
    onProgress?: (job: VideoJob) => void,
    maxAttempts: number = 60,
    intervalMs: number = 5000
  ): Promise<VideoJob> {
    let attempts = 0;

    return new Promise((resolve, reject) => {
      const poll = async () => {
        try {
          const job = await this.getJobStatus(jobId);

          if (onProgress) {
            onProgress(job);
          }

          if (job.status === "completed") {
            resolve(job);
            return;
          }

          if (job.status === "failed") {
            reject(new Error("Video generation failed"));
            return;
          }

          attempts++;
          if (attempts >= maxAttempts) {
            reject(new Error("Polling timeout"));
            return;
          }

          setTimeout(poll, intervalMs);
        } catch (error) {
          reject(error);
        }
      };

      poll();
    });
  }

  /**
   * Generate videos with new interface
   */
  static async generateVideos(request: VideoGenerationRequest) {
    const response = await api.post("/api/video-generation/generate", request);
    return response.data;
  }

  /**
   * Get video variants
   */
  static async getVariants() {
    const response = await api.get("/api/video-generation/variants");
    return response.data;
  }

  /**
   * Get variants by IDs
   */
  static async getVariantsByIds(variantIds: string[]) {
    const response = await api.post("/api/video-generation/variants/by-ids", {
      variantIds,
    });
    return response.data;
  }

  /**
   * Toggle favorite status
   */
  static async toggleFavorite(variantId: string) {
    const response = await api.post(
      `/api/video-generation/variants/${variantId}/favorite`
    );
    return response.data;
  }

  /**
   * Regenerate variant
   */
  static async regenerateVariant(variantId: string) {
    const response = await api.post(
      `/api/video-generation/variants/${variantId}/regenerate`
    );
    return response.data;
  }

  /**
   * Track video event
   */
  static async trackEvent(event: {
    variantId: string;
    eventType: string;
    timestamp: string;
    sessionId: string;
  }) {
    const response = await api.post("/api/analytics/events/ingest", {
      event_type: event.eventType,
      video_variant_id: event.variantId,
      session_id: event.sessionId,
      timestamp: event.timestamp,
      dedup_token: `${event.sessionId}-${event.variantId}-${event.eventType}-${Date.now()}`,
    });
    return response.data;
  }

  /**
   * Push to Shopify with new interface
   */
  static async pushToShopify(params: {
    variantIds: string[];
    altTexts: Record<string, string>;
    mediaPositions: Record<string, number>;
    replaceExisting: boolean;
  }) {
    const response = await api.post(
      "/api/video-generation/push-to-shopify",
      params
    );
    return response.data;
  }

  /**
   * Get push job status
   */
  static async getPushJobStatus(jobId: string) {
    const response = await api.get(
      `/api/video-generation/push-jobs/${jobId}/status`
    );
    return response.data;
  }

  /**
   * Get gallery items
   */
  static async getGallery(params: {
    search?: string;
    sortBy?: string;
    status?: string;
    tag?: string;
    page?: number;
    limit?: number;
  }) {
    const response = await api.get("/api/video-generation/gallery", { params });
    return response.data;
  }

  /**
   * Get gallery tags
   */
  static async getGalleryTags() {
    const response = await api.get("/api/video-generation/gallery/tags");
    return response.data;
  }

  /**
   * Create bulk download
   */
  static async createBulkDownload(variantIds: string[]) {
    const response = await api.post("/api/video-generation/bulk-download", {
      variantIds,
    });
    return response.data.downloadUrl;
  }

  /**
   * Bulk delete variants
   */
  static async bulkDelete(variantIds: string[]) {
    const response = await api.delete("/api/video-generation/variants/bulk", {
      data: { variantIds },
    });
    return response.data;
  }

  /**
   * Get analytics dashboard data
   */
  static async getAnalytics(params: { timeRange: string; productId?: string }) {
    const response = await api.get("/api/analytics/dashboard", { params });
    return response.data;
  }

  /**
   * Get analytics products
   */
  static async getAnalyticsProducts() {
    const response = await api.get("/api/analytics/products");
    return response.data;
  }
}

export default VideoService;
