'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { toast } from 'sonner';

export interface Company {
  id: string;
  name: string;
  description?: string;
  industry?: string;
  phone?: string;
  email?: string;
  website?: string;
  address?: string;
  logo?: string;
  settings?: {
    timezone?: string;
    business_hours?: {
      start: string;
      end: string;
      days: string[];
    };
    default_agent_id?: string;
    knowledge_base_enabled?: boolean;
    booking_enabled?: boolean;
    chat_plugin_enabled?: boolean;
  };
  created_at?: string;
  updated_at?: string;
}

interface CompanyContextType {
  companies: Company[];
  currentCompany: Company | null;
  loading: boolean;
  error: string | null;
  
  // Actions
  fetchCompanies: () => Promise<void>;
  selectCompany: (company: Company) => void;
  createCompany: (companyData: Partial<Company>) => Promise<Company | null>;
  updateCompany: (id: string, companyData: Partial<Company>) => Promise<Company | null>;
  deleteCompany: (id: string) => Promise<boolean>;
  refreshCurrentCompany: () => Promise<void>;
}

const CompanyContext = createContext<CompanyContextType | undefined>(undefined);

interface CompanyProviderProps {
  children: ReactNode;
}

export const CompanyProvider: React.FC<CompanyProviderProps> = ({ children }) => {
  const [companies, setCompanies] = useState<Company[]>([]);
  const [currentCompany, setCurrentCompany] = useState<Company | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Mock data for development
  const mockCompanies: Company[] = [
    {
      id: '1',
      name: 'Elite Home Services',
      description: 'Premium home maintenance and repair services',
      industry: 'Home Services',
      phone: '+****************',
      email: '<EMAIL>',
      website: 'https://elitehomeservices.com',
      address: '123 Main St, Anytown, USA 12345',
      settings: {
        timezone: 'America/New_York',
        business_hours: {
          start: '08:00',
          end: '18:00',
          days: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
        },
        knowledge_base_enabled: true,
        booking_enabled: true,
        chat_plugin_enabled: true
      },
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-15T00:00:00Z'
    },
    {
      id: '2',
      name: 'Quick Fix Solutions',
      description: 'Fast and reliable repair services',
      industry: 'Home Services',
      phone: '+****************',
      email: '<EMAIL>',
      settings: {
        timezone: 'America/Los_Angeles',
        business_hours: {
          start: '07:00',
          end: '19:00',
          days: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday']
        },
        knowledge_base_enabled: true,
        booking_enabled: false,
        chat_plugin_enabled: true
      },
      created_at: '2024-01-10T00:00:00Z',
      updated_at: '2024-01-20T00:00:00Z'
    }
  ];

  const fetchCompanies = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // TODO: Replace with actual API call
      // const response = await fetch('/api/companies');
      // const data = await response.json();
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setCompanies(mockCompanies);
      
      // Set first company as current if none selected
      if (!currentCompany && mockCompanies.length > 0) {
        setCurrentCompany(mockCompanies[0]);
        localStorage.setItem('currentCompanyId', mockCompanies[0].id);
      }
    } catch (err) {
      setError('Failed to fetch companies');
      toast.error('Failed to load companies');
    } finally {
      setLoading(false);
    }
  };

  const selectCompany = (company: Company) => {
    setCurrentCompany(company);
    localStorage.setItem('currentCompanyId', company.id);
    toast.success(`Switched to ${company.name}`);
  };

  const createCompany = async (companyData: Partial<Company>): Promise<Company | null> => {
    try {
      setLoading(true);
      
      // TODO: Replace with actual API call
      // const response = await fetch('/api/companies', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(companyData)
      // });
      // const newCompany = await response.json();
      
      const newCompany: Company = {
        id: Date.now().toString(),
        name: companyData.name || 'New Company',
        description: companyData.description,
        industry: companyData.industry || 'Home Services',
        phone: companyData.phone,
        email: companyData.email,
        website: companyData.website,
        address: companyData.address,
        settings: {
          timezone: 'America/New_York',
          business_hours: {
            start: '08:00',
            end: '18:00',
            days: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday']
          },
          knowledge_base_enabled: true,
          booking_enabled: true,
          chat_plugin_enabled: true,
          ...companyData.settings
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      
      setCompanies(prev => [...prev, newCompany]);
      toast.success('Company created successfully');
      return newCompany;
    } catch (err) {
      setError('Failed to create company');
      toast.error('Failed to create company');
      return null;
    } finally {
      setLoading(false);
    }
  };

  const updateCompany = async (id: string, companyData: Partial<Company>): Promise<Company | null> => {
    try {
      setLoading(true);
      
      // TODO: Replace with actual API call
      const updatedCompany = {
        ...companies.find(c => c.id === id),
        ...companyData,
        updated_at: new Date().toISOString()
      } as Company;
      
      setCompanies(prev => prev.map(c => c.id === id ? updatedCompany : c));
      
      if (currentCompany?.id === id) {
        setCurrentCompany(updatedCompany);
      }
      
      toast.success('Company updated successfully');
      return updatedCompany;
    } catch (err) {
      setError('Failed to update company');
      toast.error('Failed to update company');
      return null;
    } finally {
      setLoading(false);
    }
  };

  const deleteCompany = async (id: string): Promise<boolean> => {
    try {
      setLoading(true);
      
      // TODO: Replace with actual API call
      setCompanies(prev => prev.filter(c => c.id !== id));
      
      if (currentCompany?.id === id) {
        const remainingCompanies = companies.filter(c => c.id !== id);
        setCurrentCompany(remainingCompanies.length > 0 ? remainingCompanies[0] : null);
      }
      
      toast.success('Company deleted successfully');
      return true;
    } catch (err) {
      setError('Failed to delete company');
      toast.error('Failed to delete company');
      return false;
    } finally {
      setLoading(false);
    }
  };

  const refreshCurrentCompany = async () => {
    if (!currentCompany) return;
    
    try {
      // TODO: Replace with actual API call to fetch single company
      const updatedCompany = companies.find(c => c.id === currentCompany.id);
      if (updatedCompany) {
        setCurrentCompany(updatedCompany);
      }
    } catch (err) {
      toast.error('Failed to refresh company data');
    }
  };

  // Initialize companies on mount
  useEffect(() => {
    fetchCompanies();
  }, []);

  // Restore selected company from localStorage
  useEffect(() => {
    const savedCompanyId = localStorage.getItem('currentCompanyId');
    if (savedCompanyId && companies.length > 0) {
      const savedCompany = companies.find(c => c.id === savedCompanyId);
      if (savedCompany) {
        setCurrentCompany(savedCompany);
      }
    }
  }, [companies]);

  const value: CompanyContextType = {
    companies,
    currentCompany,
    loading,
    error,
    fetchCompanies,
    selectCompany,
    createCompany,
    updateCompany,
    deleteCompany,
    refreshCurrentCompany
  };

  return (
    <CompanyContext.Provider value={value}>
      {children}
    </CompanyContext.Provider>
  );
};

export const useCompany = (): CompanyContextType => {
  const context = useContext(CompanyContext);
  if (context === undefined) {
    throw new Error('useCompany must be used within a CompanyProvider');
  }
  return context;
};
