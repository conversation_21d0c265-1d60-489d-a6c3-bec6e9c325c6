'use client';

import { useCallback } from 'react';
import useAnalytics from './useAnalytics';
import { useRouter } from 'next/navigation';

interface UseBookDemoOptions {
  label: string;
  redirectTo?: string;
}

const useBookDemo = (options: UseBookDemoOptions) => {
  const { trackEvent } = useAnalytics();
  const router = useRouter();

  const handleBookDemoClick = useCallback(() => {
    trackEvent({
      action: 'button_click',
      category: 'Demo Call to Action',
      label: options.label,
    });
    
    // Navigate to contact or demo page
    const destination = options.redirectTo || '/contact';
    router.push(destination);
  }, [trackEvent, options.label, options.redirectTo, router]);

  return { handleBookDemoClick };
};

export default useBookDemo;
