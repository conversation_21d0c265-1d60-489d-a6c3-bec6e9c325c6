'use client';

import { useCallback } from 'react';
import useAnalytics from './useAnalytics';

interface FormTrackingOptions {
  formName: string;
  category?: string;
}

const useFormTracking = (options: FormTrackingOptions) => {
  const { trackEvent } = useAnalytics();
  const { formName, category = 'Form Interaction' } = options;

  const trackFormStart = useCallback(() => {
    trackEvent({
      action: 'form_start',
      category,
      label: `${formName} - Form Started`,
    });
  }, [trackEvent, category, formName]);

  const trackFormSubmit = useCallback((success: boolean = true) => {
    trackEvent({
      action: success ? 'form_submit' : 'form_error',
      category,
      label: `${formName} - Form ${success ? 'Submitted' : 'Error'}`,
    });
  }, [trackEvent, category, formName]);

  const trackFieldFocus = useCallback((fieldName: string) => {
    trackEvent({
      action: 'field_focus',
      category,
      label: `${formName} - ${fieldName} Field Focused`,
    });
  }, [trackEvent, category, formName]);

  const trackFieldError = useCallback((fieldName: string, errorMessage: string) => {
    trackEvent({
      action: 'field_error',
      category,
      label: `${formName} - ${fieldName} Error: ${errorMessage}`,
    });
  }, [trackEvent, category, formName]);

  const trackFormAbandonment = useCallback((lastField: string) => {
    trackEvent({
      action: 'form_abandonment',
      category,
      label: `${formName} - Abandoned at ${lastField}`,
    });
  }, [trackEvent, category, formName]);

  return {
    trackFormStart,
    trackFormSubmit,
    trackFieldFocus,
    trackFieldError,
    trackFormAbandonment,
  };
};

export default useFormTracking;
