'use client';

import { useCallback } from 'react';
import useAnalytics from './useAnalytics';

interface NavigationTrackingOptions {
  section?: string;
  category?: string;
}

const useNavigationTracking = (options: NavigationTrackingOptions = {}) => {
  const { trackEvent } = useAnalytics();
  const { section = 'Navigation', category = 'Navigation' } = options;

  const trackNavClick = useCallback((linkName: string, href: string, external: boolean = false) => {
    trackEvent({
      action: external ? 'external_link_click' : 'navigation_click',
      category: `${category} - ${section}`,
      label: `${linkName} (${href})`,
    });
  }, [trackEvent, category, section]);

  const trackMenuOpen = useCallback((menuName: string) => {
    trackEvent({
      action: 'menu_open',
      category: `${category} - ${section}`,
      label: `${menuName} Menu Opened`,
    });
  }, [trackEvent, category, section]);

  const trackMenuClose = useCallback((menuName: string) => {
    trackEvent({
      action: 'menu_close',
      category: `${category} - ${section}`,
      label: `${menuName} Menu Closed`,
    });
  }, [trackEvent, category, section]);

  const trackSearchUsage = useCallback((searchTerm: string, resultsCount: number) => {
    trackEvent({
      action: 'search_usage',
      category: `${category} - Search`,
      label: `Search: "${searchTerm}" (${resultsCount} results)`,
      value: resultsCount,
    });
  }, [trackEvent, category]);

  const trackFilterUsage = useCallback((filterType: string, filterValue: string) => {
    trackEvent({
      action: 'filter_usage',
      category: `${category} - Filter`,
      label: `${filterType}: ${filterValue}`,
    });
  }, [trackEvent, category]);

  const trackSortUsage = useCallback((sortBy: string, sortOrder: string) => {
    trackEvent({
      action: 'sort_usage',
      category: `${category} - Sort`,
      label: `Sort by ${sortBy} (${sortOrder})`,
    });
  }, [trackEvent, category]);

  return {
    trackNavClick,
    trackMenuOpen,
    trackMenuClose,
    trackSearchUsage,
    trackFilterUsage,
    trackSortUsage,
  };
};

export default useNavigationTracking;
