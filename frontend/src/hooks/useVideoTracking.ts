'use client';

import { useCallback } from 'react';
import useAnalytics from './useAnalytics';

interface VideoTrackingOptions {
  videoId: string;
  videoTitle?: string;
  category?: string;
}

const useVideoTracking = (options: VideoTrackingOptions) => {
  const { trackEvent } = useAnalytics();
  const { videoId, videoTitle, category = 'Video Interaction' } = options;

  const trackVideoPlay = useCallback(() => {
    trackEvent({
      action: 'video_play',
      category,
      label: `${videoTitle || videoId} - Video Play`,
      value: 1,
    });
  }, [trackEvent, category, videoId, videoTitle]);

  const trackVideoPause = useCallback((currentTime: number) => {
    trackEvent({
      action: 'video_pause',
      category,
      label: `${videoTitle || videoId} - Video Pause`,
      value: Math.round(currentTime),
    });
  }, [trackEvent, category, videoId, videoTitle]);

  const trackVideoComplete = useCallback((duration: number) => {
    trackEvent({
      action: 'video_complete',
      category,
      label: `${videoTitle || videoId} - Video Complete`,
      value: Math.round(duration),
    });
  }, [trackEvent, category, videoId, videoTitle]);

  const trackVideoProgress = useCallback((percentage: number, currentTime: number) => {
    // Track at 25%, 50%, 75% milestones
    if ([25, 50, 75].includes(percentage)) {
      trackEvent({
        action: 'video_progress',
        category,
        label: `${videoTitle || videoId} - ${percentage}% Complete`,
        value: Math.round(currentTime),
      });
    }
  }, [trackEvent, category, videoId, videoTitle]);

  const trackVideoSeek = useCallback((fromTime: number, toTime: number) => {
    trackEvent({
      action: 'video_seek',
      category,
      label: `${videoTitle || videoId} - Seek from ${Math.round(fromTime)}s to ${Math.round(toTime)}s`,
    });
  }, [trackEvent, category, videoId, videoTitle]);

  const trackVideoError = useCallback((errorMessage: string) => {
    trackEvent({
      action: 'video_error',
      category,
      label: `${videoTitle || videoId} - Error: ${errorMessage}`,
    });
  }, [trackEvent, category, videoId, videoTitle]);

  return {
    trackVideoPlay,
    trackVideoPause,
    trackVideoComplete,
    trackVideoProgress,
    trackVideoSeek,
    trackVideoError,
  };
};

export default useVideoTracking;
