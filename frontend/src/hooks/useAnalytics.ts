'use client';

import { useCallback } from 'react';
import { GtagEvent } from '@/lib/utils';

const useAnalytics = () => {
  const trackEvent = useCallback((event: GtagEvent) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', event.action, {
        event_category: event.category,
        event_label: event.label,
        value: event.value,
        ...event, // Spread any additional properties
      });
    } else {
      console.warn('gtag is not defined. Analytics event not sent:', event);
    }
  }, []);

  const trackPageView = useCallback((path: string, title?: string) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'page_view', {
        page_path: path,
        page_location: window.location.href,
        page_title: title || document.title,
      });
    }
  }, []);

  const trackConversion = useCallback((conversionId: string, value?: number) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'conversion', {
        send_to: conversionId,
        value: value,
        currency: 'USD',
      });
    }
  }, []);

  const trackPurchase = useCallback((transactionId: string, value: number, items: any[]) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'purchase', {
        transaction_id: transactionId,
        value: value,
        currency: 'USD',
        items: items,
      });
    }
  }, []);

  const trackAddToCart = useCallback((itemId: string, itemName: string, value: number) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'add_to_cart', {
        currency: 'USD',
        value: value,
        items: [{
          item_id: itemId,
          item_name: itemName,
          quantity: 1,
          price: value,
        }],
      });
    }
  }, []);

  const trackSearch = useCallback((searchTerm: string) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'search', {
        search_term: searchTerm,
      });
    }
  }, []);

  const trackLogin = useCallback((method: string) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'login', {
        method: method,
      });
    }
  }, []);

  const trackSignUp = useCallback((method: string) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'sign_up', {
        method: method,
      });
    }
  }, []);

  return {
    trackEvent,
    trackPageView,
    trackConversion,
    trackPurchase,
    trackAddToCart,
    trackSearch,
    trackLogin,
    trackSignUp,
  };
};

export default useAnalytics;
