'use client';

import React from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { 
  BookOpen, 
  HelpCircle, 
  MessageCircle, 
  Phone,
  Mail,
  Twitter,
  Github,
  Linkedin
} from 'lucide-react';
import useAnalytics from '@/hooks/useAnalytics';
import useNavigationTracking from '@/hooks/useNavigationTracking';

export const Footer: React.FC = () => {
  const { trackEvent } = useAnalytics();
  const { trackNavClick } = useNavigationTracking({ section: 'Footer' });

  const handleNavLinkClick = (name: string, href: string, external: boolean = false) => {
    trackEvent({
      action: external ? 'external_link_click' : 'navigation_click',
      category: 'Footer Navigation',
      label: `${name} (${href})`,
    });
    trackNavClick(name, href, external);
  };

  const helpLinks = [
    { name: 'Documentation', href: '/docs', icon: BookOpen },
    { name: 'Help Center', href: '/help', icon: HelpCircle },
    { name: 'Support', href: '/support', icon: MessageCircle },
    { name: 'Contact', href: '/contact', icon: Phone },
  ];

  const companyLinks = [
    { name: 'About Us', href: '/about' },
    { name: 'Privacy Policy', href: '/privacy' },
    { name: 'Terms of Service', href: '/terms' },
    { name: 'Cookie Policy', href: '/cookies' },
  ];

  const socialLinks = [
    { name: 'Twitter', href: 'https://twitter.com', icon: Twitter },
    { name: 'GitHub', href: 'https://github.com', icon: Github },
    { name: 'LinkedIn', href: 'https://linkedin.com', icon: Linkedin },
  ];

  return (
    <footer className="border-t bg-background">
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">E-Commerce Platform</h3>
            <p className="text-sm text-muted-foreground">
              Comprehensive e-commerce management platform with AI-powered video generation, 
              product management, and analytics.
            </p>
            <div className="flex space-x-4">
              {socialLinks.map((social) => (
                <Button
                  key={social.name}
                  variant="ghost"
                  size="icon"
                  onClick={() => handleNavLinkClick(social.name, social.href, true)}
                  className="h-8 w-8"
                >
                  <social.icon className="h-4 w-4" />
                </Button>
              ))}
            </div>
          </div>

          {/* Help & Support */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Help & Support</h3>
            <ul className="space-y-2">
              {helpLinks.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    onClick={() => handleNavLinkClick(link.name, link.href)}
                    className="text-sm text-muted-foreground hover:text-foreground transition-colors flex items-center gap-2"
                  >
                    <link.icon className="h-4 w-4" />
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Company */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Company</h3>
            <ul className="space-y-2">
              {companyLinks.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    onClick={() => handleNavLinkClick(link.name, link.href)}
                    className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Info */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Contact</h3>
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Mail className="h-4 w-4" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Phone className="h-4 w-4" />
                <span>+****************</span>
              </div>
            </div>
            <Button
              onClick={() => handleNavLinkClick('Contact Support', '/support')}
              className="w-full"
              size="sm"
            >
              Contact Support
            </Button>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
          <div className="text-sm text-muted-foreground">
            © 2025 E-Commerce Platform. All rights reserved.
          </div>
          <div className="flex items-center space-x-4 mt-4 md:mt-0">
            <Link
              href="/privacy"
              onClick={() => handleNavLinkClick('Privacy Policy', '/privacy')}
              className="text-sm text-muted-foreground hover:text-foreground transition-colors"
            >
              Privacy Policy
            </Link>
            <Link
              href="/terms"
              onClick={() => handleNavLinkClick('Terms of Service', '/terms')}
              className="text-sm text-muted-foreground hover:text-foreground transition-colors"
            >
              Terms of Service
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
};
