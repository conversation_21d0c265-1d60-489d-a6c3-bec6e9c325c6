"use client";

import { useState } from "react";
import { toast } from "sonner";
import { But<PERSON> } from "@/components/ui/button";
import { Loader2 } from "lucide-react";

interface SocialLoginButtonsProps {
  disabled?: boolean;
}

export function SocialLoginButtons({ disabled }: SocialLoginButtonsProps) {
  const [loadingProvider, setLoadingProvider] = useState<string | null>(null);

  const handleSocialLogin = async (provider: string) => {
    setLoadingProvider(provider);
    try {
      // Get OAuth authorization URL
      const response = await fetch("/api/auth/oauth/authorize", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          provider,
          redirect_uri: `${window.location.origin}/auth/callback/${provider}`,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to get authorization URL");
      }

      const data = await response.json();

      // Store state for verification
      sessionStorage.setItem(`oauth_state_${provider}`, data.state);

      // Redirect to OAuth provider
      window.location.href = data.authorization_url;
    } catch (error) {
      console.error(`${provider} login error:`, error);
      toast.error(`Failed to login with ${provider}`);
      setLoadingProvider(null);
    }
  };

  const handleShopifyLogin = async () => {
    const shopDomain = prompt(
      "Enter your Shopify store domain (e.g., my-store):"
    );
    if (!shopDomain) return;

    setLoadingProvider("shopify");
    try {
      const response = await fetch("/api/auth/oauth/authorize", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          provider: "shopify",
          redirect_uri: `${window.location.origin}/auth/callback/shopify`,
          shop_domain: shopDomain,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to get Shopify authorization URL");
      }

      const data = await response.json();
      sessionStorage.setItem("oauth_state_shopify", data.state);
      window.location.href = data.authorization_url;
    } catch (error) {
      console.error("Shopify login error:", error);
      toast.error("Failed to login with Shopify");
      setLoadingProvider(null);
    }
  };

  return (
    <div className="grid grid-cols-1 gap-4">
      {/* Google Login */}
      <Button
        variant="outline"
        type="button"
        className="w-full"
        onClick={() => handleSocialLogin("google")}
        disabled={disabled || loadingProvider !== null}
      >
        {loadingProvider === "google" ? (
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
        ) : (
          <svg className="mr-2 h-4 w-4" viewBox="0 0 24 24">
            <path
              d="M12.48 10.92v3.28h7.84c-.24 1.84-.853 3.187-1.787 4.133-1.147 1.147-2.933 2.4-6.053 2.4-4.827 0-8.6-3.893-8.6-8.72s3.773-8.72 8.6-8.72c2.6 0 4.507 1.027 5.907 2.347l2.307-2.307C18.747 1.44 16.133 0 12.48 0 5.867 0 .307 5.387.307 12s5.56 12 12.173 12c3.573 0 6.267-1.173 8.373-3.36 2.16-2.16 2.84-5.213 2.84-7.667 0-.76-.053-1.467-.173-2.053H12.48z"
              fill="currentColor"
            />
          </svg>
        )}
        Continue with Google
      </Button>

      {/* GitHub Login */}
      <Button
        variant="outline"
        type="button"
        className="w-full"
        onClick={() => handleSocialLogin("github")}
        disabled={disabled || loadingProvider !== null}
      >
        {loadingProvider === "github" ? (
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
        ) : (
          <svg className="mr-2 h-4 w-4" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z" />
          </svg>
        )}
        Continue with GitHub
      </Button>

      {/* Shopify Login */}
      <Button
        variant="outline"
        type="button"
        className="w-full"
        onClick={handleShopifyLogin}
        disabled={disabled || loadingProvider !== null}
      >
        {loadingProvider === "shopify" ? (
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
        ) : (
          <svg className="mr-2 h-4 w-4" fill="currentColor" viewBox="0 0 24 24">
            <path d="M15.337 2.368c-.332-.048-.672-.048-1.008 0-.336.048-.672.144-1.008.288-.336.144-.624.336-.864.576-.24.24-.432.528-.576.864-.144.336-.24.672-.288 1.008-.048.336-.048.672 0 1.008.048.336.144.672.288 1.008.144.336.336.624.576.864.24.24.528.432.864.576.336.144.672.24 1.008.288.336.048.672.048 1.008 0 .336-.048.672-.144 1.008-.288.336-.144.624-.336.864-.576.24-.24.432-.528.576-.864.144-.336.24-.672.288-1.008.048-.336.048-.672 0-1.008-.048-.336-.144-.672-.288-1.008-.144-.336-.336-.624-.576-.864-.24-.24-.528-.432-.864-.576-.336-.144-.672-.24-1.008-.288z" />
          </svg>
        )}
        Continue with Shopify
      </Button>
    </div>
  );
}
