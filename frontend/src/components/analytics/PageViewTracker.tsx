'use client';

import { useEffect } from 'react';
import { usePathname } from 'next/navigation';
import useAnalytics from '@/hooks/useAnalytics';

const PageViewTracker = () => {
  const pathname = usePathname();
  const { trackPageView } = useAnalytics();

  useEffect(() => {
    // Scroll to top on route change
    window.scrollTo(0, 0);
    
    // Track page view
    trackPageView(pathname, document.title);
  }, [pathname, trackPageView]);

  return null;
};

export default PageViewTracker;
