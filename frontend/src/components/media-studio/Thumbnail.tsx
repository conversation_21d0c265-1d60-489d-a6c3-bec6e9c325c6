import React, { useState } from "react";
import { Asset } from "@/services/assetService";
import { cn } from "@/lib/utils";
import { Play, AlertCircle, Image as ImageIcon } from "lucide-react";
import { Card } from "@/components/ui/card";

interface ThumbnailProps {
  asset: Asset;
  isSelected: boolean;
  onSelect: (asset: Asset, isMultiSelect: boolean) => void;
  isGenerated?: boolean;
  isFailedPlaceholder?: boolean;
  draggable?: boolean;
}

export const Thumbnail: React.FC<ThumbnailProps> = ({
  asset,
  isSelected,
  onSelect,
  isGenerated = false,
  isFailedPlaceholder = false,
  draggable = false,
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [isDragging, setIsDragging] = useState(false);

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onSelect(asset, e.ctrlKey || e.meta<PERSON>ey);
  };

  const handleDragStart = (e: React.DragEvent) => {
    if (!draggable) return;

    setIsDragging(true);
    e.dataTransfer.setData("text/plain", asset.id);
    e.dataTransfer.effectAllowed = "copy";

    // Create a custom drag image
    const dragImage = document.createElement("div");
    dragImage.innerHTML = `
      <div style="
        background: rgba(0,0,0,0.8);
        color: white;
        padding: 8px 12px;
        border-radius: 6px;
        font-size: 12px;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 6px;
      ">
        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
          <circle cx="9" cy="9" r="2"/>
          <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/>
        </svg>
        ${asset.displayName || asset.filename}
      </div>
    `;
    document.body.appendChild(dragImage);
    e.dataTransfer.setDragImage(dragImage, 10, 10);

    // Clean up the drag image after a short delay
    setTimeout(() => {
      document.body.removeChild(dragImage);
    }, 0);
  };

  const handleDragEnd = () => {
    setIsDragging(false);
  };

  const isPlaceholder = asset.id.startsWith("temp_");
  const isVideo = asset.type === "video";

  return (
    <Card
      className={cn(
        "relative aspect-square overflow-hidden cursor-pointer border-2 transition-all duration-200 group",
        isSelected
          ? "border-primary ring-2 ring-primary/20"
          : "border-border hover:border-border/80",
        isDragging && "opacity-50 scale-95",
        isFailedPlaceholder && "border-destructive",
        draggable && "hover:shadow-lg"
      )}
      onClick={handleClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      draggable={draggable}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      title={`${asset.displayName || asset.filename}${isGenerated ? " (Generated)" : ""}`}
    >
      {/* Image/Video Content */}
      {isPlaceholder ? (
        <div className="w-full h-full bg-muted flex items-center justify-center">
          {isFailedPlaceholder ? (
            <AlertCircle className="h-8 w-8 text-destructive" />
          ) : (
            <div className="animate-spin rounded-full h-8 w-8 border-2 border-primary border-t-transparent" />
          )}
        </div>
      ) : (
        <>
          <img
            src={asset.url}
            alt={asset.displayName || asset.filename}
            className="w-full h-full object-cover"
            loading="lazy"
          />

          {/* Video overlay */}
          {isVideo && (
            <div className="absolute inset-0 bg-black/20 flex items-center justify-center">
              <Play className="h-6 w-6 text-white drop-shadow-lg" />
            </div>
          )}

          {/* Hover overlay with drag indicator */}
          {isHovered && draggable && (
            <div className="absolute inset-0 bg-black/20 flex items-center justify-center">
              <div className="bg-background/90 rounded-full p-2">
                <ImageIcon className="h-4 w-4 text-foreground" />
              </div>
            </div>
          )}
        </>
      )}

      {/* Selection indicator */}
      {isSelected && (
        <div className="absolute top-2 right-2 w-5 h-5 bg-primary rounded-full flex items-center justify-center">
          <svg
            className="w-3 h-3 text-white"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              fillRule="evenodd"
              d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
              clipRule="evenodd"
            />
          </svg>
        </div>
      )}

      {/* AI badge */}
      {asset.sourceType === "ai_generated" && (
        <div className="absolute bottom-2 left-2 px-2 py-1 bg-purple-500 text-white text-xs rounded-full font-medium">
          AI
        </div>
      )}

      {/* Drag indicator */}
      {draggable && (
        <div className="absolute bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
          <div className="bg-background/90 rounded-full p-1">
            <svg
              className="w-3 h-3 text-muted-foreground"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"
              />
            </svg>
          </div>
        </div>
      )}
    </Card>
  );
};
