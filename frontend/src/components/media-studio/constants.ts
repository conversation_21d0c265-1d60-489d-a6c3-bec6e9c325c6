import type { Model } from "../types";

export const MODELS: Model[] = [
  { id: 'gemini-2.5-flash', name: 'Gemini 2.5 Flash', health: 'good', eta: '~2s', cost: 1 },
  { id: 'imagen-4.0', name: 'Imagen 4.0', health: 'good', eta: '~5s', cost: 2 },
  { id: 'dall-e-3', name: 'DALL-E 3', health: 'degraded', eta: '~8s', cost: 3 },
  { id: 'midjourney-v6', name: 'Midjourney v6', health: 'good', eta: '~12s', cost: 4 },
  { id: 'veo-2.0', name: 'Veo 2.0 (Video)', health: 'good', eta: '~45s', cost: 10 },
];