import React from "react";
import { GenerationMode } from "@/types/mediaStudio";
import { cn } from "@/lib/utils";
import { ImageIcon, VideoIcon, FileTextIcon } from "lucide-react";

interface ModeToggleProps {
  mode: GenerationMode;
  onChange: (mode: GenerationMode) => void;
  orientation?: "horizontal" | "vertical";
  fullWidth?: boolean;
}

export const ModeToggle: React.FC<ModeToggleProps> = ({
  mode,
  onChange,
  orientation = "horizontal",
  fullWidth = false,
}) => {
  return (
    <div
      className={cn(
        "flex bg-gray-100 dark:bg-gray-800 rounded-lg p-1",
        orientation === "vertical" ? "flex-col" : "flex-row",
        fullWidth && "w-full"
      )}
    >
      <button
        onClick={() => onChange("image")}
        className={cn(
          "flex items-center gap-1.5 px-3 py-2 text-xs font-medium rounded-md transition-all duration-200",
          mode === "image"
            ? "bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm"
            : "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100"
        )}
      >
        <ImageIcon className="w-3.5 h-3.5" />
        Image
      </button>
      <button
        onClick={() => onChange("video")}
        className={cn(
          "flex items-center gap-1.5 px-3 py-2 text-xs font-medium rounded-md transition-all duration-200",
          mode === "video"
            ? "bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm"
            : "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100"
        )}
      >
        <VideoIcon className="w-3.5 h-3.5" />
        Video
      </button>
      <button
        onClick={() => onChange("text")}
        className={cn(
          "flex items-center gap-1.5 px-3 py-2 text-xs font-medium rounded-md transition-all duration-200",
          mode === "text"
            ? "bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm"
            : "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100"
        )}
      >
        <FileTextIcon className="w-3.5 h-3.5" />
        Text
      </button>
    </div>
  );
};