import React from "react";
import { Asset } from "@/services/assetService";
import { Thumbnail } from "./Thumbnail";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { RefreshCw } from "lucide-react";

interface PreviewPaneProps {
  assets: Asset[];
  loading?: boolean;
  error?: string | null;
  onRefresh?: () => void;
  onLoadMore?: () => void;
  hasMore?: boolean;
  isLoadingMore?: boolean;
}

export const PreviewPane: React.FC<PreviewPaneProps> = ({
  assets = [],
  loading = false,
  error = null,
  onRefresh,
  onLoadMore,
  hasMore = false,
  isLoadingMore = false,
}) => {
  const [typeFilter, setTypeFilter] = React.useState<"all" | "image" | "video">(
    "all"
  );
  const [promptQuery, setPromptQuery] = React.useState<string>("");
  const [debouncedQuery, setDebouncedQuery] = React.useState<string>("");

  // Debounce query to reduce recompute churn
  React.useEffect(() => {
    const t = window.setTimeout(
      () => setDebouncedQuery(promptQuery.trim().toLowerCase()),
      200
    );
    return () => window.clearTimeout(t);
  }, [promptQuery]);

  // Intersection observer for infinite scroll
  const sentinelRef = React.useRef<HTMLDivElement | null>(null);
  React.useEffect(() => {
    if (!sentinelRef.current || !hasMore || isLoadingMore || !onLoadMore) return;

    const el = sentinelRef.current;
    const io = new IntersectionObserver(
      (entries) => {
        const e = entries[0];
        if (e && e.isIntersecting) {
          // Trigger load-more when sentinel enters view
          onLoadMore();
        }
      },
      { root: null, rootMargin: "200px 0px 0px 0px", threshold: 0 }
    );
    io.observe(el);
    return () => io.disconnect();
  }, [hasMore, isLoadingMore, onLoadMore]);

  const filteredAssets = React.useMemo(() => {
    const q = debouncedQuery;
    return assets.filter((a) => {
      const typeOk = typeFilter === "all" || a.type === typeFilter;
      const text = `${a.prompt || ""} ${a.filename || ""}`.toLowerCase();
      const queryOk = q === "" || text.includes(q);
      return typeOk && queryOk;
    });
  }, [assets, typeFilter, debouncedQuery]);

  const onSelect = React.useCallback(() => {}, []);

  return (
    <Card className="w-full h-full flex-1 self-stretch overflow-auto">
      <CardContent className="p-4 lg:p-6">
        {loading ? (
          <div className="h-full min-h-[200px] flex items-center justify-center text-muted-foreground text-sm">
            <RefreshCw className="h-6 w-6 animate-spin mr-2" />
            Loading…
          </div>
        ) : error ? (
          <div className="h-full min-h-[200px] flex items-center justify-center text-amber-700 text-sm">
            {error}
          </div>
        ) : assets.length === 0 ? (
          <div className="h-full min-h-[200px] flex items-center justify-center text-muted-foreground text-sm">
            No generated assets yet.
          </div>
        ) : (
          <>
            {/* Filters toolbar */}
            <div className="mb-3 flex items-center gap-2 flex-wrap">
              {/* Type tags */}
              <Button
                variant={typeFilter === "all" ? "default" : "outline"}
                size="sm"
                onClick={() => setTypeFilter("all")}
                className="px-2.5 py-1 text-xs rounded-full h-7"
                aria-pressed={typeFilter === "all"}
                title="All media"
              >
                All
              </Button>
              <Button
                variant={typeFilter === "image" ? "default" : "outline"}
                size="sm"
                onClick={() => setTypeFilter("image")}
                className="px-2.5 py-1 text-xs rounded-full h-7"
                aria-pressed={typeFilter === "image"}
                title="Images"
              >
                Images
              </Button>
              <Button
                variant={typeFilter === "video" ? "default" : "outline"}
                size="sm"
                onClick={() => setTypeFilter("video")}
                className="px-2.5 py-1 text-xs rounded-full h-7"
                aria-pressed={typeFilter === "video"}
                title="Videos"
              >
                Videos
              </Button>

              {/* Search input */}
              <div className="flex-1 max-w-xs">
                <Input
                  type="text"
                  placeholder="Search prompts..."
                  value={promptQuery}
                  onChange={(e) => setPromptQuery(e.target.value)}
                  className="h-7 text-xs"
                />
              </div>

              {/* Refresh button */}
              <Button
                variant="outline"
                size="sm"
                onClick={onRefresh}
                disabled={loading}
                className="h-7 px-2"
              >
                <RefreshCw
                  className={`h-3 w-3 ${loading ? "animate-spin" : ""}`}
                />
              </Button>

              {/* Count tag aligned right */}
              <span className="ml-auto inline-flex items-center rounded-full bg-gray-200 dark:bg-gray-600 text-gray-800 dark:text-gray-100 px-2.5 py-0.5 text-[11px] font-medium">
                {filteredAssets.length} items
              </span>
            </div>

            {/* Simple Grid */}
            <div className="relative h-full w-full overflow-auto">
              <div className="grid grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 pb-10">
                {filteredAssets.map((asset) => (
                  <div key={asset.id} className="w-full aspect-square">
                    <Thumbnail
                      asset={asset}
                      isSelected={false}
                      onSelect={onSelect}
                      isGenerated={asset.sourceType === "ai_generated"}
                    />
                  </div>
                ))}
              </div>

              {/* Bottom sentinel for infinite scroll */}
              {hasMore && <div ref={sentinelRef} className="w-full h-10" />}

              {/* Loading more indicator */}
              {isLoadingMore && (
                <div className="absolute bottom-2 left-1/2 -translate-x-1/2 text-xs text-muted-foreground">
                  <RefreshCw className="h-4 w-4 animate-spin inline mr-1" />
                  Loading more…
                </div>
              )}
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
};
