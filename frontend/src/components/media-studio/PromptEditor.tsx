import React, { useState, useRef, useCallback } from "react";
import { Asset } from "@/services/mediaService";
import { cn } from "@/lib/utils";
import { Image, X, AtSign } from "lucide-react";

interface PromptEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  availableAssets?: Asset[];
  onAssetReference?: (asset: Asset) => void;
}

interface AssetReference {
  id: string;
  displayName: string;
  url: string;
  type: "image" | "video";
}

export const PromptEditor: React.FC<PromptEditorProps> = ({
  value,
  onChange,
  placeholder = "Describe what to create...",
  className,
  availableAssets = [],
  onAssetReference,
}) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [isDragOver, setIsDragOver] = useState(false);
  const [showAssetPicker, setShowAssetPicker] = useState(false);
  const [atPosition, setAtPosition] = useState<number | null>(null);
  const [assetReferences, setAssetReferences] = useState<AssetReference[]>([]);

  // Parse asset references from the prompt text
  const parseAssetReferences = useCallback((text: string) => {
    const references: AssetReference[] = [];
    const regex = /@(\w+)/g;
    let match;

    while ((match = regex.exec(text)) !== null) {
      const assetId = match[1];
      const asset = availableAssets.find(a => a.id === assetId);
      if (asset) {
        references.push({
          id: asset.id,
          displayName: asset.displayName || asset.filename,
          url: asset.url,
          type: asset.type,
        });
      }
    }

    setAssetReferences(references);
  }, [availableAssets]);

  React.useEffect(() => {
    parseAssetReferences(value);
  }, [value, parseAssetReferences]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    const assetId = e.dataTransfer.getData("text/plain");
    if (assetId) {
      const asset = availableAssets.find(a => a.id === assetId);
      if (asset && textareaRef.current) {
        const textarea = textareaRef.current;
        const start = textarea.selectionStart;
        const end = textarea.selectionEnd;
        const beforeText = value.substring(0, start);
        const afterText = value.substring(end);
        const reference = `@${asset.id}`;

        const newValue = beforeText + reference + afterText;
        onChange(newValue);

        // Move cursor after the reference
        setTimeout(() => {
          textarea.focus();
          textarea.setSelectionRange(start + reference.length, start + reference.length);
        }, 0);

        if (onAssetReference) {
          onAssetReference(asset);
        }
      }
    }
  }, [value, onChange, availableAssets, onAssetReference]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === "@") {
      const textarea = textareaRef.current;
      if (textarea) {
        const position = textarea.selectionStart;
        setAtPosition(position);
        setShowAssetPicker(true);
      }
    } else if (e.key === "Escape") {
      setShowAssetPicker(false);
      setAtPosition(null);
    }
  }, []);

  const handleAssetSelect = useCallback((asset: Asset) => {
    if (atPosition !== null && textareaRef.current) {
      const textarea = textareaRef.current;
      const beforeText = value.substring(0, atPosition);
      const afterText = value.substring(atPosition);
      const reference = `@${asset.id}`;

      const newValue = beforeText + reference + afterText;
      onChange(newValue);

      setShowAssetPicker(false);
      setAtPosition(null);

      // Move cursor after the reference
      setTimeout(() => {
        textarea.focus();
        const newPosition = atPosition + reference.length;
        textarea.setSelectionRange(newPosition, newPosition);
      }, 0);

      if (onAssetReference) {
        onAssetReference(asset);
      }
    }
  }, [value, onChange, atPosition, onAssetReference]);

  const removeAssetReference = useCallback((assetId: string) => {
    const newValue = value.replace(new RegExp(`@${assetId}\\b`, 'g'), '');
    onChange(newValue);
  }, [value, onChange]);

  return (
    <div className="relative">
      {/* Asset References Display */}
      {assetReferences.length > 0 && (
        <div className="mb-2 flex flex-wrap gap-1">
          {assetReferences.map((ref) => (
            <div
              key={ref.id}
              className="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 text-xs rounded-full"
            >
              <Image className="h-3 w-3" />
              <span className="truncate max-w-20">{ref.displayName}</span>
              <button
                onClick={() => removeAssetReference(ref.id)}
                className="ml-1 hover:bg-blue-200 dark:hover:bg-blue-800 rounded-full p-0.5"
              >
                <X className="h-3 w-3" />
              </button>
            </div>
          ))}
        </div>
      )}

      {/* Textarea */}
      <div className="relative">
        <textarea
          ref={textareaRef}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          onKeyDown={handleKeyDown}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          placeholder={placeholder}
          className={cn(
            "w-full min-h-[80px] p-3 text-sm border rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-primary resize-none transition-colors",
            isDragOver && "ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-900/20",
            className
          )}
        />

        {/* Drag overlay */}
        {isDragOver && (
          <div className="absolute inset-0 bg-blue-500/10 border-2 border-dashed border-blue-500 rounded-md flex items-center justify-center">
            <div className="text-center text-blue-600 dark:text-blue-400">
              <Image className="h-8 w-8 mx-auto mb-2" />
              <p className="text-sm font-medium">Drop image here to reference</p>
            </div>
          </div>
        )}

        {/* Asset Picker */}
        {showAssetPicker && (
          <div className="absolute bottom-full left-0 right-0 mb-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-md shadow-lg z-50 max-h-48 overflow-y-auto">
            <div className="p-2">
              <div className="text-xs text-gray-500 dark:text-gray-400 mb-2 flex items-center gap-1">
                <AtSign className="h-3 w-3" />
                Select asset to reference
              </div>
              {availableAssets.length === 0 ? (
                <div className="text-xs text-gray-400 py-2">No assets available</div>
              ) : (
                <div className="space-y-1">
                  {availableAssets.slice(0, 10).map((asset) => (
                    <button
                      key={asset.id}
                      onClick={() => handleAssetSelect(asset)}
                      className="w-full flex items-center gap-2 p-2 text-left hover:bg-gray-50 dark:hover:bg-gray-700 rounded text-xs"
                    >
                      <div className="w-6 h-6 bg-gray-100 dark:bg-gray-600 rounded flex items-center justify-center flex-shrink-0">
                        {asset.type === "video" ? (
                          <div className="w-3 h-3 bg-gray-400 rounded-sm relative">
                            <div className="absolute inset-0 flex items-center justify-center">
                              <div className="w-1 h-1 bg-white rounded-full"></div>
                            </div>
                          </div>
                        ) : (
                          <Image className="h-3 w-3 text-gray-500" />
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="font-medium truncate">
                          {asset.displayName || asset.filename}
                        </div>
                        <div className="text-gray-400 text-xs truncate">
                          {asset.id}
                        </div>
                      </div>
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Helper text */}
      <div className="mt-1 text-xs text-gray-500 dark:text-gray-400 flex items-center gap-2">
        <span>Drag images here or type @ to reference assets</span>
        {availableAssets.length > 0 && (
          <span className="text-blue-500">
            {availableAssets.length} assets available
          </span>
        )}
      </div>
    </div>
  );
};