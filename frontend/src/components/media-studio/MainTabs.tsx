import React from "react";
import { MainTab } from "@/types/mediaStudio";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface MainTabsProps {
  activeTab: MainTab;
  onTabChange: (tab: MainTab) => void;
}

export const MainTabs: React.FC<MainTabsProps> = ({
  activeTab,
  onTabChange,
}) => {
  const tabs: Array<{ id: MainTab; label: string }> = [
    { id: "canvas", label: "Gallery" },
    { id: "models", label: "Models" },
    { id: "props", label: "Props" },
    { id: "scenes", label: "Scenes" },
    { id: "brandbook", label: "Brandbook" },
  ];

  return (
    <div className="flex-shrink-0 w-48 bg-card border-l border-border flex flex-col">
      <Tabs
        value={activeTab}
        onValueChange={(value) => onTabChange(value as MainTab)}
        orientation="vertical"
        className="w-full"
      >
        <TabsList className="h-auto w-full flex-col bg-transparent p-0 space-y-0">
          {tabs.map(({ id, label }) => (
            <TabsTrigger
              key={id}
              value={id}
              className="w-full justify-start px-4 py-3 text-sm font-medium border-b border-border/50 rounded-none data-[state=active]:bg-muted data-[state=active]:text-foreground data-[state=active]:border-r-2 data-[state=active]:border-r-primary hover:bg-muted/50 transition-colors"
            >
              {label}
            </TabsTrigger>
          ))}
        </TabsList>
      </Tabs>
    </div>
  );
};
