"use client";

import { useState, useRef, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Progress } from "@/components/ui/progress";
import { Skeleton } from "@/components/ui/skeleton";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { 
  Play, 
  Pause, 
  Volume2, 
  VolumeX, 
  Maximize, 
  Download, 
  Share2, 
  MoreHorizontal, 
  RefreshCw, 
  Heart, 
  HeartOff,
  Eye,
  Clock,
  TrendingUp,
  ShoppingCart,
  Upload
} from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { VideoService as videoService } from "@/services/videoService";
import { toast } from "sonner";

interface VideoVariant {
  id: string;
  jobId: string;
  productId: string;
  variantName: string;
  videoUrl: string;
  thumbnailUrl?: string;
  duration: number;
  status: 'generating' | 'ready' | 'failed';
  isFavorite: boolean;
  metrics: {
    views: number;
    plays: number;
    completionRate: number;
    avgWatchTime: number;
    ctaClicks: number;
    addToCarts: number;
  };
  versions: Array<{
    id: string;
    createdAt: string;
    isActive: boolean;
  }>;
}

interface VariantCardsProps {
  selectedVariants: string[];
  onSelectionChange: (variantIds: string[]) => void;
  onPushSelected: () => void;
}

export function VariantCards({ selectedVariants, onSelectionChange, onPushSelected }: VariantCardsProps) {
  const [playingVideo, setPlayingVideo] = useState<string | null>(null);
  const [mutedVideos, setMutedVideos] = useState<Set<string>>(new Set());
  const [videoProgress, setVideoProgress] = useState<Record<string, number>>({});
  const videoRefs = useRef<Record<string, HTMLVideoElement>>({});

  // Fetch video variants
  const { data: variantsData, isLoading, refetch } = useQuery({
    queryKey: ['video-variants'],
    queryFn: () => videoService.getVariants(),
    refetchInterval: 5000, // Refetch every 5 seconds to update generating videos
  });

  const variants = variantsData?.variants || [];

  const handleVideoSelect = (variantId: string, checked: boolean) => {
    if (checked) {
      onSelectionChange([...selectedVariants, variantId]);
    } else {
      onSelectionChange(selectedVariants.filter(id => id !== variantId));
    }
  };

  const handleSelectAll = () => {
    const readyVariants = variants.filter(v => v.status === 'ready').map(v => v.id);
    onSelectionChange(readyVariants);
  };

  const handleDeselectAll = () => {
    onSelectionChange([]);
  };

  const handlePlayPause = (variantId: string) => {
    const video = videoRefs.current[variantId];
    if (!video) return;

    if (playingVideo === variantId) {
      video.pause();
      setPlayingVideo(null);
    } else {
      // Pause other videos
      Object.values(videoRefs.current).forEach(v => v.pause());
      setPlayingVideo(variantId);
      video.play();
      
      // Track video play event
      trackVideoEvent(variantId, 'play');
    }
  };

  const handleMuteToggle = (variantId: string) => {
    const video = videoRefs.current[variantId];
    if (!video) return;

    const newMutedVideos = new Set(mutedVideos);
    if (mutedVideos.has(variantId)) {
      newMutedVideos.delete(variantId);
      video.muted = false;
    } else {
      newMutedVideos.add(variantId);
      video.muted = true;
    }
    setMutedVideos(newMutedVideos);
  };

  const handleVideoTimeUpdate = (variantId: string) => {
    const video = videoRefs.current[variantId];
    if (!video) return;

    const progress = (video.currentTime / video.duration) * 100;
    setVideoProgress(prev => ({ ...prev, [variantId]: progress }));

    // Track video progress events
    if (progress >= 25 && progress < 26) {
      trackVideoEvent(variantId, 'progress_25');
    } else if (progress >= 50 && progress < 51) {
      trackVideoEvent(variantId, 'progress_50');
    } else if (progress >= 75 && progress < 76) {
      trackVideoEvent(variantId, 'progress_75');
    }
  };

  const handleVideoEnded = (variantId: string) => {
    setPlayingVideo(null);
    trackVideoEvent(variantId, 'complete');
  };

  const trackVideoEvent = async (variantId: string, eventType: string) => {
    try {
      await videoService.trackEvent({
        variantId,
        eventType,
        timestamp: new Date().toISOString(),
        sessionId: getSessionId(),
      });
    } catch (error) {
      console.error('Failed to track video event:', error);
    }
  };

  const getSessionId = () => {
    let sessionId = sessionStorage.getItem('video_session_id');
    if (!sessionId) {
      sessionId = Math.random().toString(36).substring(2, 15);
      sessionStorage.setItem('video_session_id', sessionId);
    }
    return sessionId;
  };

  const handleToggleFavorite = async (variantId: string) => {
    try {
      await videoService.toggleFavorite(variantId);
      refetch();
      toast.success('Favorite updated');
    } catch (error) {
      toast.error('Failed to update favorite');
    }
  };

  const handleRegenerate = async (variantId: string) => {
    try {
      await videoService.regenerateVariant(variantId);
      refetch();
      toast.success('Regeneration started');
    } catch (error) {
      toast.error('Failed to start regeneration');
    }
  };

  const handleDownload = async (variantId: string) => {
    const variant = variants.find(v => v.id === variantId);
    if (!variant) return;

    try {
      const response = await fetch(variant.videoUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${variant.variantName}-${variant.productId}.mp4`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      toast.success('Download started');
    } catch (error) {
      toast.error('Failed to download video');
    }
  };

  const handleShare = async (variantId: string) => {
    const variant = variants.find(v => v.id === variantId);
    if (!variant) return;

    try {
      await navigator.share({
        title: `${variant.variantName} - Product Video`,
        url: variant.videoUrl,
      });
    } catch (error) {
      // Fallback to copying URL
      await navigator.clipboard.writeText(variant.videoUrl);
      toast.success('Video URL copied to clipboard');
    }
  };

  const readyVariants = variants.filter(v => v.status === 'ready');
  const isAllSelected = readyVariants.length > 0 && readyVariants.every(v => selectedVariants.includes(v.id));
  const isSomeSelected = readyVariants.some(v => selectedVariants.includes(v.id));

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {Array.from({ length: 6 }).map((_, i) => (
            <Card key={i}>
              <CardContent className="p-4">
                <Skeleton className="aspect-video w-full mb-4" />
                <Skeleton className="h-4 w-3/4 mb-2" />
                <Skeleton className="h-3 w-1/2" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Selection Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Checkbox
            checked={isAllSelected}
            onCheckedChange={(checked) => {
              if (checked) {
                handleSelectAll();
              } else {
                handleDeselectAll();
              }
            }}
            ref={(el) => {
              if (el) {
                el.indeterminate = isSomeSelected && !isAllSelected;
              }
            }}
          />
          <span className="text-sm text-muted-foreground">
            {selectedVariants.length > 0 
              ? `${selectedVariants.length} variant${selectedVariants.length !== 1 ? 's' : ''} selected`
              : "Select variants"
            }
          </span>
        </div>

        {selectedVariants.length > 0 && (
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={handleDeselectAll}>
              Clear Selection
            </Button>
            <Button size="sm" onClick={onPushSelected}>
              <Upload className="mr-2 h-4 w-4" />
              Push to Shopify ({selectedVariants.length})
            </Button>
          </div>
        )}
      </div>

      {/* Variants Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {variants.map((variant) => (
          <Card 
            key={variant.id} 
            className={`relative ${
              selectedVariants.includes(variant.id) ? 'ring-2 ring-primary' : ''
            }`}
          >
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm">{variant.variantName}</CardTitle>
                <div className="flex items-center space-x-2">
                  {variant.status === 'ready' && (
                    <Checkbox
                      checked={selectedVariants.includes(variant.id)}
                      onCheckedChange={(checked) => handleVideoSelect(variant.id, checked as boolean)}
                    />
                  )}
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleToggleFavorite(variant.id)}>
                        {variant.isFavorite ? (
                          <>
                            <HeartOff className="mr-2 h-4 w-4" />
                            Remove from Favorites
                          </>
                        ) : (
                          <>
                            <Heart className="mr-2 h-4 w-4" />
                            Add to Favorites
                          </>
                        )}
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleRegenerate(variant.id)}>
                        <RefreshCw className="mr-2 h-4 w-4" />
                        Regenerate
                      </DropdownMenuItem>
                      {variant.status === 'ready' && (
                        <>
                          <DropdownMenuItem onClick={() => handleDownload(variant.id)}>
                            <Download className="mr-2 h-4 w-4" />
                            Download
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleShare(variant.id)}>
                            <Share2 className="mr-2 h-4 w-4" />
                            Share
                          </DropdownMenuItem>
                        </>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <Badge variant="outline" className="text-xs">
                  Product {variant.productId}
                </Badge>
                <Badge variant={
                  variant.status === 'ready' ? 'default' : 
                  variant.status === 'generating' ? 'secondary' : 'destructive'
                }>
                  {variant.status}
                </Badge>
              </div>
            </CardHeader>

            <CardContent className="space-y-3">
              {/* Video Player */}
              <div className="relative aspect-video bg-gray-100 rounded-lg overflow-hidden">
                {variant.status === 'generating' ? (
                  <div className="flex items-center justify-center h-full">
                    <div className="text-center">
                      <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2 text-gray-400" />
                      <p className="text-sm text-gray-500">Generating...</p>
                    </div>
                  </div>
                ) : variant.status === 'failed' ? (
                  <div className="flex items-center justify-center h-full">
                    <div className="text-center">
                      <p className="text-sm text-red-500">Generation failed</p>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="mt-2"
                        onClick={() => handleRegenerate(variant.id)}
                      >
                        <RefreshCw className="mr-2 h-4 w-4" />
                        Retry
                      </Button>
                    </div>
                  </div>
                ) : (
                  <>
                    <video
                      ref={(el) => {
                        if (el) videoRefs.current[variant.id] = el;
                      }}
                      src={variant.videoUrl}
                      poster={variant.thumbnailUrl}
                      className="w-full h-full object-cover"
                      onTimeUpdate={() => handleVideoTimeUpdate(variant.id)}
                      onEnded={() => handleVideoEnded(variant.id)}
                      muted={mutedVideos.has(variant.id)}
                      loop={false}
                    />
                    
                    {/* Video Controls Overlay */}
                    <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center">
                      <div className="flex items-center space-x-2 opacity-0 hover:opacity-100 transition-opacity">
                        <Button
                          variant="secondary"
                          size="sm"
                          onClick={() => handlePlayPause(variant.id)}
                        >
                          {playingVideo === variant.id ? (
                            <Pause className="h-4 w-4" />
                          ) : (
                            <Play className="h-4 w-4" />
                          )}
                        </Button>
                        <Button
                          variant="secondary"
                          size="sm"
                          onClick={() => handleMuteToggle(variant.id)}
                        >
                          {mutedVideos.has(variant.id) ? (
                            <VolumeX className="h-4 w-4" />
                          ) : (
                            <Volume2 className="h-4 w-4" />
                          )}
                        </Button>
                      </div>
                    </div>

                    {/* Progress Bar */}
                    {videoProgress[variant.id] > 0 && (
                      <div className="absolute bottom-0 left-0 right-0">
                        <Progress 
                          value={videoProgress[variant.id]} 
                          className="h-1 rounded-none"
                        />
                      </div>
                    )}

                    {/* Favorite Indicator */}
                    {variant.isFavorite && (
                      <div className="absolute top-2 left-2">
                        <Heart className="h-4 w-4 text-red-500 fill-current" />
                      </div>
                    )}

                    {/* Duration */}
                    <div className="absolute top-2 right-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
                      {Math.floor(variant.duration / 60)}:{(variant.duration % 60).toString().padStart(2, '0')}
                    </div>
                  </>
                )}
              </div>

              {/* Metrics */}
              {variant.status === 'ready' && (
                <div className="grid grid-cols-2 gap-2 text-xs">
                  <div className="flex items-center space-x-1">
                    <Eye className="h-3 w-3 text-gray-400" />
                    <span>{variant.metrics.views} views</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Play className="h-3 w-3 text-gray-400" />
                    <span>{variant.metrics.plays} plays</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <TrendingUp className="h-3 w-3 text-gray-400" />
                    <span>{variant.metrics.completionRate}% completion</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Clock className="h-3 w-3 text-gray-400" />
                    <span>{variant.metrics.avgWatchTime}s avg</span>
                  </div>
                </div>
              )}

              {/* Version History */}
              {variant.versions.length > 1 && (
                <div className="text-xs text-muted-foreground">
                  <span>Version {variant.versions.findIndex(v => v.isActive) + 1} of {variant.versions.length}</span>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {variants.length === 0 && (
        <Card className="p-12">
          <div className="text-center text-muted-foreground">
            <Play className="mx-auto h-12 w-12 mb-4 opacity-50" />
            <p>No video variants found.</p>
            <p className="text-sm mt-2">Generate some videos to see them here.</p>
          </div>
        </Card>
      )}
    </div>
  );
}
