import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Analytics utilities
export interface GtagEvent {
  action: string;
  category: string;
  label?: string;
  value?: number;
  [key: string]: any; // Allow for additional custom parameters
}

// Declare global gtag function
declare global {
  interface Window {
    gtag: (
      command: "config" | "event" | "js" | "set",
      targetId: string | Date,
      config?: any
    ) => void;
    dataLayer: any[];
  }
}
