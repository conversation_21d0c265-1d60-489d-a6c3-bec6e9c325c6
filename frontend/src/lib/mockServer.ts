/**
 * Mock server for local development
 * Provides realistic API responses for testing the frontend without a backend
 */

import { delay } from './utils';

// Mock data
const mockProducts = [
  {
    id: 'product-1',
    title: 'Wireless Bluetooth Headphones - Premium Sound Quality',
    handle: 'wireless-bluetooth-headphones',
    images: [
      {
        id: 'img-1',
        src: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=400&fit=crop',
        alt: 'Wireless Bluetooth Headphones'
      }
    ],
    variants: [
      { id: 'variant-1', title: 'Black', price: '99.99', sku: 'WBH-001-BLK' },
      { id: 'variant-2', title: 'White', price: '99.99', sku: 'WBH-001-WHT' },
      { id: 'variant-3', title: 'Silver', price: '109.99', sku: 'WBH-001-SLV' }
    ],
    tags: ['electronics', 'audio', 'wireless', 'premium'],
    productType: 'Electronics',
    vendor: 'AudioTech',
    status: 'active'
  },
  {
    id: 'product-2',
    title: 'Smart Fitness Watch with Heart Rate Monitor',
    handle: 'smart-fitness-watch',
    images: [
      {
        id: 'img-2',
        src: 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=400&fit=crop',
        alt: 'Smart Fitness Watch'
      }
    ],
    variants: [
      { id: 'variant-4', title: 'Small - Black', price: '199.99', sku: 'SFW-001-SM-BLK' },
      { id: 'variant-5', title: 'Large - Black', price: '199.99', sku: 'SFW-001-LG-BLK' }
    ],
    tags: ['fitness', 'wearable', 'smart', 'health'],
    productType: 'Wearables',
    vendor: 'FitTech',
    status: 'active'
  }
];

const mockTemplates = [
  {
    id: 'template-1',
    name: 'Product Showcase',
    description: 'Clean product presentation with smooth transitions',
    category: 'Showcase',
    thumbnailUrl: 'https://images.unsplash.com/photo-**********-b33ff0c44a43?w=200&h=120&fit=crop'
  },
  {
    id: 'template-2',
    name: 'Lifestyle Demo',
    description: 'Product in real-world usage scenarios',
    category: 'Lifestyle',
    thumbnailUrl: 'https://images.unsplash.com/photo-**********-0cfed4f6a45d?w=200&h=120&fit=crop'
  }
];

const mockVoices = [
  { id: 'voice-1', name: 'Sarah', gender: 'Female', accent: 'American', sampleUrl: '/voices/sarah.mp3' },
  { id: 'voice-2', name: 'David', gender: 'Male', accent: 'British', sampleUrl: '/voices/david.mp3' }
];

const mockVariants = [
  {
    id: 'variant-1',
    jobId: 'job-1',
    productId: 'product-1',
    variantName: 'Energetic Showcase',
    videoUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
    thumbnailUrl: 'https://images.unsplash.com/photo-**********-b33ff0c44a43?w=320&h=180&fit=crop',
    duration: 30,
    status: 'ready',
    isFavorite: false,
    metrics: {
      views: 1250,
      plays: 890,
      completionRate: 78.5,
      avgWatchTime: 23.4,
      ctaClicks: 45,
      addToCarts: 12
    },
    versions: [{ id: 'v1', createdAt: '2024-01-15T10:30:00Z', isActive: true }]
  }
];

// Mock API handlers
export class MockServer {
  private static jobs = new Map<string, any>();
  private static jobCounter = 1;

  static async handleRequest(url: string, options: RequestInit = {}): Promise<Response> {
    await delay(Math.random() * 500 + 200); // Simulate network delay

    const urlObj = new URL(url);
    const path = urlObj.pathname;
    const method = options.method || 'GET';
    const searchParams = urlObj.searchParams;

    console.log(`[MockServer] ${method} ${path}`);

    // Shopify endpoints
    if (path === '/api/shopify/products') {
      const search = searchParams.get('search');
      let products = [...mockProducts];
      
      if (search) {
        products = products.filter(p => 
          p.title.toLowerCase().includes(search.toLowerCase())
        );
      }
      
      return new Response(JSON.stringify({ products, total: products.length }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    if (path === '/api/shopify/collections') {
      return new Response(JSON.stringify({
        collections: [
          { id: 'collection-1', title: 'Electronics', handle: 'electronics' },
          { id: 'collection-2', title: 'Fitness', handle: 'fitness' }
        ]
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    if (path === '/api/shopify/product-tags') {
      return new Response(JSON.stringify({
        tags: ['electronics', 'audio', 'wireless', 'fitness', 'wearable', 'smart']
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    if (path === '/api/shopify/stores') {
      return new Response(JSON.stringify({
        stores: [{
          id: 'store-1',
          name: 'Demo Store',
          domain: 'demo-store.myshopify.com',
          isConnected: true,
          productCount: 150,
          videosGenerated: 45,
          lastSync: '2 hours ago'
        }]
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Video generation endpoints
    if (path === '/api/video-generation/templates') {
      return new Response(JSON.stringify({ templates: mockTemplates }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    if (path === '/api/video-generation/voices') {
      return new Response(JSON.stringify({ voices: mockVoices }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    if (path === '/api/video-generation/generate' && method === 'POST') {
      const jobId = `job-${this.jobCounter++}`;
      const job = {
        id: jobId,
        status: 'pending',
        progress: 0,
        createdAt: new Date().toISOString()
      };
      
      this.jobs.set(jobId, job);
      
      // Simulate job progression
      setTimeout(() => {
        const job = this.jobs.get(jobId);
        if (job) {
          job.status = 'processing';
          job.progress = 25;
        }
      }, 1000);
      
      setTimeout(() => {
        const job = this.jobs.get(jobId);
        if (job) {
          job.status = 'processing';
          job.progress = 75;
          job.variants = [
            {
              id: `variant-${Date.now()}-1`,
              productId: 'product-1',
              variantName: 'Variant 1',
              status: 'generating'
            },
            {
              id: `variant-${Date.now()}-2`,
              productId: 'product-1',
              variantName: 'Variant 2',
              status: 'generating'
            }
          ];
        }
      }, 3000);
      
      setTimeout(() => {
        const job = this.jobs.get(jobId);
        if (job) {
          job.status = 'completed';
          job.progress = 100;
          job.variants = job.variants?.map((v: any) => ({
            ...v,
            status: 'ready',
            videoUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
            thumbnailUrl: 'https://images.unsplash.com/photo-**********-b33ff0c44a43?w=320&h=180&fit=crop'
          }));
        }
      }, 5000);
      
      return new Response(JSON.stringify({ job }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    if (path.startsWith('/api/video-generation/jobs/') && path.endsWith('/status')) {
      const jobId = path.split('/')[4];
      const job = this.jobs.get(jobId);
      
      if (!job) {
        return new Response(JSON.stringify({ error: 'Job not found' }), {
          status: 404,
          headers: { 'Content-Type': 'application/json' }
        });
      }
      
      return new Response(JSON.stringify(job), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    if (path === '/api/video-generation/variants') {
      return new Response(JSON.stringify({ variants: mockVariants }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Analytics endpoints
    if (path === '/api/analytics/dashboard') {
      return new Response(JSON.stringify({
        analytics: {
          overview: {
            totalViews: 5420,
            totalPlays: 3890,
            avgCompletionRate: 78.5,
            avgWatchTime: 23.4,
            totalConversions: 156,
            totalRevenue: 4680.50,
            viewsChange: 12.5,
            playsChange: 8.3,
            completionRateChange: 2.1,
            conversionsChange: 15.7
          },
          chartData: {
            daily: Array.from({ length: 7 }, (_, i) => ({
              date: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
              views: Math.floor(Math.random() * 200) + 100,
              plays: Math.floor(Math.random() * 150) + 80,
              completions: Math.floor(Math.random() * 100) + 60,
              conversions: Math.floor(Math.random() * 20) + 5
            })).reverse(),
            hourly: Array.from({ length: 24 }, (_, i) => ({
              hour: `${i.toString().padStart(2, '0')}:00`,
              views: Math.floor(Math.random() * 50) + 10,
              plays: Math.floor(Math.random() * 40) + 8
            }))
          },
          conversionFunnel: [
            { stage: 'Views', count: 5420, percentage: 100 },
            { stage: 'Plays', count: 3890, percentage: 71.8 },
            { stage: 'Completions', count: 3050, percentage: 56.3 },
            { stage: 'CTA Clicks', count: 890, percentage: 16.4 },
            { stage: 'Conversions', count: 156, percentage: 2.9 }
          ],
          deviceBreakdown: [
            { device: 'Desktop', count: 3250, percentage: 60 },
            { device: 'Mobile', count: 1890, percentage: 35 },
            { device: 'Tablet', count: 280, percentage: 5 }
          ],
          topPerformers: [
            {
              variantId: 'variant-1',
              variantName: 'Energetic Showcase',
              productTitle: 'Wireless Bluetooth Headphones',
              views: 1250,
              plays: 890,
              completionRate: 78.5,
              conversions: 45,
              revenue: 1350.00
            }
          ]
        }
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Default 404 response
    return new Response(JSON.stringify({ error: 'Not found' }), {
      status: 404,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

// Override fetch for development
export function enableMockServer() {
  if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
    const originalFetch = window.fetch;
    
    window.fetch = async (input: RequestInfo | URL, init?: RequestInit) => {
      const url = typeof input === 'string' ? input : input.toString();
      
      // Only mock API calls to our backend
      if (url.includes('/api/')) {
        try {
          return await MockServer.handleRequest(url, init);
        } catch (error) {
          console.error('[MockServer] Error:', error);
          // Fall back to original fetch
          return originalFetch(input, init);
        }
      }
      
      // Use original fetch for other requests
      return originalFetch(input, init);
    };
    
    console.log('[MockServer] Mock server enabled for development');
  }
}
