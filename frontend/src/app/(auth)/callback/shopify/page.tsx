"use client";

import { useEffect, useState } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { CheckCircle, XCircle, Loader2 } from "lucide-react";
import { api } from "@/services/api";
import { toast } from "sonner";

export default function ShopifyCallbackPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('');

  useEffect(() => {
    const handleCallback = async () => {
      try {
        const code = searchParams.get('code');
        const shop = searchParams.get('shop');
        const state = searchParams.get('state');

        if (!code || !shop) {
          throw new Error('Missing required parameters: code or shop');
        }

        // Call the backend to complete OAuth
        const response = await api.get(
          `/api/plugins/shopify/oauth/callback?code=${encodeURIComponent(code)}&shop=${encodeURIComponent(shop)}${state ? `&state=${encodeURIComponent(state)}` : ''}`
        );

        setStatus('success');
        setMessage('Shopify store connected successfully!');

        toast.success('Store connected successfully!');

        // Redirect to stores page after a short delay
        setTimeout(() => {
          router.push('/stores');
        }, 2000);

      } catch (error: any) {
        console.error('OAuth callback error:', error);
        setStatus('error');
        setMessage(error?.response?.data?.detail || 'Failed to connect Shopify store');

        toast.error('Failed to connect store');
      }
    };

    handleCallback();
  }, [searchParams, router]);

  return (
    <div className="container mx-auto p-6 flex items-center justify-center min-h-screen">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            {status === 'loading' && <Loader2 className="h-12 w-12 animate-spin text-blue-600" />}
            {status === 'success' && <CheckCircle className="h-12 w-12 text-green-600" />}
            {status === 'error' && <XCircle className="h-12 w-12 text-red-600" />}
          </div>
          <CardTitle>
            {status === 'loading' && 'Connecting Shopify Store'}
            {status === 'success' && 'Connection Successful'}
            {status === 'error' && 'Connection Failed'}
          </CardTitle>
          <CardDescription>{message}</CardDescription>
        </CardHeader>
        <CardContent className="text-center">
          {status === 'success' && (
            <p className="text-sm text-muted-foreground mb-4">
              Redirecting to your stores page...
            </p>
          )}
          {status === 'error' && (
            <div className="space-y-4">
              <p className="text-sm text-muted-foreground">
                Please try connecting again or contact support if the problem persists.
              </p>
              <Button onClick={() => router.push('/stores/connect')}>
                Try Again
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}