@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap");
@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme {
  /* Colors */
  --color-background: #ffffff;
  --color-foreground: #0f172a;

  --color-card: #ffffff;
  --color-card-foreground: #0f172a;

  --color-popover: #ffffff;
  --color-popover-foreground: #0f172a;

  /* Primary: Professional blue */
  --color-primary: #2563eb;
  --color-primary-foreground: #f8fafc;

  /* Secondary: Neutral gray */
  --color-secondary: #f1f5f9;
  --color-secondary-foreground: #0f172a;

  --color-muted: #f1f5f9;
  --color-muted-foreground: #64748b;

  /* Accent: Vibrant purple */
  --color-accent: #8b5cf6;
  --color-accent-foreground: #f8fafc;

  --color-destructive: #ef4444;
  --color-destructive-foreground: #f8fafc;

  --color-border: #e2e8f0;
  --color-input: #e2e8f0;
  --color-ring: #2563eb;

  /* Chart colors */
  --color-chart-1: #f97316;
  --color-chart-2: #06b6d4;
  --color-chart-3: #8b5cf6;
  --color-chart-4: #eab308;
  --color-chart-5: #ef4444;

  /* Border radius */
  --radius: 0.75rem;
}

@layer theme {
  .dark {
    /* Dark mode colors */
    --color-background: #0f172a;
    --color-foreground: #f8fafc;

    --color-card: #0f172a;
    --color-card-foreground: #f8fafc;

    --color-popover: #0f172a;
    --color-popover-foreground: #f8fafc;

    /* Primary: Brighter blue for dark mode */
    --color-primary: #3b82f6;
    --color-primary-foreground: #0f172a;

    /* Secondary: Darker gray */
    --color-secondary: #1e293b;
    --color-secondary-foreground: #f8fafc;

    --color-muted: #1e293b;
    --color-muted-foreground: #94a3b8;

    /* Accent: Adjusted for dark mode */
    --color-accent: #a855f7;
    --color-accent-foreground: #0f172a;

    --color-destructive: #f87171;
    --color-destructive-foreground: #0f172a;

    --color-border: #334155;
    --color-input: #1e293b;
    --color-ring: #3b82f6;

    /* Chart colors for dark mode */
    --color-chart-1: #fb923c;
    --color-chart-2: #22d3ee;
    --color-chart-3: #a855f7;
    --color-chart-4: #facc15;
    --color-chart-5: #f87171;
  }
}

/* Base styles */
@layer base {
  * {
    border-color: var(--color-border);
  }

  html, body {
    height: 100%;
    overflow: hidden;
  }

  body {
    color: var(--color-foreground);
    background: var(--color-background);
    font-feature-settings:
      "rlig" 1,
      "calt" 1;
    letter-spacing: var(--tracking-normal);
  }
}

/* Custom animations */
@keyframes accordion-down {
  from {
    height: 0;
  }
  to {
    height: var(--radix-accordion-content-height);
  }
}

@keyframes accordion-up {
  from {
    height: var(--radix-accordion-content-height);
  }
  to {
    height: 0;
  }
}

.animate-accordion-down {
  animation: accordion-down 0.2s ease-out;
}

.animate-accordion-up {
  animation: accordion-up 0.2s ease-out;
}

@theme inline {
  --font-sans: Geist, sans-serif;
  --font-mono: "Fira Code", "Courier New", monospace;
  --font-serif: "Lora", Georgia, serif;
  --radius: 0.5rem;
  --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
  --tracking-tight: calc(var(--tracking-normal) - 0.025em);
  --tracking-wide: calc(var(--tracking-normal) + 0.025em);
  --tracking-wider: calc(var(--tracking-normal) + 0.05em);
  --tracking-widest: calc(var(--tracking-normal) + 0.1em);
  --tracking-normal: var(--tracking-normal);
  --shadow-2xl: var(--shadow-2xl);
  --shadow-xl: var(--shadow-xl);
  --shadow-lg: var(--shadow-lg);
  --shadow-md: var(--shadow-md);
  --shadow: var(--shadow);
  --shadow-sm: var(--shadow-sm);
  --shadow-xs: var(--shadow-xs);
  --shadow-2xs: var(--shadow-2xs);
  --spacing: var(--spacing);
  --letter-spacing: var(--letter-spacing);
  --shadow-offset-y: var(--shadow-offset-y);
  --shadow-offset-x: var(--shadow-offset-x);
  --shadow-spread: var(--shadow-spread);
  --shadow-blur: var(--shadow-blur);
  --shadow-opacity: var(--shadow-opacity);
  --color-shadow-color: var(--shadow-color);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --color-foreground: var(--foreground);
  --color-background: var(--background);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --background: oklch(0.9777 0.0041 301.4256);
  --foreground: oklch(0.3651 0.0325 287.0807);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.3651 0.0325 287.0807);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.3651 0.0325 287.0807);
  --primary: oklch(0.6104 0.0767 299.7335);
  --primary-foreground: oklch(0.9777 0.0041 301.4256);
  --secondary: oklch(0.8957 0.0265 300.2416);
  --secondary-foreground: oklch(0.3651 0.0325 287.0807);
  --muted: oklch(0.8906 0.0139 299.7754);
  --muted-foreground: oklch(0.5288 0.0375 290.7895);
  --accent: oklch(0.7889 0.0802 359.9375);
  --accent-foreground: oklch(0.3394 0.0441 1.7583);
  --destructive: oklch(0.6332 0.1578 22.6734);
  --destructive-foreground: oklch(0.9777 0.0041 301.4256);
  --border: oklch(0.8447 0.0226 300.1421);
  --input: oklch(0.9329 0.0124 301.2783);
  --ring: oklch(0.6104 0.0767 299.7335);
  --chart-1: oklch(0.6104 0.0767 299.7335);
  --chart-2: oklch(0.7889 0.0802 359.9375);
  --chart-3: oklch(0.7321 0.0749 169.867);
  --chart-4: oklch(0.854 0.0882 76.8292);
  --chart-5: oklch(0.7857 0.0645 258.0839);
  --radius: 0.5rem;
  --sidebar: oklch(0.9554 0.0082 301.3541);
  --sidebar-foreground: oklch(0.3651 0.0325 287.0807);
  --sidebar-primary: oklch(0.6104 0.0767 299.7335);
  --sidebar-primary-foreground: oklch(0.9777 0.0041 301.4256);
  --sidebar-accent: oklch(0.7889 0.0802 359.9375);
  --sidebar-accent-foreground: oklch(0.3394 0.0441 1.7583);
  --sidebar-border: oklch(0.8719 0.0198 302.169);
  --sidebar-ring: oklch(0.6104 0.0767 299.7335);
  --font-sans: Geist, sans-serif;
  --font-serif: "Lora", Georgia, serif;
  --font-mono: "Fira Code", "Courier New", monospace;
  --shadow-color: hsl(0 0% 0%);
  --shadow-opacity: 0.06;
  --shadow-blur: 5px;
  --shadow-spread: 1px;
  --shadow-offset-x: 1px;
  --shadow-offset-y: 2px;
  --letter-spacing: 0em;
  --spacing: 0.25rem;
  --shadow-2xs: 1px 2px 5px 1px hsl(0 0% 0% / 0.03);
  --shadow-xs: 1px 2px 5px 1px hsl(0 0% 0% / 0.03);
  --shadow-sm:
    1px 2px 5px 1px hsl(0 0% 0% / 0.06), 1px 1px 2px 0px hsl(0 0% 0% / 0.06);
  --shadow:
    1px 2px 5px 1px hsl(0 0% 0% / 0.06), 1px 1px 2px 0px hsl(0 0% 0% / 0.06);
  --shadow-md:
    1px 2px 5px 1px hsl(0 0% 0% / 0.06), 1px 2px 4px 0px hsl(0 0% 0% / 0.06);
  --shadow-lg:
    1px 2px 5px 1px hsl(0 0% 0% / 0.06), 1px 4px 6px 0px hsl(0 0% 0% / 0.06);
  --shadow-xl:
    1px 2px 5px 1px hsl(0 0% 0% / 0.06), 1px 8px 10px 0px hsl(0 0% 0% / 0.06);
  --shadow-2xl: 1px 2px 5px 1px hsl(0 0% 0% / 0.15);
  --tracking-normal: 0em;
}

.dark {
  --background: oklch(0.2166 0.0215 292.8474);
  --foreground: oklch(0.9053 0.0245 293.557);
  --card: oklch(0.2544 0.0301 292.7315);
  --card-foreground: oklch(0.9053 0.0245 293.557);
  --popover: oklch(0.2544 0.0301 292.7315);
  --popover-foreground: oklch(0.9053 0.0245 293.557);
  --primary: oklch(0.7058 0.0777 302.0489);
  --primary-foreground: oklch(0.2166 0.0215 292.8474);
  --secondary: oklch(0.4604 0.0472 295.5578);
  --secondary-foreground: oklch(0.9053 0.0245 293.557);
  --muted: oklch(0.256 0.032 294.838);
  --muted-foreground: oklch(0.6974 0.0282 300.0614);
  --accent: oklch(0.3181 0.0321 308.6149);
  --accent-foreground: oklch(0.8391 0.0692 2.6681);
  --destructive: oklch(0.6875 0.142 21.4566);
  --destructive-foreground: oklch(0.2166 0.0215 292.8474);
  --border: oklch(0.3063 0.0359 293.3367);
  --input: oklch(0.2847 0.0346 291.2726);
  --ring: oklch(0.7058 0.0777 302.0489);
  --chart-1: oklch(0.7058 0.0777 302.0489);
  --chart-2: oklch(0.8391 0.0692 2.6681);
  --chart-3: oklch(0.7321 0.0749 169.867);
  --chart-4: oklch(0.854 0.0882 76.8292);
  --chart-5: oklch(0.7857 0.0645 258.0839);
  --radius: 0.5rem;
  --sidebar: oklch(0.1985 0.02 293.6639);
  --sidebar-foreground: oklch(0.9053 0.0245 293.557);
  --sidebar-primary: oklch(0.7058 0.0777 302.0489);
  --sidebar-primary-foreground: oklch(0.2166 0.0215 292.8474);
  --sidebar-accent: oklch(0.3181 0.0321 308.6149);
  --sidebar-accent-foreground: oklch(0.8391 0.0692 2.6681);
  --sidebar-border: oklch(0.2847 0.0346 291.2726);
  --sidebar-ring: oklch(0.7058 0.0777 302.0489);
  --font-sans: Geist, sans-serif;
  --font-serif: "Lora", Georgia, serif;
  --font-mono: "Fira Code", "Courier New", monospace;
  --shadow-color: hsl(0 0% 0%);
  --shadow-opacity: 0.06;
  --shadow-blur: 5px;
  --shadow-spread: 1px;
  --shadow-offset-x: 1px;
  --shadow-offset-y: 2px;
  --letter-spacing: 0em;
  --spacing: 0.25rem;
  --shadow-2xs: 1px 2px 5px 1px hsl(0 0% 0% / 0.03);
  --shadow-xs: 1px 2px 5px 1px hsl(0 0% 0% / 0.03);
  --shadow-sm:
    1px 2px 5px 1px hsl(0 0% 0% / 0.06), 1px 1px 2px 0px hsl(0 0% 0% / 0.06);
  --shadow:
    1px 2px 5px 1px hsl(0 0% 0% / 0.06), 1px 1px 2px 0px hsl(0 0% 0% / 0.06);
  --shadow-md:
    1px 2px 5px 1px hsl(0 0% 0% / 0.06), 1px 2px 4px 0px hsl(0 0% 0% / 0.06);
  --shadow-lg:
    1px 2px 5px 1px hsl(0 0% 0% / 0.06), 1px 4px 6px 0px hsl(0 0% 0% / 0.06);
  --shadow-xl:
    1px 2px 5px 1px hsl(0 0% 0% / 0.06), 1px 8px 10px 0px hsl(0 0% 0% / 0.06);
  --shadow-2xl: 1px 2px 5px 1px hsl(0 0% 0% / 0.15);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
