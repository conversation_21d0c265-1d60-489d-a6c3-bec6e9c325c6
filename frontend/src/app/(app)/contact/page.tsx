'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Mail, 
  Phone, 
  MapPin,
  Clock,
  Send,
  MessageCircle,
  Calendar,
  Building
} from 'lucide-react';
import useAnalytics from '@/hooks/useAnalytics';
import useFormTracking from '@/hooks/useFormTracking';
import useBookDemo from '@/hooks/useBookDemo';

const ContactPage = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    company: '',
    phone: '',
    subject: '',
    message: '',
    inquiryType: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const { trackEvent } = useAnalytics();
  const { trackFormStart, trackFormSubmit, trackFieldFocus } = useFormTracking({ 
    formName: 'Contact Form' 
  });
  const { handleBookDemoClick } = useBookDemo({ 
    label: 'Contact Page - Schedule Demo Button',
    redirectTo: '/demo'
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    try {
      // Simulate form submission
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      trackFormSubmit(true);
      trackEvent({
        action: 'contact_form_submitted',
        category: 'Contact',
        label: `Inquiry Type: ${formData.inquiryType}`,
      });
      
      // Reset form
      setFormData({
        name: '',
        email: '',
        company: '',
        phone: '',
        subject: '',
        message: '',
        inquiryType: '',
      });
      
      alert('Message sent successfully! We\'ll get back to you soon.');
    } catch (error) {
      trackFormSubmit(false);
      alert('Error sending message. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const contactInfo = [
    {
      title: 'Email Us',
      description: 'Send us an email anytime',
      icon: Mail,
      value: '<EMAIL>',
      action: 'mailto:<EMAIL>'
    },
    {
      title: 'Call Us',
      description: 'Mon-Fri 9AM-6PM EST',
      icon: Phone,
      value: '+****************',
      action: 'tel:+***********'
    },
    {
      title: 'Visit Us',
      description: 'Our headquarters',
      icon: MapPin,
      value: '123 Business Ave, Suite 100\nSan Francisco, CA 94105',
      action: 'https://maps.google.com'
    },
    {
      title: 'Business Hours',
      description: 'When we\'re available',
      icon: Clock,
      value: 'Mon-Fri: 9AM-6PM EST\nSat-Sun: Closed',
      action: null
    },
  ];

  const handleContactClick = (title: string, action: string | null) => {
    trackEvent({
      action: 'contact_info_click',
      category: 'Contact',
      label: title,
    });
    
    if (action) {
      window.open(action, '_blank');
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold mb-4">Contact Us</h1>
        <p className="text-xl text-muted-foreground mb-6">
          Get in touch with our team. We'd love to hear from you!
        </p>
        <div className="flex justify-center gap-4">
          <Button onClick={handleBookDemoClick} className="gap-2">
            <Calendar className="h-4 w-4" />
            Schedule a Demo
          </Button>
          <Button variant="outline" className="gap-2">
            <MessageCircle className="h-4 w-4" />
            Live Chat
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Contact Form */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Send className="h-5 w-5" />
                Send us a Message
              </CardTitle>
              <CardDescription>
                Fill out the form below and we'll get back to you within 24 hours
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="name">Full Name *</Label>
                    <Input
                      id="name"
                      type="text"
                      value={formData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      onFocus={() => trackFieldFocus('name')}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="email">Email Address *</Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      onFocus={() => trackFieldFocus('email')}
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="company">Company</Label>
                    <Input
                      id="company"
                      type="text"
                      value={formData.company}
                      onChange={(e) => handleInputChange('company', e.target.value)}
                      onFocus={() => trackFieldFocus('company')}
                    />
                  </div>
                  <div>
                    <Label htmlFor="phone">Phone Number</Label>
                    <Input
                      id="phone"
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      onFocus={() => trackFieldFocus('phone')}
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="inquiryType">Inquiry Type *</Label>
                  <Select 
                    value={formData.inquiryType} 
                    onValueChange={(value) => handleInputChange('inquiryType', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="What can we help you with?" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="demo">Schedule a Demo</SelectItem>
                      <SelectItem value="sales">Sales Inquiry</SelectItem>
                      <SelectItem value="support">Technical Support</SelectItem>
                      <SelectItem value="partnership">Partnership</SelectItem>
                      <SelectItem value="billing">Billing Question</SelectItem>
                      <SelectItem value="general">General Question</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="subject">Subject *</Label>
                  <Input
                    id="subject"
                    type="text"
                    value={formData.subject}
                    onChange={(e) => handleInputChange('subject', e.target.value)}
                    onFocus={() => trackFieldFocus('subject')}
                    placeholder="Brief description of your inquiry"
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="message">Message *</Label>
                  <Textarea
                    id="message"
                    value={formData.message}
                    onChange={(e) => handleInputChange('message', e.target.value)}
                    onFocus={() => trackFieldFocus('message')}
                    placeholder="Tell us more about how we can help you..."
                    rows={6}
                    required
                  />
                </div>

                <Button 
                  type="submit" 
                  className="w-full" 
                  disabled={isSubmitting}
                  onClick={() => trackFormStart()}
                >
                  {isSubmitting ? 'Sending...' : 'Send Message'}
                </Button>
              </form>
            </CardContent>
          </Card>
        </div>

        {/* Contact Information */}
        <div className="space-y-6">
          {/* Contact Details */}
          <Card>
            <CardHeader>
              <CardTitle>Get in Touch</CardTitle>
              <CardDescription>
                Multiple ways to reach our team
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {contactInfo.map((info) => (
                <div
                  key={info.title}
                  className={`flex items-start gap-3 p-3 rounded-lg ${
                    info.action ? 'cursor-pointer hover:bg-muted transition-colors' : ''
                  }`}
                  onClick={() => info.action && handleContactClick(info.title, info.action)}
                >
                  <div className="p-2 bg-primary/10 rounded-lg">
                    <info.icon className="h-4 w-4 text-primary" />
                  </div>
                  <div>
                    <h4 className="font-medium text-sm">{info.title}</h4>
                    <p className="text-xs text-muted-foreground mb-1">{info.description}</p>
                    <p className="text-sm whitespace-pre-line">{info.value}</p>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Office Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building className="h-5 w-5" />
                Our Office
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div>
                  <h4 className="font-medium text-sm">Headquarters</h4>
                  <p className="text-sm text-muted-foreground">
                    123 Business Avenue<br />
                    Suite 100<br />
                    San Francisco, CA 94105<br />
                    United States
                  </p>
                </div>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="w-full"
                  onClick={() => handleContactClick('View on Map', 'https://maps.google.com')}
                >
                  View on Map
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Quick Links */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Links</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button 
                variant="ghost" 
                size="sm" 
                className="w-full justify-start"
                onClick={() => trackEvent({
                  action: 'quick_link_click',
                  category: 'Contact',
                  label: 'FAQ'
                })}
              >
                Frequently Asked Questions
              </Button>
              <Button 
                variant="ghost" 
                size="sm" 
                className="w-full justify-start"
                onClick={() => trackEvent({
                  action: 'quick_link_click',
                  category: 'Contact',
                  label: 'Documentation'
                })}
              >
                Documentation
              </Button>
              <Button 
                variant="ghost" 
                size="sm" 
                className="w-full justify-start"
                onClick={() => trackEvent({
                  action: 'quick_link_click',
                  category: 'Contact',
                  label: 'System Status'
                })}
              >
                System Status
              </Button>
              <Button 
                variant="ghost" 
                size="sm" 
                className="w-full justify-start"
                onClick={() => trackEvent({
                  action: 'quick_link_click',
                  category: 'Contact',
                  label: 'Community Forum'
                })}
              >
                Community Forum
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default ContactPage;
