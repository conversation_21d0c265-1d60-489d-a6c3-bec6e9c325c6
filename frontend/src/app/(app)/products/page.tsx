'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useLayout } from '@/contexts/LayoutContext';
import { cn } from '@/lib/utils';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { MediaViewer } from '@/components/ui/media-viewer';
import { MediaItem } from '@/services/productService';
import { Package, Plus, Search, Loader2, RefreshCw, ShoppingCart } from 'lucide-react';
import {
  productService,
  Product,
  ProductVariant,
} from '@/services/productService';
import { MetafieldsSection } from '@/components/ui/metafields-display';
import { storeService, Store } from '@/services/storeService';
import { toast } from 'sonner';

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { api } from '@/services/api';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';

const Products: React.FC = () => {
  const router = useRouter();
  
  const [products, setProducts] = useState<Product[]>([]);
  const [pageLoading, setPageLoading] = useState(true);
  const [productsLoading, setProductsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalProducts, setTotalProducts] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('');
  const [isImageModalOpen, setIsImageModalOpen] = useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [currentProductMedia, setCurrentProductMedia] = useState<MediaItem[]>([]);
  const itemsPerPage = 50;

  const { sidebarCollapsed, isMobile } = useLayout();
  const queryClient = useQueryClient();

  const paginationLeftClass = cn(
    !isMobile && sidebarCollapsed ? "left-16" : !isMobile ? "left-64" : "left-0"
  );

  // Fetch user stores
  const {
    data: userStores,
    isLoading: storesLoading,
    error: storesError
  } = useQuery<Store[]>({
    queryKey: ['user-stores'],
    queryFn: async () => {
      const stores = await storeService.getStores();
      return stores.filter(store => store.is_active); // Only active stores
    },
    retry: 2,
    retryDelay: 1000,
  });

  // Get the first active store ID (or allow user to select in future)
  const primaryStoreId = userStores?.[0]?.id;

  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
      setCurrentPage(1); // Reset to first page when searching
    }, 500);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setProductsLoading(true);
        const response = await productService.getProducts(currentPage, itemsPerPage, debouncedSearchQuery);

        if (response && response.items) {
          setProducts(response.items);
          setTotalPages(response.total_pages || 1);
          setTotalProducts(response.total || 0);
          console.log('Set totalProducts to:', response.total || 0);
        } else {
          console.error('Invalid response format:', response);
          setProducts([]);
          setTotalPages(1);
          setTotalProducts(0);
        }
        setError(null);
      } catch (err) {
        console.error('Failed to fetch products:', err);
        setError('Failed to load products. Please try again.');
      } finally {
        setProductsLoading(false);
        setPageLoading(false); // Set pageLoading to false once initial products are fetched
      }
    };

    fetchProducts();
  }, [currentPage, debouncedSearchQuery]);

  

  // Sync mutation using React Query - now using storeService for consistency
  const syncMutation = useMutation({
    mutationFn: async ({ storeId, mode }: { storeId: number; mode: 'full' | 'incremental' }) => {
      // Use the stores API endpoint for sync
      const response = await api.post(`/api/stores/${storeId}/sync/products`, {
        mode: mode
      });
      return response.data;
    },
    onSuccess: (data, variables) => {
      const modeText = variables.mode === 'full' ? 'Full' : 'Incremental';
      toast.success(`${modeText} product sync has been triggered. Job ID: ${data.job_id}`);
      // Invalidate and refetch products after sync
      queryClient.invalidateQueries({ queryKey: ['products'] });
    },
    onError: (error: any) => {
      console.error('Failed to start sync:', error);
      toast.error(error?.response?.data?.detail || "Failed to trigger product sync. Please try again.");
    },
  });

  const handleSync = () => {
    if (storesLoading) {
      toast("Please wait while we load your stores.");
      return;
    }

    if (storesError) {
      toast.error("Unable to load your stores. Please try refreshing the page.");
      return;
    }

    if (!primaryStoreId) {
      toast.error("No active store found. Please connect a store first.");
      return;
    }

    syncMutation.mutate({ storeId: primaryStoreId, mode: 'incremental' });
  };

  const getProductPricing = (product: Product) => {
    if (!product.variants || product.variants.length === 0) {
      return { minPrice: 0, maxPrice: 0, totalQuantity: 0 };
    }

    const prices = product.variants
      .map(v => v.price !== null && v.price !== undefined ? v.price : v.compare_at_price)
      .filter(p => p !== null && p !== undefined) as number[];
    
    const quantities = product.variants
      .map(v => v.quantity || 0);

    return {
      minPrice: prices.length > 0 ? Math.min(...prices) : 0,
      maxPrice: prices.length > 0 ? Math.max(...prices) : 0,
      totalQuantity: quantities.reduce((sum, q) => sum + q, 0)
    };
  };

  const formatPrice = (price: number) => {
    if (price === 0) {
      return "N/A";
    }
    return `${price.toFixed(2)}`;
  };



  

  if (pageLoading || storesLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">
          {storesLoading ? 'Loading stores...' : 'Loading products...'}
        </span>
      </div>
    );
  }

  if (error || storesError) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <p className="text-red-600 mb-4">
            {error || (storesError && 'Failed to load stores. Please try again.')}
          </p>
          <Button onClick={() => window.location.reload()}>
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 pb-24">
      {/* Page Description */}
      <div className="flex items-center justify-between">
        <div>
          <p className="text-muted-foreground">
            Manage your product catalog ({totalProducts} products)
            {debouncedSearchQuery && (
              <span className="ml-2 text-blue-600">
                • Searching for "{debouncedSearchQuery}"
              </span>
            )}
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleSync}
            disabled={syncMutation.isPending || !primaryStoreId}
          >
            {syncMutation.isPending ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="mr-2 h-4 w-4" />
            )}
            {syncMutation.isPending ? 'Syncing...' : 'Sync Products'}
          </Button>

        </div>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center space-x-2">
            <Search className="h-4 w-4 text-muted-foreground" />
            <input
              type="text"
              placeholder="Search products..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="flex-1 border-0 bg-transparent text-sm placeholder:text-muted-foreground focus:outline-none"
            />
            {searchQuery && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSearchQuery('')}
                className="h-6 w-6 p-0"
              >
                ✕
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Products Vertical Layout */}
      <div className="space-y-4">
        {productsLoading ? (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">Loading products...</span>
          </div>
        ) : products?.length === 0 ? (
          <div className="text-center py-12">
            <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">
              {userStores && userStores.length === 0 ? 'No Stores Connected' : 'No products found'}
            </h3>
            <p className="text-muted-foreground mb-4">
              {userStores && userStores.length === 0
                ? 'Connect a store to start syncing products'
                : 'Products will appear here when available from your stores'
              }
            </p>
            {userStores && userStores.length > 0 ? (
              <Button onClick={handleSync} disabled={syncMutation.isPending || !primaryStoreId}>
                {syncMutation.isPending ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <RefreshCw className="mr-2 h-4 w-4" />
                )}
                {syncMutation.isPending ? 'Syncing...' : 'Sync Products'}
              </Button>
            ) : (
              <Button onClick={() => router.push('/stores/connect')}>
                Connect Store
              </Button>
            )}
          </div>
        ) : (
          (products || []).map((product) => {
            const { minPrice, maxPrice, totalQuantity } = getProductPricing(product);

            return (
              <Card key={product.id} className="w-full overflow-hidden">
                <CardContent className="p-0">
                  <div className="flex flex-col md:flex-row">
                    {/* Media Section */}
                    <div className="w-full md:w-1/4 p-4 flex flex-wrap items-center justify-center bg-muted/20 gap-2">
                      {(() => {
                        const productMedia = product.assets || [];

                        if (!productMedia || productMedia.length === 0) {
                          return <Package className="h-24 w-24 text-muted-foreground" />;
                        }

                        // Show ALL media items (videos and images together)
                        return productMedia.map((media, index) => {
                          return (
                            <div
                              key={media.id}
                              className="relative cursor-pointer border-2 border-border/50 hover:border-border transition-colors duration-200 rounded-md overflow-hidden"
                              onClick={() => {
                                setCurrentProductMedia(productMedia);
                                setSelectedImageIndex(index);
                                setIsImageModalOpen(true);
                              }}
                            >
                              {media.type.includes('video') ? (
                                <video
                                  src={media.src}
                                  className="h-24 w-24 object-cover"
                                  muted
                                />
                              ) : (
                                <img
                                  src={media.src}
                                  alt={media.alt}
                                  className="h-24 w-24 object-cover hover:opacity-80 transition-opacity"
                                  onError={(e) => {
                                    console.warn('Image failed to load:', media.src);
                                    e.currentTarget.style.display = 'none';
                                  }}
                                />
                              )}

                              {/* Video play icon overlay */}
                              {media.type.includes('video') && (
                                <div className="absolute inset-0 flex items-center justify-center">
                                  <div className="bg-black bg-opacity-50 rounded-full p-1">
                                    <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                      <path d="M8 5v10l8-5-8-5z"/>
                                    </svg>
                                  </div>
                                </div>
                              )}
                            </div>
                          );
                        });
                      })()}
                    </div>

                    {/* Product Details Section */}
                    <div className="flex-1 p-4 md:p-6">
                      <div className="flex items-start justify-between mb-2">
                        <h3 className="text-xl font-bold leading-tight pr-4">{product.title}</h3>
                        <div className="text-right flex-shrink-0">
                          <p className="text-2xl font-bold text-primary">{formatPrice(minPrice)}</p>
                          {minPrice !== maxPrice && maxPrice > minPrice && (
                            <p className="text-sm text-muted-foreground line-through">
                              {formatPrice(maxPrice)}
                            </p>
                          )}
                        </div>
                      </div>

                      <div className="grid grid-cols-1 sm:grid-cols-3 gap-x-4 gap-y-1 text-sm text-muted-foreground mb-3">
                        <div className="space-y-1">
                          {product.sku && <span><span className="font-medium">SKU:</span> {product.sku}</span>}
                          {product.product_type && <span><span className="font-medium">Type:</span> {product.product_type}</span>}
                          {product.vendor && <span><span className="font-medium">Vendor:</span> {product.vendor}</span>}
                        </div>
                        <div className="space-y-1">
                          <span><span className="font-medium">Status:</span>
                            <Badge variant={product.status === 'active' ? 'default' : 'secondary'} className="ml-1 text-xs">
                              {product.status}
                            </Badge>
                          </span>
                          <span><span className="font-medium">Published:</span>
                            <Badge variant={product.published ? 'default' : 'secondary'} className="ml-1 text-xs">
                              {product.published ? 'Yes' : 'No'}
                            </Badge>
                          </span>
                        </div>
                        <div className="space-y-1">
                          {product.variants && product.variants.length > 1 && (
                            <span><span className="font-medium">Variants:</span> {product.variants.length}</span>
                          )}
                          <span><span className="font-medium">Total Stock:</span>
                            <span className={totalQuantity > 0 ? 'text-green-600 font-medium' : 'text-red-600 font-medium'}>
                              {totalQuantity}
                            </span>
                          </span>
                          {product.variants && product.variants.length === 1 && product.variants[0].weight && (
                            <span><span className="font-medium">Weight:</span> {product.variants[0].weight} {product.variants[0].weight_unit?.toLowerCase()}</span>
                          )}
                        </div>
                      </div>

                      {product.tags && product.tags.length > 0 && (
                        <div className="flex flex-wrap gap-1 mb-3">
                          {product.tags.split(', ').slice(0, 3).map((tag, index) => (
                            <span key={index} className="px-2 py-1 bg-gray-100 text-xs rounded-full">
                              {tag}
                            </span>
                          ))}
                          {product.tags.split(', ').length > 3 && (
                            <span className="px-2 py-1 bg-gray-100 text-xs rounded-full">
                              +{product.tags.split(', ').length - 3} more
                            </span>
                          )}
                        </div>
                      )}

                      {/* Variant Details */}
                      {product.variants && product.variants.length > 1 && (
                        <div className="mb-3">
                          <div className="text-xs font-medium text-muted-foreground mb-1">Variant Options:</div>
                          <div className="flex flex-wrap gap-1">
                            {product.variants.slice(0, 4).map((variant, index) => (
                              <span key={index} className="px-2 py-1 bg-blue-50 text-blue-700 text-xs rounded-md">
                                {variant.title || `Variant ${index + 1}`}
                              </span>
                            ))}
                            {product.variants.length > 4 && (
                              <span className="px-2 py-1 bg-gray-100 text-xs rounded-md">
                                +{product.variants.length - 4} more
                              </span>
                            )}
                          </div>
                        </div>
                      )}

                      {product.description && (
                        <div className="text-sm text-muted-foreground mb-4 line-clamp-2">
                          <div
                            dangerouslySetInnerHTML={{
                              __html: product.description.includes('<') && product.description.includes('>')
                                ? product.description
                                : product.description.replace(/\n/g, '<br>')
                            }}
                          />
                        </div>
                      )}

                      {/* Metafields */}
                      <MetafieldsSection
                        productMetafields={product.metafields}
                        variantMetafields={product.variants?.flatMap(v => v.metafields || [])}
                        imageMetafields={product.images?.flatMap(i => i.metafields || [])}
                        className="mb-4"
                      />

                      {/* Actions */}
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => router.push(`/products/${product.id}`)}
                        >
                          View Details
                        </Button>
                        {product.online_store_url && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => window.open(product.online_store_url, '_blank')}
                          >
                            View on Store
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })
        )}
      </div>

      {/* Pagination Controls */}
      {products?.length > 0 && ( // Only show pagination if there are products
        <div className={cn("fixed bottom-0 right-0 z-50 p-4", paginationLeftClass)}>
          <div className="mt-6">
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    onClick={() => totalPages > 1 && setCurrentPage(prev => Math.max(1, prev - 1))}
                    className={currentPage === 1 || totalPages === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                  />
                </PaginationItem>

                

                {/* Page Numbers */}
                {totalPages > 1 && (
                  <>
                    {/* First page */}
                    <PaginationItem>
                      <PaginationLink
                        onClick={() => setCurrentPage(1)}
                        isActive={currentPage === 1}
                        className={currentPage === 1 ? "" : "cursor-pointer"}
                      >
                        1
                      </PaginationLink>
                    </PaginationItem>

                    {/* Ellipsis after first page */}
                    {currentPage > 3 && (
                      <PaginationItem>
                        <PaginationEllipsis />
                      </PaginationItem>
                    )}

                    {/* Pages around current page */}
                    {Array.from({ length: Math.min(3, totalPages - 2) }, (_, i) => {
                      let pageNum = currentPage - 1 + i;
                      if (currentPage <= 3) pageNum = 2 + i;
                      if (currentPage >= totalPages - 2) pageNum = totalPages - 3 + i;

                      if (pageNum > 1 && pageNum < totalPages) {
                        return (
                          <PaginationItem key={pageNum}>
                            <PaginationLink
                              onClick={() => setCurrentPage(pageNum)}
                              isActive={currentPage === pageNum}
                              className={currentPage === pageNum ? "" : "cursor-pointer"}
                            >
                              {pageNum}
                            </PaginationLink>
                          </PaginationItem>
                        );
                      }
                      return null;
                    })}

                    {/* Ellipsis before last page */}
                    {currentPage < totalPages - 2 && (
                      <PaginationItem>
                        <PaginationEllipsis />
                      </PaginationItem>
                    )}

                    {/* Last page */}
                    {totalPages > 1 && (
                      <PaginationItem>
                        <PaginationLink
                          onClick={() => setCurrentPage(totalPages)}
                          isActive={currentPage === totalPages}
                          className={currentPage === totalPages ? "" : "cursor-pointer"}
                        >
                          {totalPages}
                        </PaginationLink>
                      </PaginationItem>
                    )}
                  </>
                )}

                <PaginationItem>
                  <PaginationNext
                    onClick={() => totalPages > 1 && setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                    className={currentPage === totalPages || totalPages === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>

            
          </div>
        </div>
      )}
    <MediaViewer
      isOpen={isImageModalOpen}
      onClose={() => setIsImageModalOpen(false)}
      mediaItems={currentProductMedia}
      initialIndex={selectedImageIndex}
    />
    </div>
  );
};

export default Products;