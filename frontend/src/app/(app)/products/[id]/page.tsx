"use client";

import React, { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Package,
  ArrowLeft,
  Edit,
  ShoppingCart,
  Loader2,
  ChevronDown,
  ChevronUp,
  Image as ImageIcon,
  Video,
  FileImage,
  FileVideo,
  Search,
} from "lucide-react";
import { JSONTree } from "react-json-tree";
import {
  productService,
  Product,
  ProductVariant,
  ProductImage,
} from "@/services/productService";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { MediaViewer, MediaItem } from "@/components/ui/media-viewer";
import {
  parseJsonStringsRecursively,
  parseFeaturedMedia,
  detectMediaInJson,
  getAllMediaForProduct
} from "@/lib/mediaUtils";

const ProductDetailPage: React.FC = () => {
  const params = useParams();
  const router = useRouter();
  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedVariant, setSelectedVariant] = useState<ProductVariant | null>(
    null
  );
  const [quantity, setQuantity] = useState(1);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [isImageModalOpen, setIsImageModalOpen] = useState(false);
  const [showRawData, setShowRawData] = useState(false);
  const [viewMode, setViewMode] = useState<"tree" | "raw">("tree");

  useEffect(() => {
    const fetchProduct = async () => {
      if (!params.id) return;

      try {
        setLoading(true);
        const data = await productService.getProduct(params.id as string);
        setProduct(data);
        setError(null);

        // Set initial selected variant if available
        if (data.variants && data.variants.length > 0) {
          setSelectedVariant(data.variants[0]);
        }
      } catch (err) {
        console.error("Failed to fetch product:", err);
        setError("Failed to load product details. Please try again.");
      } finally {
        setLoading(false);
      }
    };

    fetchProduct();
  }, [params.id]);


  const parsedProductForViewer = product
    ? parseJsonStringsRecursively(product)
    : null;



  // Get all available media using shared utility
  const getAllMedia = (product: Product) => {
    return getAllMediaForProduct(product);
  };

  const allMedia = product ? getAllMedia(product) : [];
  const hasAnyMedia = allMedia.length > 0;
  const featuredMedia = product ? parseFeaturedMedia(product) : null;
  const isFeaturedVideo =
    featuredMedia && featuredMedia.mediaContentType === "VIDEO";

  // Custom theme matching the app's modern color scheme
  const jsonTreeTheme = {
    scheme: "modern",
    author: "app theme",
    base00: "var(--color-background)", // Background
    base01: "var(--color-muted)", // Lighter background
    base02: "var(--color-border)", // Border/selection
    base03: "var(--color-muted-foreground)", // Comments
    base04: "var(--color-muted-foreground)", // Darker foreground
    base05: "var(--color-foreground)", // Default foreground
    base06: "var(--color-foreground)", // Light foreground
    base07: "var(--color-foreground)", // Lightest foreground
    base08: "var(--color-destructive)", // Red - errors, deletion
    base09: "var(--color-primary)", // Orange - integers, booleans
    base0A: "var(--color-accent)", // Yellow - classes, labels
    base0B: "var(--color-primary)", // Green - strings
    base0C: "var(--color-secondary-foreground)", // Aqua - regex, escape chars
    base0D: "var(--color-accent)", // Blue - functions, methods
    base0E: "var(--color-accent)", // Purple - keywords, operators
    base0F: "var(--color-muted-foreground)", // Brown - brackets, punctuation
  };

  // Keyboard navigation for image modal
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isImageModalOpen || !hasAnyMedia) return;

      switch (e.key) {
        case "ArrowLeft":
          e.preventDefault();
          setSelectedImageIndex((prev) =>
            prev > 0 ? prev - 1 : allMedia.length - 1
          );
          break;
        case "ArrowRight":
          e.preventDefault();
          setSelectedImageIndex((prev) =>
            prev < allMedia.length - 1 ? prev + 1 : 0
          );
          break;
        case "Escape":
          e.preventDefault();
          setIsImageModalOpen(false);
          break;
      }
    };

    if (isImageModalOpen) {
      document.addEventListener("keydown", handleKeyDown);
      return () => document.removeEventListener("keydown", handleKeyDown);
    }
  }, [isImageModalOpen, hasAnyMedia, allMedia.length, selectedImageIndex]);

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading product...</span>
      </div>
    );
  }

  if (error || !product) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Products
          </Button>
        </div>
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <p className="text-red-600 mb-4">{error || "Product not found"}</p>
            <Button onClick={() => router.back()}>Back to Products</Button>
          </div>
        </div>
      </div>
    );
  }

  const hasMultipleVariants = product.variants && product.variants.length > 1;

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Products
          </Button>
          <div>
            <h1 className="text-3xl font-bold">{product.title}</h1>
            <p className="text-muted-foreground">Product Details</p>
          </div>
        </div>
      </div>

      {/* Product Media - Enhanced Display */}
      <div className="w-full">
        {hasAnyMedia ? (
          <div className="w-full mb-6">
            {/* Detected Media Section */}
            {allMedia.some((media) => media.type.startsWith("detected")) && (
              <div className="mb-6">
                <h3 className="text-sm font-medium text-muted-foreground mb-3 flex items-center gap-2">
                  <Search className="h-4 w-4" />
                  Detected Media from Product Data
                </h3>
                <div className="flex gap-4 overflow-x-auto pb-3">
                  {allMedia
                    .filter((media) => media.type.startsWith("detected"))
                    .map((media, originalIndex) => {
                      const index = allMedia.findIndex(
                        (m) => m.id === media.id
                      );
                      return (
                        <div
                          key={`${media.type}-${media.id}`}
                          className="flex-shrink-0 w-96 h-64 border-2 border-blue-200 rounded-lg overflow-hidden cursor-pointer hover:border-blue-400 hover:shadow-lg transition-all duration-200 relative group"
                          onClick={() => {
                            setSelectedImageIndex(index);
                            setIsImageModalOpen(true);
                          }}
                        >
                          {media.type === "detected_video" ? (
                            <div className="w-full h-full bg-gradient-to-br from-blue-50 to-blue-100 flex items-center justify-center">
                              <div className="text-center">
                                <FileVideo className="h-16 w-16 text-blue-500 mx-auto mb-3" />
                                <span className="text-sm font-medium text-blue-700">
                                  Video File
                                </span>
                                <div className="text-xs text-blue-600 mt-1 space-y-0.5">
                                  <p className="max-w-32 truncate">
                                    Path: {media.path?.split(".").pop() || "Detected"}
                                  </p>
                                  {media.key && (
                                    <p className="max-w-32 truncate">
                                      Key: <span className="font-mono bg-blue-50 px-1 rounded text-xs">{media.key}</span>
                                    </p>
                                  )}
                                </div>
                              </div>
                            </div>
                          ) : (
                            <img
                              src={media.src}
                              alt={media.alt}
                              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                              onError={(e) => {
                                e.currentTarget.style.display = "none";
                                e.currentTarget.nextElementSibling!.classList.remove(
                                  "hidden"
                                );
                              }}
                            />
                          )}
                          <div className="hidden w-full h-full bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center">
                            <div className="text-center">
                              <FileImage className="h-16 w-16 text-gray-500 mx-auto mb-3" />
                              <span className="text-sm font-medium text-gray-700">
                                Image File
                              </span>
                              <div className="text-xs text-gray-600 mt-1 space-y-0.5">
                                <p className="max-w-32 truncate">
                                  Path: {media.path?.split(".").pop() || "Detected"}
                                </p>
                                {media.key && (
                                  <p className="max-w-32 truncate">
                                    Key: <span className="font-mono bg-gray-50 px-1 rounded text-xs">{media.key}</span>
                                  </p>
                                )}
                              </div>
                            </div>
                          </div>
                          <div className="absolute top-2 right-2 bg-blue-500 text-white text-xs px-2 py-1 rounded-md font-medium shadow-md">
                            {media.type === "detected_video" ? (
                              <div className="flex items-center gap-1">
                                <Video className="h-3 w-3" />
                                VIDEO
                              </div>
                            ) : (
                              <div className="flex items-center gap-1">
                                <ImageIcon className="h-3 w-3" />
                                IMAGE
                              </div>
                            )}
                          </div>
                        </div>
                      );
                    })}
                </div>
              </div>
            )}

            {/* Featured Media Section */}
            {allMedia.some((media) => media.type.startsWith("featured")) && (
              <div className="mb-6">
                <h3 className="text-sm font-medium text-muted-foreground mb-3 flex items-center gap-2">
                  <Video className="h-4 w-4" />
                  Featured Media
                </h3>
                <div className="flex gap-4 overflow-x-auto pb-3">
                  {allMedia
                    .filter((media) => media.type.startsWith("featured"))
                    .map((media, originalIndex) => {
                      const index = allMedia.findIndex(
                        (m) => m.id === media.id
                      );
                      return (
                        <div
                          key={`${media.type}-${media.id}`}
                          className="flex-shrink-0 w-96 h-64 border-2 border-green-200 rounded-lg overflow-hidden cursor-pointer hover:border-green-400 hover:shadow-lg transition-all duration-200 relative group"
                          onClick={() => {
                            setSelectedImageIndex(index);
                            setIsImageModalOpen(true);
                          }}
                        >
                          {media.type === "featured_video" ? (
                            <video
                              src={media.src}
                              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                              controls
                              autoPlay
                              playsInline
                              preload="metadata"
                              onMouseEnter={(e) => e.currentTarget.play()}
                              onMouseLeave={(e) => e.currentTarget.pause()}
                            >
                              <source src={media.src} type="video/mp4" />
                            </video>
                          ) : (
                            <img
                              src={media.src}
                              alt={media.alt}
                              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                            />
                          )}
                          <div className="absolute top-2 right-2 bg-green-500 text-white text-xs px-2 py-1 rounded-md font-medium shadow-md">
                            {media.type === "featured_video" ? (
                              <div className="flex items-center gap-1">
                                <Video className="h-3 w-3" />
                                FEATURED
                              </div>
                            ) : (
                              <div className="flex items-center gap-1">
                                <ImageIcon className="h-3 w-3" />
                                FEATURED
                              </div>
                            )}
                          </div>
                        </div>
                      );
                    })}
                </div>
              </div>
            )}

            {/* Regular Images Section */}
            {allMedia.some((media) => media.type === "image") && (
              <div className="mb-6">
                <h3 className="text-sm font-medium text-muted-foreground mb-3 flex items-center gap-2">
                  <ImageIcon className="h-4 w-4" />
                  Product Images
                </h3>
                <div className="flex gap-4 overflow-x-auto pb-3">
                  {allMedia
                    .filter((media) => media.type === "image")
                    .map((media, originalIndex) => {
                      const index = allMedia.findIndex(
                        (m) => m.id === media.id
                      );
                      return (
                        <div
                          key={`${media.type}-${media.id}`}
                          className="flex-shrink-0 w-96 h-64 border rounded-lg overflow-hidden cursor-pointer hover:shadow-lg transition-all duration-200 relative group"
                          onClick={() => {
                            setSelectedImageIndex(index);
                            setIsImageModalOpen(true);
                          }}
                        >
                          <img
                            src={media.src}
                            alt={media.alt}
                            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                          />
                          <div className="absolute top-2 right-2 bg-gray-800/80 text-white text-xs px-2 py-1 rounded-md font-medium opacity-0 group-hover:opacity-100 transition-opacity">
                            <div className="flex items-center gap-1">
                              <ImageIcon className="h-3 w-3" />
                              PRODUCT
                            </div>
                          </div>
                        </div>
                      );
                    })}
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="w-full h-64 flex items-center justify-center bg-muted rounded-lg mb-6">
            <Package className="h-24 w-24 text-muted-foreground" />
          </div>
        )}
      </div>

      {/* Raw Data Viewer */}
      <div className="w-full mb-6">
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div
                className="flex items-center cursor-pointer hover:bg-muted/50 rounded-md px-2 py-1 -mx-2 -my-1 transition-colors"
                onClick={() => setShowRawData(!showRawData)}
              >
                <CardTitle className="text-lg mr-2">
                  Raw Product Data (JSON)
                </CardTitle>
                {showRawData ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  onClick={(e) => {
                    e.stopPropagation();
                    setViewMode(viewMode === "tree" ? "raw" : "tree");
                  }}
                >
                  {viewMode === "tree" ? "Raw JSON" : "Tree View"}
                </Button>
              </div>
            </div>
          </CardHeader>
          {showRawData && (
            <CardContent>
              <div className="bg-card border border-border rounded-lg p-4 overflow-x-auto max-h-192">
                {viewMode === "tree" ? (
                  <JSONTree
                    data={parsedProductForViewer}
                    theme={jsonTreeTheme}
                    invertTheme={false}
                    hideRoot={false}
                    shouldExpandNodeInitially={() => true}
                  />
                ) : (
                  <pre className="text-sm whitespace-pre-wrap">
                    {JSON.stringify(parsedProductForViewer, null, 2)}
                  </pre>
                )}
              </div>
            </CardContent>
          )}
        </Card>
      </div>

      {/* Product Information - 2 Column Grid */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Left Column */}
        <div className="space-y-6">
          {/* Product Information */}
          <Card>
            <CardHeader>
              <CardTitle>Product Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3 text-sm">
              <div className="flex justify-between items-center">
                <span className="font-medium">Title:</span>
                <span className="font-semibold">{product.title}</span>
              </div>
              {selectedVariant && (
                <div className="flex justify-between items-center">
                  <span className="font-medium">Price:</span>
                  <span className="font-semibold text-primary">
                    $
                    {selectedVariant.price
                      ? selectedVariant.price.toFixed(2)
                      : "N/A"}
                    {selectedVariant.compare_at_price &&
                      selectedVariant.compare_at_price >
                        selectedVariant.price && (
                        <span className="ml-2 text-lg text-muted-foreground line-through">
                          ${selectedVariant.compare_at_price.toFixed(2)}
                        </span>
                      )}
                  </span>
                </div>
              )}
              <div className="flex justify-between items-center">
                <span className="font-medium">SKU:</span>
                <span className="font-mono">
                  {selectedVariant?.sku || product.variants?.[0]?.sku || "N/A"}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="font-medium">Status:</span>
                <Badge
                  variant={
                    product.status === "active" ? "default" : "secondary"
                  }
                >
                  {product.status}
                </Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="font-medium">Vendor:</span>
                <span>{product.vendor || "N/A"}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="font-medium">Product Type:</span>
                <span>{product.product_type || "N/A"}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="font-medium">Published:</span>
                <Badge variant={product.published ? "default" : "secondary"}>
                  {product.published ? "Published" : "Draft"}
                </Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="font-medium">Created:</span>
                <span>{new Date(product.created_at).toLocaleDateString()}</span>
              </div>
              {selectedVariant?.weight && (
                <div className="flex justify-between items-center">
                  <span className="font-medium">Weight:</span>
                  <span>
                    {selectedVariant.weight} {selectedVariant.weight_unit}
                  </span>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Variants Information */}
          {hasMultipleVariants && (
            <Card>
              <CardHeader>
                <CardTitle>
                  Product Variants ({product.variants?.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {product.variants?.map((variant) => (
                    <div key={variant.id} className="border rounded-lg p-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div>
                          <h4 className="font-medium">
                            {variant.title || "Default Variant"}
                          </h4>
                          <p className="text-sm text-muted-foreground">
                            SKU: {variant.sku || "N/A"}
                          </p>
                          {variant.barcode && (
                            <p className="text-sm text-muted-foreground">
                              Barcode: {variant.barcode}
                            </p>
                          )}
                        </div>
                        <div>
                          <p className="text-lg font-semibold text-primary">
                            ${variant.price?.toFixed(2)}
                            {variant.compare_at_price &&
                              variant.compare_at_price > variant.price && (
                                <span className="ml-2 text-sm text-muted-foreground line-through">
                                  ${variant.compare_at_price.toFixed(2)}
                                </span>
                              )}
                          </p>
                          <p className="text-sm">
                            <span className="font-medium">Stock:</span>
                            <span
                              className={
                                variant.quantity > 0
                                  ? "text-green-600 ml-1"
                                  : "text-red-600 ml-1"
                              }
                            >
                              {variant.quantity}
                            </span>
                          </p>
                          <div className="text-sm">
                            <span className="font-medium">Available:</span>
                            <Badge
                              variant={
                                variant.available_for_sale
                                  ? "default"
                                  : "secondary"
                              }
                              className="ml-1 text-xs"
                            >
                              {variant.available_for_sale ? "Yes" : "No"}
                            </Badge>
                          </div>
                        </div>
                        <div>
                          {variant.weight && (
                            <p className="text-sm">
                              <span className="font-medium">Weight:</span>{" "}
                              {variant.weight}{" "}
                              {variant.weight_unit?.toLowerCase()}
                            </p>
                          )}
                          {variant.cost && (
                            <p className="text-sm">
                              <span className="font-medium">Cost:</span> $
                              {variant.cost.toFixed(2)}
                            </p>
                          )}
                          <div className="text-sm">
                            <span className="font-medium">Taxable:</span>
                            <Badge
                              variant={
                                variant.taxable ? "default" : "secondary"
                              }
                              className="ml-1 text-xs"
                            >
                              {variant.taxable ? "Yes" : "No"}
                            </Badge>
                          </div>
                        </div>
                        <div>
                          <div className="text-sm">
                            <span className="font-medium">Shipping:</span>
                            <Badge
                              variant={
                                variant.requires_shipping
                                  ? "default"
                                  : "secondary"
                              }
                              className="ml-1 text-xs"
                            >
                              {variant.requires_shipping
                                ? "Required"
                                : "Not Required"}
                            </Badge>
                          </div>
                          {variant.fulfillment_service && (
                            <p className="text-sm">
                              <span className="font-medium">Fulfillment:</span>{" "}
                              {variant.fulfillment_service}
                            </p>
                          )}
                          <div className="flex gap-2 mt-1">
                            {variant.option1 && (
                              <Badge variant="outline" className="text-xs">
                                {variant.option1}
                              </Badge>
                            )}
                            {variant.option2 && (
                              <Badge variant="outline" className="text-xs">
                                {variant.option2}
                              </Badge>
                            )}
                            {variant.option3 && (
                              <Badge variant="outline" className="text-xs">
                                {variant.option3}
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Single Variant Information */}
          {!hasMultipleVariants &&
            product.variants &&
            product.variants.length === 1 && (
              <Card>
                <CardHeader>
                  <CardTitle>Variant Details</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div className="space-y-3">
                      <h4 className="font-medium">Pricing & Inventory</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span>Price:</span>
                          <span className="font-semibold text-primary">
                            ${product.variants[0].price?.toFixed(2)}
                          </span>
                        </div>
                        {product.variants[0].compare_at_price && (
                          <div className="flex justify-between">
                            <span>Compare at:</span>
                            <span className="line-through text-muted-foreground">
                              ${product.variants[0].compare_at_price.toFixed(2)}
                            </span>
                          </div>
                        )}
                        {product.variants[0].cost && (
                          <div className="flex justify-between">
                            <span>Cost:</span>
                            <span>${product.variants[0].cost.toFixed(2)}</span>
                          </div>
                        )}
                        <div className="flex justify-between">
                          <span>Stock:</span>
                          <span
                            className={
                              product.variants[0].quantity > 0
                                ? "text-green-600 font-medium"
                                : "text-red-600 font-medium"
                            }
                          >
                            {product.variants[0].quantity}
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <h4 className="font-medium">Physical Properties</h4>
                      <div className="space-y-2 text-sm">
                        {product.variants[0].weight && (
                          <div className="flex justify-between">
                            <span>Weight:</span>
                            <span>
                              {product.variants[0].weight}{" "}
                              {product.variants[0].weight_unit?.toLowerCase()}
                            </span>
                          </div>
                        )}
                        <div className="flex justify-between">
                          <span>Taxable:</span>
                          <Badge
                            variant={
                              product.variants[0].taxable
                                ? "default"
                                : "secondary"
                            }
                            className="text-xs"
                          >
                            {product.variants[0].taxable ? "Yes" : "No"}
                          </Badge>
                        </div>
                        <div className="flex justify-between">
                          <span>Shipping:</span>
                          <Badge
                            variant={
                              product.variants[0].requires_shipping
                                ? "default"
                                : "secondary"
                            }
                            className="text-xs"
                          >
                            {product.variants[0].requires_shipping
                              ? "Required"
                              : "Not Required"}
                          </Badge>
                        </div>
                        {product.variants[0].fulfillment_service && (
                          <div className="flex justify-between">
                            <span>Fulfillment:</span>
                            <span>
                              {product.variants[0].fulfillment_service}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="space-y-3">
                      <h4 className="font-medium">Identification</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span>SKU:</span>
                          <span className="font-mono">
                            {product.variants[0].sku || "N/A"}
                          </span>
                        </div>
                        {product.variants[0].barcode && (
                          <div className="flex justify-between">
                            <span>Barcode:</span>
                            <span className="font-mono">
                              {product.variants[0].barcode}
                            </span>
                          </div>
                        )}
                        <div className="flex justify-between">
                          <span>Available:</span>
                          <Badge
                            variant={
                              product.variants[0].available_for_sale
                                ? "default"
                                : "secondary"
                            }
                            className="text-xs"
                          >
                            {product.variants[0].available_for_sale
                              ? "Yes"
                              : "No"}
                          </Badge>
                        </div>
                        {product.variants[0].inventory_policy && (
                          <div className="flex justify-between">
                            <span>Inventory Policy:</span>
                            <span>{product.variants[0].inventory_policy}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
        </div>

        {/* Right Column */}
        <div className="space-y-6">
          {/* Description */}
          {product.description && (
            <Card>
              <CardHeader>
                <CardTitle>Description</CardTitle>
              </CardHeader>
              <CardContent>
                <div
                  className="prose max-w-none" // Use prose for better markdown rendering if applicable
                  dangerouslySetInnerHTML={{
                    __html:
                      product.description.includes("<") &&
                      product.description.includes(">")
                        ? product.description
                        : product.description.replace(/\n/g, "<br>"),
                  }}
                />
              </CardContent>
            </Card>
          )}

          {/* Featured Media Information */}
          {featuredMedia && (
            <Card>
              <CardHeader>
                <CardTitle>Featured Media</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2 text-sm">
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex justify-between">
                    <span className="font-medium">Type:</span>
                    <Badge
                      variant={
                        featuredMedia.mediaContentType === "VIDEO"
                          ? "default"
                          : "secondary"
                      }
                    >
                      {featuredMedia.mediaContentType}
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-medium">Status:</span>
                    <Badge
                      variant={
                        featuredMedia.status === "READY"
                          ? "default"
                          : "secondary"
                      }
                    >
                      {featuredMedia.status}
                    </Badge>
                  </div>
                </div>
                {featuredMedia.alt && (
                  <div>
                    <span className="font-medium">Alt Text:</span>
                    <p className="text-muted-foreground mt-1">
                      {featuredMedia.alt}
                    </p>
                  </div>
                )}
                {featuredMedia.preview?.image?.width &&
                  featuredMedia.preview?.image?.height && (
                    <div className="flex justify-between">
                      <span className="font-medium">Dimensions:</span>
                      <span>
                        {featuredMedia.preview.image.width} ×{" "}
                        {featuredMedia.preview.image.height}
                      </span>
                    </div>
                  )}
              </CardContent>
            </Card>
          )}

          {/* Product Metadata */}
          <Card>
            <CardHeader>
              <CardTitle>Product Metadata</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <h4 className="font-medium">Basic Information</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Handle:</span>
                      <span className="font-mono text-muted-foreground">
                        {product.handle || "N/A"}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Product Type:</span>
                      <span>{product.product_type || "N/A"}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Vendor:</span>
                      <span>{product.vendor || "N/A"}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Status:</span>
                      <Badge
                        variant={
                          product.status === "active" ? "default" : "secondary"
                        }
                      >
                        {product.status}
                      </Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Published:</span>
                      <Badge
                        variant={product.published ? "default" : "secondary"}
                      >
                        {product.published ? "Yes" : "No"}
                      </Badge>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <h4 className="font-medium">Timestamps</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Created:</span>
                      <span>
                        {new Date(product.created_at).toLocaleDateString()}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Updated:</span>
                      <span>
                        {new Date(product.updated_at).toLocaleDateString()}
                      </span>
                    </div>
                    {product.published_at && (
                      <div className="flex justify-between">
                        <span>Published:</span>
                        <span>
                          {new Date(product.published_at).toLocaleDateString()}
                        </span>
                      </div>
                    )}
                    {product.source_updated_at && (
                      <div className="flex justify-between">
                        <span>Source Updated:</span>
                        <span>
                          {new Date(
                            product.source_updated_at
                          ).toLocaleDateString()}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Tags */}
              {product.tags && product.tags.length > 0 && (
                <div>
                  <h4 className="font-medium mb-2">Tags</h4>
                  <div className="flex flex-wrap gap-2">
                    {product.tags.split(", ").map((tag, index) => (
                      <Badge
                        key={index}
                        variant="secondary"
                        className="text-xs"
                      >
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* Collections */}
              {product.collections && (
                <div>
                  <h4 className="font-medium mb-2">Collections</h4>
                  <p className="text-sm text-muted-foreground">
                    {typeof product.collections === "string"
                      ? product.collections
                      : JSON.stringify(product.collections)}
                  </p>
                </div>
              )}

              {/* Product Options */}
              {product.options && (
                <div>
                  <h4 className="font-medium mb-2">Product Options</h4>
                  <p className="text-sm text-muted-foreground">
                    {typeof product.options === "string"
                      ? product.options
                      : JSON.stringify(product.options)}
                  </p>
                </div>
              )}

              {/* SEO Information */}
              {product.seo && (
                <div>
                  <h4 className="font-medium mb-2">SEO Information</h4>
                  <p className="text-sm text-muted-foreground">
                    {typeof product.seo === "string"
                      ? product.seo
                      : JSON.stringify(product.seo)}
                  </p>
                </div>
              )}

              {/* Metafields */}
              {product.metafields && product.metafields.length > 0 && (
                <div>
                  <h4 className="font-medium mb-2">Custom Metafields</h4>
                  <div className="space-y-2 text-sm">
                    <div className="text-xs text-muted-foreground mb-2">
                      Raw metafields data: {JSON.stringify(product.metafields)}
                    </div>
                    <div className="space-y-4">
                      {/* Group metafields by namespace */}
                      {Object.entries(
                        product.metafields.reduce(
                          (acc: Record<string, any[]>, field) => {
                            const namespace = field.namespace || "default";
                            if (!acc[namespace]) acc[namespace] = [];
                            acc[namespace].push(field);
                            return acc;
                          },
                          {} as Record<string, any[]>
                        )
                      ).map(([namespace, fields]) => (
                        <div key={namespace} className="border rounded-lg p-3">
                          <h5 className="font-medium text-sm mb-2 capitalize">
                            {namespace} Fields
                          </h5>
                          {!fields || fields.length === 0 ? (
                            <p className="text-muted-foreground text-sm">
                              No fields in this namespace
                            </p>
                          ) : (
                            <div className="space-y-2">
                              {fields.map((field, index) => (
                                <div key={index} className="text-sm">
                                  <span className="font-medium">
                                    {field.key}:
                                  </span>
                                  <span className="text-muted-foreground ml-2">
                                    {field.value_type ===
                                      "multi_line_text_field" &&
                                    field.value?.includes?.("http") ? (
                                      <a
                                        href={field.value}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="text-blue-600 hover:underline"
                                      >
                                        {field.value}
                                      </a>
                                    ) : (
                                      field.value || "No value"
                                    )}
                                  </span>
                                  {field.updated_at && (
                                    <span className="text-xs text-muted-foreground ml-2">
                                      (Updated:{" "}
                                      {new Date(
                                        field.updated_at
                                      ).toLocaleDateString()}
                                      )
                                    </span>
                                  )}
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Customer Reviews */}
          <Card>
            <CardHeader>
              <CardTitle>Customer Reviews</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                No reviews yet. Be the first to review this product!
              </p>
              {/* Future: Add review submission form and display existing reviews */}
            </CardContent>
          </Card>

          {/* Related Products */}
          <Card>
            <CardHeader>
              <CardTitle>Related Products</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Check back later for related products.
              </p>
              {/* Future: Display a carousel or grid of related products */}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Shopify Integration */}
      {(product.shopify_product_id || product.shopify_handle) && (
        <Card>
          <CardHeader>
            <CardTitle>Shopify Integration</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2 text-sm">
            {product.shopify_product_id && (
              <div className="flex justify-between">
                <span className="font-medium">Shopify Product ID:</span>
                <span className="font-mono">{product.shopify_product_id}</span>
              </div>
            )}
            {product.shopify_handle && (
              <div className="flex justify-between">
                <span className="font-medium">Shopify Handle:</span>
                <span className="font-mono">{product.shopify_handle}</span>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Media Viewer Component */}
      <MediaViewer
        isOpen={isImageModalOpen}
        onClose={() => setIsImageModalOpen(false)}
        mediaItems={allMedia}
        initialIndex={selectedImageIndex}
      />
    </div>
  );
};

export default ProductDetailPage;
