'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Users, ArrowLeft, Edit, Mail, Phone, MapPin, DollarSign, ShoppingCart, Calendar } from 'lucide-react';
import { customerService, Customer } from '@/services/customerService';
import { Loader2 } from 'lucide-react';

const CustomerDetailPage: React.FC = () => {
  const params = useParams();
  const router = useRouter();
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCustomer = async () => {
      if (!params.id) return;

      try {
        setLoading(true);
        const data = await customerService.getCustomer(params.id as string);
        setCustomer(data);
        setError(null);
      } catch (err) {
        console.error('Failed to fetch customer:', err);
        setError('Failed to load customer details. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchCustomer();
  }, [params.id]);

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading customer...</span>
        </div>
      </div>
    );
  }

  if (error || !customer) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Customers
          </Button>
        </div>
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <p className="text-red-600 mb-4">{error || 'Customer not found'}</p>
            <Button onClick={() => router.back()}>
              Back to Customers
            </Button>
          </div>
        </div>
      </div>
    );
  }

  const fullName = customer.first_name && customer.last_name
    ? `${customer.first_name} ${customer.last_name}`
    : customer.first_name || customer.last_name || 'Unknown Customer';

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Customers
          </Button>
          <div>
            <h1 className="text-3xl font-bold">{fullName}</h1>
            <p className="text-muted-foreground">Customer Details</p>
          </div>
        </div>
        <Button>
          <Edit className="mr-2 h-4 w-4" />
          Edit Customer
        </Button>
      </div>

      {/* Customer Overview */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="mr-2 h-5 w-5" />
              Customer Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Name:</span>
              <span>{fullName}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Email:</span>
              <span>{customer.email}</span>
            </div>
            {customer.phone && (
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Phone:</span>
                <span>{customer.phone}</span>
              </div>
            )}
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Status:</span>
              <Badge variant={customer.state === 'enabled' ? 'default' : 'secondary'}>
                {customer.state === 'enabled' ? 'Active' : 'Inactive'}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Total Spent:</span>
              <span className="font-semibold">${customer.total_spent.toFixed(2)}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Orders Count:</span>
              <span>{customer.orders_count}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="mr-2 h-5 w-5" />
              Account Details
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Customer ID:</span>
              <span className="font-mono text-sm">{customer.id}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">External ID:</span>
              <span className="font-mono text-sm">{customer.external_id}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Accepts Marketing:</span>
              <Badge variant={customer.accepts_marketing ? 'default' : 'secondary'}>
                {customer.accepts_marketing ? 'Yes' : 'No'}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Email Verified:</span>
              <Badge variant={customer.verified_email ? 'default' : 'secondary'}>
                {customer.verified_email ? 'Verified' : 'Unverified'}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Tax Exempt:</span>
              <Badge variant={customer.tax_exempt ? 'default' : 'secondary'}>
                {customer.tax_exempt ? 'Yes' : 'No'}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Created:</span>
              <span className="text-sm">
                {new Date(customer.created_at).toLocaleDateString()}
              </span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Contact Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Mail className="mr-2 h-5 w-5" />
            Contact Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">{customer.email}</span>
              </div>
              {customer.phone && (
                <div className="flex items-center space-x-2">
                  <Phone className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">{customer.phone}</span>
                </div>
              )}
            </div>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <ShoppingCart className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">{customer.orders_count} orders</span>
              </div>
              <div className="flex items-center space-x-2">
                <DollarSign className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">${customer.total_spent.toFixed(2)} total spent</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default CustomerDetailPage;