"use client";

import React, { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { toast } from "sonner";
import {
  Globe,
  Download,
  Trash2,
  Search,
  RefreshCw,
  ExternalLink,
  Database,
  Cloud,
  AlertCircle,
  CheckCircle,
  Clock,
  Play,
  Pause,
  Settings,
  Eye,
  MoreHorizontal,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { scraperService, ScrapedDocument, ScrapingJob, ScraperStats } from "@/services/scraperService";

const ScraperPage: React.FC = () => {
  const [dataMode, setDataMode] = useState<"synced" | "scraped">("synced");
  const [scrapedDocuments, setScrapedDocuments] = useState<ScrapedDocument[]>([]);
  const [activeJobs, setActiveJobs] = useState<ScrapingJob[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  
  // Scraping form state
  const [newUrl, setNewUrl] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Stats
  const [stats, setStats] = useState({
    totalDocuments: 0,
    totalProducts: 0,
    totalDomains: 0,
    activeJobs: 0,
  });

  // Load scraped documents and stats
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        
        // Fetch scraped documents
        const docsData = await scraperService.getDocuments();
        setScrapedDocuments(docsData.items || []);
        
        // Fetch active jobs
        const jobsData = await scraperService.getActiveJobs();
        setActiveJobs(jobsData.items || []);
        
        // Fetch stats
        const statsData = await scraperService.getStats();
        setStats(statsData);
      } catch (error) {
        console.error("Failed to fetch scraper data:", error);
        toast.error("Failed to load scraper data");
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
    
    // Poll for updates every 5 seconds
    const interval = setInterval(fetchData, 5000);
    return () => clearInterval(interval);
  }, []);

  // Filter documents based on search
  const filteredDocuments = React.useMemo(() => {
    if (!searchQuery.trim()) return scrapedDocuments;
    const query = searchQuery.toLowerCase();
    return scrapedDocuments.filter(doc =>
      doc.url.toLowerCase().includes(query) ||
      doc.domain.toLowerCase().includes(query) ||
      doc.title.toLowerCase().includes(query)
    );
  }, [scrapedDocuments, searchQuery]);

  const handleSubmitUrl = async () => {
    if (!newUrl.trim()) {
      toast.error("Please enter a valid URL");
      return;
    }

    try {
      setIsSubmitting(true);
      
      const result = await scraperService.startScraping(newUrl.trim());
      toast.success(`Scraping job started: ${result.job_id}`);
      setNewUrl("");
      
      
    } catch (error) {
      console.error("Failed to start scraping:", error);
      toast.error("Failed to start scraping job");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteDocument = async (documentId: string) => {
    try {
      await scraperService.deleteDocument(documentId);

      toast.success("Document deleted successfully");
      setScrapedDocuments(prev => prev.filter(doc => doc.id !== documentId));
    } catch (error) {
      console.error("Failed to delete document:", error);
      toast.error("Failed to delete document");
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "failed":
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case "scraping":
      case "running":
        return <RefreshCw className="h-4 w-4 text-blue-500 animate-spin" />;
      default:
        return <Clock className="h-4 w-4 text-yellow-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";
      case "failed":
        return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400";
      case "scraping":
      case "running":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400";
      default:
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400";
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Page Description and Controls */}
      <div className="flex items-center justify-between">
        <div>
          <p className="text-muted-foreground">Manage scraped product data and sync with stores</p>
        </div>
        <div className="flex items-center gap-4">
          <div className="flex items-center space-x-2">
            <Database className="h-4 w-4" />
            <Label htmlFor="data-mode">Synced Data</Label>
            <Switch
              id="data-mode"
              checked={dataMode === "scraped"}
              onCheckedChange={(checked) => setDataMode(checked ? "scraped" : "synced")}
            />
            <Label htmlFor="data-mode">Scraped Data</Label>
            <Globe className="h-4 w-4" />
          </div>
        </div>
      </div>

      {/* Data Mode Alert */}
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          {dataMode === "synced" 
            ? "Viewing synchronized data from connected stores. Switch to 'Scraped Data' to see web-scraped content."
            : "Viewing scraped data from web sources. Switch to 'Synced Data' to see store-synchronized content."
          }
        </AlertDescription>
      </Alert>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Documents</CardTitle>
            <Globe className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalDocuments}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Products</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalProducts}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Domains</CardTitle>
            <ExternalLink className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalDomains}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Jobs</CardTitle>
            <RefreshCw className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.activeJobs}</div>
          </CardContent>
        </Card>
      </div>

      {dataMode === "scraped" && (
        <>
          {/* Add New URL */}
          <Card>
            <CardHeader>
              <CardTitle>Add New URL to Scrape</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-4">
                <div className="flex-1">
                  <Label htmlFor="new-url">Website URL</Label>
                  <Input
                    id="new-url"
                    placeholder="https://example-store.com"
                    value={newUrl}
                    onChange={(e) => setNewUrl(e.target.value)}
                    onKeyDown={(e) => e.key === "Enter" && handleSubmitUrl()}
                  />
                </div>
                <div className="flex items-end">
                  <Button onClick={handleSubmitUrl} disabled={isSubmitting}>
                    {isSubmitting ? (
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Play className="h-4 w-4 mr-2" />
                    )}
                    Start Scraping
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Active Jobs */}
          {activeJobs.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Active Scraping Jobs</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {activeJobs.map((job) => (
                    <div key={job.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-3">
                        {getStatusIcon(job.status)}
                        <div>
                          <p className="font-medium">{job.url}</p>
                          <p className="text-sm text-muted-foreground">
                            {job.status === "running" ? "Scraping in progress..." : `Status: ${job.status}`}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <Badge className={getStatusColor(job.status)}>
                          {job.status}
                        </Badge>
                        {job.status === "running" && (
                          <Progress value={job.progress} className="w-24 mt-2" />
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Search */}
          <div className="flex items-center gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search scraped documents..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {/* Scraped Documents */}
          <Card>
            <CardHeader>
              <CardTitle>Scraped Documents</CardTitle>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex items-center justify-center h-32">
                  <RefreshCw className="h-6 w-6 animate-spin" />
                  <span className="ml-2">Loading documents...</span>
                </div>
              ) : filteredDocuments.length === 0 ? (
                <div className="text-center py-8">
                  <Globe className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">No scraped documents found</p>
                  <p className="text-sm text-muted-foreground">Add a URL above to start scraping</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredDocuments.map((doc) => (
                    <div key={doc.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-3">
                        {getStatusIcon(doc.status)}
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <p className="font-medium">{doc.title || doc.domain}</p>
                            <a
                              href={doc.url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-muted-foreground hover:text-primary"
                            >
                              <ExternalLink className="h-4 w-4" />
                            </a>
                          </div>
                          <p className="text-sm text-muted-foreground">{doc.url}</p>
                          <div className="flex items-center gap-4 mt-1">
                            <span className="text-xs text-muted-foreground">
                              {doc.productCount} products
                            </span>
                            <span className="text-xs text-muted-foreground">
                              {doc.collectionCount} collections
                            </span>
                            <span className="text-xs text-muted-foreground">
                              {new Date(doc.createdAt).toLocaleDateString()}
                            </span>
                          </div>
                          {doc.error && (
                            <p className="text-sm text-red-500 mt-1">{doc.error}</p>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge className={getStatusColor(doc.status)}>
                          {doc.status}
                        </Badge>
                        {doc.status === "scraping" && (
                          <Progress value={doc.progress} className="w-24" />
                        )}
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleDeleteDocument(doc.id)}
                          className="text-red-500 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </>
      )}

      {dataMode === "synced" && (
        <Card>
          <CardHeader>
            <CardTitle>Synchronized Store Data</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-8">
              <Database className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">Synchronized data from connected stores</p>
              <p className="text-sm text-muted-foreground">
                This view shows products and data synchronized from your connected Shopify stores
              </p>
              <Button className="mt-4" asChild>
                <a href="/stores">Manage Stores</a>
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default ScraperPage;
