'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Webhook, CheckCircle, XCircle, Clock, AlertTriangle } from 'lucide-react';

const WebhookMonitor: React.FC = () => {
  const webhooks = [
    { id: 1, endpoint: '/api/webhooks/orders', method: 'POST', status: 'Success', timestamp: '2 minutes ago', responseTime: '145ms' },
    { id: 2, endpoint: '/api/webhooks/products', method: 'PUT', status: 'Success', timestamp: '5 minutes ago', responseTime: '89ms' },
    { id: 3, endpoint: '/api/webhooks/inventory', method: 'POST', status: 'Failed', timestamp: '8 minutes ago', responseTime: 'Timeout' },
    { id: 4, endpoint: '/api/webhooks/customers', method: 'POST', status: 'Pending', timestamp: '10 minutes ago', responseTime: '-' },
    { id: 5, endpoint: '/api/webhooks/orders', method: 'POST', status: 'Success', timestamp: '15 minutes ago', responseTime: '234ms' },
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'Failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'Pending':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      default:
        return <AlertTriangle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Success':
        return 'bg-green-100 text-green-800';
      case 'Failed':
        return 'bg-red-100 text-red-800';
      case 'Pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Webhook Monitor</h1>
        <p className="text-muted-foreground">Monitor webhook deliveries and responses</p>
      </div>

      {/* Stats */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Webhooks</p>
                <p className="text-2xl font-bold">1,234</p>
              </div>
              <Webhook className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Successful</p>
                <p className="text-2xl font-bold text-green-600">1,156</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Failed</p>
                <p className="text-2xl font-bold text-red-600">67</p>
              </div>
              <XCircle className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Pending</p>
                <p className="text-2xl font-bold text-yellow-600">11</p>
              </div>
              <Clock className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Webhooks */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Webhook Deliveries</CardTitle>
          <CardDescription>Latest webhook delivery attempts and their status</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {webhooks.map((webhook) => (
              <div key={webhook.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-4">
                  {getStatusIcon(webhook.status)}
                  <div>
                    <p className="font-medium">{webhook.endpoint}</p>
                    <p className="text-sm text-muted-foreground">{webhook.method} • {webhook.timestamp}</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-4">
                  <div className="text-right">
                    <p className="text-sm text-muted-foreground">Response Time</p>
                    <p className="font-medium">{webhook.responseTime}</p>
                  </div>
                  
                  <span className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${getStatusColor(webhook.status)}`}>
                    {webhook.status}
                  </span>
                  
                  <Button variant="outline" size="sm">
                    View Details
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Webhook Configuration */}
      <Card>
        <CardHeader>
          <CardTitle>Webhook Configuration</CardTitle>
          <CardDescription>Configure webhook endpoints and settings</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">Webhook URL</label>
              <input 
                type="url" 
                placeholder="https://your-domain.com/webhooks" 
                className="w-full mt-1 p-2 border rounded-md"
              />
            </div>
            
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <label className="text-sm font-medium">Retry Attempts</label>
                <select className="w-full mt-1 p-2 border rounded-md">
                  <option>3 attempts</option>
                  <option>5 attempts</option>
                  <option>10 attempts</option>
                </select>
              </div>
              
              <div>
                <label className="text-sm font-medium">Timeout (seconds)</label>
                <input 
                  type="number" 
                  placeholder="30" 
                  className="w-full mt-1 p-2 border rounded-md"
                />
              </div>
            </div>
            
            <div className="flex justify-end space-x-2">
              <Button variant="outline">Test Webhook</Button>
              <Button>Save Configuration</Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default WebhookMonitor;
