'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Search, 
  ChevronDown, 
  ChevronUp,
  HelpCircle,
  MessageCircle,
  Mail,
  Phone
} from 'lucide-react';
import useAnalytics from '@/hooks/useAnalytics';
import useNavigationTracking from '@/hooks/useNavigationTracking';

const FAQPage = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [openItems, setOpenItems] = useState<number[]>([]);
  const [selectedCategory, setSelectedCategory] = useState('all');
  
  const { trackEvent } = useAnalytics();
  const { trackSearchUsage } = useNavigationTracking({ section: 'FAQ' });

  const toggleItem = (index: number) => {
    setOpenItems(prev => 
      prev.includes(index) 
        ? prev.filter(i => i !== index)
        : [...prev, index]
    );
    
    trackEvent({
      action: 'faq_toggle',
      category: 'FAQ',
      label: `FAQ Item ${index} ${openItems.includes(index) ? 'Closed' : 'Opened'}`,
    });
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      trackSearchUsage(searchQuery, 0);
      trackEvent({
        action: 'faq_search',
        category: 'FAQ',
        label: `Search: ${searchQuery}`,
      });
    }
  };

  const categories = [
    { id: 'all', name: 'All Questions', count: 24 },
    { id: 'getting-started', name: 'Getting Started', count: 8 },
    { id: 'products', name: 'Products', count: 6 },
    { id: 'videos', name: 'Video Generation', count: 5 },
    { id: 'billing', name: 'Billing', count: 3 },
    { id: 'technical', name: 'Technical', count: 2 },
  ];

  const faqItems = [
    {
      category: 'getting-started',
      question: 'How do I get started with the platform?',
      answer: 'Getting started is easy! First, create your account and verify your email. Then, connect your Shopify store or add products manually. You can start generating videos for your products right away using our AI-powered video generation tools.',
      popular: true
    },
    {
      category: 'getting-started',
      question: 'Do I need a Shopify store to use this platform?',
      answer: 'No, you don\'t need a Shopify store. While we offer seamless Shopify integration, you can also add products manually and use all our features without any external store connection.',
      popular: true
    },
    {
      category: 'products',
      question: 'How many products can I add?',
      answer: 'The number of products you can add depends on your plan. Our Starter plan allows up to 100 products, Professional plan allows up to 1,000 products, and Enterprise plan offers unlimited products.',
      popular: false
    },
    {
      category: 'videos',
      question: 'How long does it take to generate a video?',
      answer: 'Video generation typically takes 2-5 minutes depending on the complexity and length of the video. You\'ll receive a notification when your video is ready, and you can continue working on other tasks while it processes.',
      popular: true
    },
    {
      category: 'videos',
      question: 'Can I customize the video templates?',
      answer: 'Yes! Our video templates are fully customizable. You can modify colors, fonts, animations, music, and even add your own branding elements. You can also create completely custom videos from scratch.',
      popular: false
    },
    {
      category: 'videos',
      question: 'What video formats are supported?',
      answer: 'We support MP4, MOV, and WebM formats for output. Videos are optimized for social media platforms, websites, and e-commerce stores. You can also choose different aspect ratios like 16:9, 1:1, and 9:16.',
      popular: false
    },
    {
      category: 'billing',
      question: 'Can I change my plan at any time?',
      answer: 'Yes, you can upgrade or downgrade your plan at any time. When you upgrade, you\'ll be charged the prorated amount immediately. When you downgrade, the change will take effect at the end of your current billing cycle.',
      popular: true
    },
    {
      category: 'billing',
      question: 'Do you offer refunds?',
      answer: 'We offer a 30-day money-back guarantee for all new subscriptions. If you\'re not satisfied with our service within the first 30 days, contact our support team for a full refund.',
      popular: false
    },
    {
      category: 'technical',
      question: 'Is my data secure?',
      answer: 'Absolutely. We use enterprise-grade security measures including SSL encryption, regular security audits, and SOC 2 compliance. Your data is stored securely and we never share it with third parties.',
      popular: true
    },
    {
      category: 'technical',
      question: 'Do you have an API?',
      answer: 'Yes, we offer a comprehensive REST API that allows you to integrate our video generation capabilities into your own applications. API documentation is available in our developer portal.',
      popular: false
    },
    {
      category: 'products',
      question: 'Can I import products from other platforms?',
      answer: 'Currently, we support direct integration with Shopify and CSV import. We\'re working on adding support for other platforms like WooCommerce, Magento, and BigCommerce.',
      popular: false
    },
    {
      category: 'getting-started',
      question: 'Is there a mobile app?',
      answer: 'We don\'t have a dedicated mobile app yet, but our web platform is fully responsive and works great on mobile devices. You can access all features from your mobile browser.',
      popular: false
    },
  ];

  const filteredFAQs = faqItems.filter(item => {
    const matchesCategory = selectedCategory === 'all' || item.category === selectedCategory;
    const matchesSearch = searchQuery === '' || 
      item.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.answer.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  const popularFAQs = faqItems.filter(item => item.popular);

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold mb-4">Frequently Asked Questions</h1>
        <p className="text-xl text-muted-foreground mb-6">
          Find quick answers to common questions about our platform
        </p>
        
        {/* Search */}
        <form onSubmit={handleSearch} className="max-w-md mx-auto">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              type="text"
              placeholder="Search FAQs..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 pr-4 py-2"
            />
          </div>
        </form>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Categories Sidebar */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Categories</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`w-full text-left p-3 rounded-lg transition-colors ${
                    selectedCategory === category.id
                      ? 'bg-primary text-primary-foreground'
                      : 'hover:bg-muted'
                  }`}
                >
                  <div className="flex justify-between items-center">
                    <span className="font-medium">{category.name}</span>
                    <Badge variant={selectedCategory === category.id ? 'secondary' : 'outline'}>
                      {category.count}
                    </Badge>
                  </div>
                </button>
              ))}
            </CardContent>
          </Card>

          {/* Contact Support */}
          <Card className="mt-6">
            <CardHeader>
              <CardTitle className="text-lg">Still Need Help?</CardTitle>
              <CardDescription>
                Can't find what you're looking for?
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button variant="outline" size="sm" className="w-full justify-start gap-2">
                <MessageCircle className="h-4 w-4" />
                Live Chat
              </Button>
              <Button variant="outline" size="sm" className="w-full justify-start gap-2">
                <Mail className="h-4 w-4" />
                Email Support
              </Button>
              <Button variant="outline" size="sm" className="w-full justify-start gap-2">
                <Phone className="h-4 w-4" />
                Call Us
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* FAQ Content */}
        <div className="lg:col-span-3">
          {/* Popular Questions */}
          {selectedCategory === 'all' && searchQuery === '' && (
            <div className="mb-8">
              <h2 className="text-2xl font-bold mb-4 flex items-center gap-2">
                <HelpCircle className="h-6 w-6" />
                Popular Questions
              </h2>
              <div className="space-y-4">
                {popularFAQs.slice(0, 3).map((item, index) => (
                  <Card key={`popular-${index}`} className="border-l-4 border-l-primary">
                    <CardHeader 
                      className="cursor-pointer"
                      onClick={() => toggleItem(`popular-${index}` as any)}
                    >
                      <div className="flex justify-between items-center">
                        <CardTitle className="text-lg">{item.question}</CardTitle>
                        {openItems.includes(`popular-${index}` as any) ? (
                          <ChevronUp className="h-5 w-5" />
                        ) : (
                          <ChevronDown className="h-5 w-5" />
                        )}
                      </div>
                    </CardHeader>
                    {openItems.includes(`popular-${index}` as any) && (
                      <CardContent>
                        <p className="text-muted-foreground">{item.answer}</p>
                      </CardContent>
                    )}
                  </Card>
                ))}
              </div>
            </div>
          )}

          {/* All Questions */}
          <div>
            <h2 className="text-2xl font-bold mb-4">
              {selectedCategory === 'all' ? 'All Questions' : categories.find(c => c.id === selectedCategory)?.name}
              <Badge variant="secondary" className="ml-2">
                {filteredFAQs.length}
              </Badge>
            </h2>
            
            {filteredFAQs.length === 0 ? (
              <Card>
                <CardContent className="text-center py-8">
                  <HelpCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">No questions found</h3>
                  <p className="text-muted-foreground">
                    Try adjusting your search or browse different categories
                  </p>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-4">
                {filteredFAQs.map((item, index) => (
                  <Card key={index}>
                    <CardHeader 
                      className="cursor-pointer"
                      onClick={() => toggleItem(index)}
                    >
                      <div className="flex justify-between items-center">
                        <CardTitle className="text-lg">{item.question}</CardTitle>
                        <div className="flex items-center gap-2">
                          {item.popular && (
                            <Badge variant="secondary" className="text-xs">Popular</Badge>
                          )}
                          {openItems.includes(index) ? (
                            <ChevronUp className="h-5 w-5" />
                          ) : (
                            <ChevronDown className="h-5 w-5" />
                          )}
                        </div>
                      </div>
                    </CardHeader>
                    {openItems.includes(index) && (
                      <CardContent>
                        <p className="text-muted-foreground">{item.answer}</p>
                      </CardContent>
                    )}
                  </Card>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default FAQPage;
