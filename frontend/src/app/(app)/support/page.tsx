"use client";

import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  MessageCircle,
  Mail,
  Phone,
  Clock,
  CheckCircle,
  AlertCircle,
  HelpCircle,
  Send,
  FileText,
  Video,
  Zap,
  MapPin,
  Calendar,
  Building,
} from "lucide-react";
import useAnalytics from "@/hooks/useAnalytics";
import useFormTracking from "@/hooks/useFormTracking";
import useBookDemo from "@/hooks/useBookDemo";

const SupportPage = () => {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    company: "",
    phone: "",
    subject: "",
    category: "",
    priority: "",
    inquiryType: "",
    message: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { trackEvent } = useAnalytics();
  const { trackFormStart, trackFormSubmit, trackFieldFocus } = useFormTracking({
    formName: "Support Request",
  });
  const { handleBookDemoClick } = useBookDemo({
    label: "Support Page - Schedule Demo Button",
    redirectTo: "/demo",
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Simulate form submission
      await new Promise((resolve) => setTimeout(resolve, 2000));

      trackFormSubmit(true);
      trackEvent({
        action: "support_ticket_submitted",
        category: "Support",
        label: `Category: ${formData.category}, Priority: ${formData.priority}`,
      });

      // Reset form
      setFormData({
        name: "",
        email: "",
        company: "",
        phone: "",
        subject: "",
        category: "",
        priority: "",
        inquiryType: "",
        message: "",
      });

      alert("Support ticket submitted successfully!");
    } catch (error) {
      trackFormSubmit(false);
      alert("Error submitting support ticket. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const supportChannels = [
    {
      title: "Live Chat",
      description: "Get instant help from our support team",
      icon: MessageCircle,
      availability: "Available 24/7",
      responseTime: "Instant",
      color: "bg-green-500",
      action: "Start Chat",
      href: "/support/chat",
    },
    {
      title: "Email Support",
      description: "Send us a detailed message",
      icon: Mail,
      availability: "Always available",
      responseTime: "Within 24 hours",
      color: "bg-blue-500",
      action: "Send Email",
      href: "/support/email",
    },
    {
      title: "Phone Support",
      description: "Speak directly with our team",
      icon: Phone,
      availability: "Mon-Fri 9AM-6PM EST",
      responseTime: "Immediate",
      color: "bg-purple-500",
      action: "Call Now",
      href: "tel:******-0123",
    },
  ];

  const quickActions = [
    {
      title: "Check System Status",
      description: "View current system status and outages",
      icon: CheckCircle,
      color: "text-green-500",
      href: "/status",
    },
    {
      title: "Browse Documentation",
      description: "Find answers in our comprehensive docs",
      icon: FileText,
      color: "text-blue-500",
      href: "/docs",
    },
    {
      title: "Watch Tutorials",
      description: "Learn with step-by-step video guides",
      icon: Video,
      color: "text-purple-500",
      href: "/tutorials",
    },
    {
      title: "Feature Requests",
      description: "Suggest new features or improvements",
      icon: Zap,
      color: "text-orange-500",
      href: "/feature-requests",
    },
    {
      title: "Community Forum",
      description: "Connect with other users and share knowledge",
      icon: MessageCircle,
      color: "text-indigo-500",
      href: "/community",
    },
  ];

  const handleQuickActionClick = (title: string, href: string) => {
    trackEvent({
      action: "quick_action_click",
      category: "Support",
      label: `${title} - ${href}`,
    });
  };

  const contactInfo = [
    {
      title: "Email Us",
      description: "Send us an email anytime",
      icon: Mail,
      value: "<EMAIL>",
      action: "mailto:<EMAIL>",
    },
    {
      title: "Call Us",
      description: "Mon-Fri 9AM-6PM EST",
      icon: Phone,
      value: "+****************",
      action: "tel:+***********",
    },
    {
      title: "Visit Us",
      description: "Our headquarters",
      icon: MapPin,
      value: "123 Business Ave, Suite 100\nSan Francisco, CA 94105",
      action: "https://maps.google.com",
    },
    {
      title: "Business Hours",
      description: "When we're available",
      icon: Clock,
      value: "Mon-Fri: 9AM-6PM EST\nSat-Sun: Closed",
      action: null,
    },
  ];

  const handleContactClick = (title: string, action: string | null) => {
    trackEvent({
      action: "contact_info_click",
      category: "Support",
      label: title,
    });

    if (action) {
      window.open(action, "_blank");
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      <div className="container mx-auto px-4 py-12">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-primary/10 rounded-full mb-6">
            <HelpCircle className="h-8 w-8 text-primary" />
          </div>
          <h1 className="text-5xl font-bold mb-4 bg-gradient-to-r from-slate-900 to-slate-600 bg-clip-text text-transparent">
            Support Center
          </h1>
          <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto leading-relaxed">
            We're here to help you succeed. Choose how you'd like to get support.
          </p>
          <div className="flex justify-center">
            <Button onClick={handleBookDemoClick} size="lg" className="gap-2 shadow-lg hover:shadow-xl transition-shadow">
              <Calendar className="h-5 w-5" />
              Schedule a Demo
            </Button>
          </div>
        </div>

      {/* Support Channels */}
      <div className="mb-16">
        <div className="text-center mb-10">
          <h2 className="text-3xl font-bold mb-4">Contact Support</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Choose the support channel that works best for you
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {supportChannels.map((channel) => (
            <Card key={channel.title} className="text-center hover:shadow-lg transition-shadow duration-300 border-0 shadow-md">
              <CardHeader className="pb-4">
                <div
                  className={`mx-auto mb-6 p-4 rounded-full w-fit ${channel.color} shadow-lg`}
                >
                  <channel.icon className="h-8 w-8 text-white" />
                </div>
                <CardTitle className="text-xl mb-2">{channel.title}</CardTitle>
                <CardDescription className="text-base">{channel.description}</CardDescription>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="space-y-3 mb-6">
                  <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
                    <Clock className="h-4 w-4" />
                    {channel.availability}
                  </div>
                  <Badge variant="secondary" className="bg-primary/10 text-primary border-primary/20">
                    Response: {channel.responseTime}
                  </Badge>
                </div>
                <Button
                  className="w-full shadow-md hover:shadow-lg transition-shadow"
                  onClick={() =>
                    handleQuickActionClick(channel.title, channel.href)
                  }
                >
                  {channel.action}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
        {/* Support Form */}
        <div className="xl:col-span-2">
          <Card className="shadow-lg border-0">
            <CardHeader className="bg-gradient-to-r from-primary/5 to-primary/10 rounded-t-lg">
              <CardTitle className="flex items-center gap-3 text-2xl">
                <div className="p-2 bg-primary/10 rounded-lg">
                  <Send className="h-6 w-6 text-primary" />
                </div>
                Submit a Support Ticket
              </CardTitle>
              <CardDescription className="text-base mt-2">
                Describe your issue in detail and we'll get back to you as soon as possible
              </CardDescription>
            </CardHeader>
            <CardContent className="p-8">
              <form onSubmit={handleSubmit} className="space-y-8">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="name">Full Name *</Label>
                    <Input
                      id="name"
                      type="text"
                      value={formData.name}
                      onChange={(e) =>
                        handleInputChange("name", e.target.value)
                      }
                      onFocus={() => trackFieldFocus("name")}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="email">Email Address *</Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) =>
                        handleInputChange("email", e.target.value)
                      }
                      onFocus={() => trackFieldFocus("email")}
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="company">Company</Label>
                    <Input
                      id="company"
                      type="text"
                      value={formData.company}
                      onChange={(e) =>
                        handleInputChange("company", e.target.value)
                      }
                      onFocus={() => trackFieldFocus("company")}
                    />
                  </div>
                  <div>
                    <Label htmlFor="phone">Phone Number</Label>
                    <Input
                      id="phone"
                      type="tel"
                      value={formData.phone}
                      onChange={(e) =>
                        handleInputChange("phone", e.target.value)
                      }
                      onFocus={() => trackFieldFocus("phone")}
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="subject">Subject *</Label>
                  <Input
                    id="subject"
                    type="text"
                    value={formData.subject}
                    onChange={(e) =>
                      handleInputChange("subject", e.target.value)
                    }
                    onFocus={() => trackFieldFocus("subject")}
                    placeholder="Brief description of your issue"
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="inquiryType">Inquiry Type *</Label>
                  <Select
                    value={formData.inquiryType}
                    onValueChange={(value) =>
                      handleInputChange("inquiryType", value)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="What can we help you with?" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="demo">Schedule a Demo</SelectItem>
                      <SelectItem value="sales">Sales Inquiry</SelectItem>
                      <SelectItem value="support">Technical Support</SelectItem>
                      <SelectItem value="partnership">Partnership</SelectItem>
                      <SelectItem value="billing">Billing Question</SelectItem>
                      <SelectItem value="general">General Question</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="category">Category *</Label>
                    <Select
                      value={formData.category}
                      onValueChange={(value) =>
                        handleInputChange("category", value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select a category" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="technical">
                          Technical Issue
                        </SelectItem>
                        <SelectItem value="billing">
                          Billing & Account
                        </SelectItem>
                        <SelectItem value="feature">Feature Request</SelectItem>
                        <SelectItem value="integration">
                          Integration Help
                        </SelectItem>
                        <SelectItem value="general">
                          General Question
                        </SelectItem>
                        <SelectItem value="bug">Bug Report</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="priority">Priority *</Label>
                    <Select
                      value={formData.priority}
                      onValueChange={(value) =>
                        handleInputChange("priority", value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select priority" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="low">Low</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="high">High</SelectItem>
                        <SelectItem value="urgent">Urgent</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Label htmlFor="message">Message *</Label>
                  <Textarea
                    id="message"
                    value={formData.message}
                    onChange={(e) =>
                      handleInputChange("message", e.target.value)
                    }
                    onFocus={() => trackFieldFocus("message")}
                    placeholder="Please provide as much detail as possible about your issue..."
                    rows={6}
                    required
                  />
                </div>

                <Button
                  type="submit"
                  className="w-full h-12 text-lg font-medium shadow-lg hover:shadow-xl transition-shadow"
                  disabled={isSubmitting}
                  onClick={() => trackFormStart()}
                >
                  {isSubmitting ? "Submitting..." : "Submit Support Ticket"}
                </Button>
              </form>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions & Info */}
        <div className="space-y-6">
          {/* Quick Actions */}
          <Card className="shadow-md border-0">
            <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-lg">
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5 text-blue-600" />
                Quick Actions
              </CardTitle>
              <CardDescription>
                Common tasks and helpful resources
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              {quickActions.map((action) => (
                <button
                  key={action.title}
                  onClick={() =>
                    handleQuickActionClick(action.title, action.href)
                  }
                  className="w-full text-left p-2 rounded-md hover:bg-muted/50 transition-colors"
                >
                  <div className="flex items-start gap-2">
                    <action.icon className={`h-4 w-4 mt-0.5 flex-shrink-0 ${action.color}`} />
                    <div className="min-w-0">
                      <h4 className="font-medium text-sm">{action.title}</h4>
                      <p className="text-xs text-muted-foreground">
                        {action.description}
                      </p>
                    </div>
                  </div>
                </button>
              ))}
            </CardContent>
          </Card>


          {/* Office Information */}
          <Card className="shadow-md border-0">
            <CardHeader className="bg-gradient-to-r from-purple-50 to-violet-50 rounded-t-lg">
              <CardTitle className="flex items-center gap-2">
                <Building className="h-5 w-5 text-purple-600" />
                Our Office
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div>
                  <h4 className="font-medium text-sm">Headquarters</h4>
                  <p className="text-sm text-muted-foreground">
                    123 Business Avenue
                    <br />
                    Suite 100
                    <br />
                    San Francisco, CA 94105
                    <br />
                    United States
                  </p>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full"
                  onClick={() =>
                    handleContactClick("View on Map", "https://maps.google.com")
                  }
                >
                  View on Map
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* System Status */}
          <Card className="shadow-md border-0">
            <CardHeader className="bg-gradient-to-r from-teal-50 to-cyan-50 rounded-t-lg">
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-teal-600" />
                System Status
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="space-y-2">
                <div className="flex justify-between items-center text-sm">
                  <span>API Services</span>
                  <Badge variant="outline" className="text-green-600 border-green-600">✓</Badge>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span>Video Generation</span>
                  <Badge variant="outline" className="text-green-600 border-green-600">✓</Badge>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span>Dashboard</span>
                  <Badge variant="outline" className="text-green-600 border-green-600">✓</Badge>
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                className="w-full"
                onClick={() =>
                  handleQuickActionClick("System Status", "/status")
                }
              >
                View Full Status
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  </div>
  );
};

export default SupportPage;
