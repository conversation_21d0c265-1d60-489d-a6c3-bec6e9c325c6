'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Search, 
  BookOpen, 
  MessageCircle, 
  Phone, 
  Mail,
  Clock,
  CheckCircle,
  ArrowRight,
  HelpCircle,
  Lightbulb,
  AlertCircle
} from 'lucide-react';
import useAnalytics from '@/hooks/useAnalytics';
import useNavigationTracking from '@/hooks/useNavigationTracking';

const HelpPage = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const { trackEvent } = useAnalytics();
  const { trackNavClick, trackSearchUsage } = useNavigationTracking({ section: 'Help Center' });

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      trackSearchUsage(searchQuery, 0); // Would be actual results count in real implementation
      trackEvent({
        action: 'help_search',
        category: 'Help Center',
        label: `Search: ${searchQuery}`,
      });
    }
  };

  const handleHelpClick = (title: string, href: string) => {
    trackEvent({
      action: 'help_article_click',
      category: 'Help Center',
      label: `${title} - ${href}`,
    });
    trackNavClick(title, href);
  };

  const popularArticles = [
    {
      title: 'How to add your first product',
      description: 'Step-by-step guide to adding products to your store',
      category: 'Getting Started',
      readTime: '3 min',
      href: '/help/add-first-product',
      icon: CheckCircle,
      color: 'text-green-500'
    },
    {
      title: 'Connecting your Shopify store',
      description: 'Learn how to integrate with your existing Shopify store',
      category: 'Integrations',
      readTime: '5 min',
      href: '/help/connect-shopify',
      icon: CheckCircle,
      color: 'text-blue-500'
    },
    {
      title: 'Creating your first video',
      description: 'Generate AI-powered videos for your products',
      category: 'Video Generation',
      readTime: '4 min',
      href: '/help/create-first-video',
      icon: CheckCircle,
      color: 'text-purple-500'
    },
    {
      title: 'Understanding analytics dashboard',
      description: 'Make sense of your performance metrics',
      category: 'Analytics',
      readTime: '6 min',
      href: '/help/analytics-dashboard',
      icon: CheckCircle,
      color: 'text-orange-500'
    },
    {
      title: 'Managing user permissions',
      description: 'Control access and permissions for team members',
      category: 'User Management',
      readTime: '4 min',
      href: '/help/user-permissions',
      icon: CheckCircle,
      color: 'text-teal-500'
    },
    {
      title: 'Troubleshooting common issues',
      description: 'Solutions to frequently encountered problems',
      category: 'Troubleshooting',
      readTime: '8 min',
      href: '/help/troubleshooting',
      icon: AlertCircle,
      color: 'text-red-500'
    },
  ];

  const helpCategories = [
    {
      title: 'Getting Started',
      description: 'New to the platform? Start here',
      icon: Lightbulb,
      color: 'bg-yellow-500',
      articleCount: 12,
      href: '/help/getting-started'
    },
    {
      title: 'Product Management',
      description: 'Managing products and inventory',
      icon: BookOpen,
      color: 'bg-green-500',
      articleCount: 18,
      href: '/help/products'
    },
    {
      title: 'Video Generation',
      description: 'Creating and managing videos',
      icon: BookOpen,
      color: 'bg-purple-500',
      articleCount: 15,
      href: '/help/videos'
    },
    {
      title: 'Analytics & Reports',
      description: 'Understanding your data',
      icon: BookOpen,
      color: 'bg-orange-500',
      articleCount: 10,
      href: '/help/analytics'
    },
    {
      title: 'Integrations',
      description: 'Connecting external services',
      icon: BookOpen,
      color: 'bg-blue-500',
      articleCount: 8,
      href: '/help/integrations'
    },
    {
      title: 'Account & Billing',
      description: 'Account settings and billing',
      icon: BookOpen,
      color: 'bg-teal-500',
      articleCount: 6,
      href: '/help/account'
    },
  ];

  const supportOptions = [
    {
      title: 'Live Chat',
      description: 'Chat with our support team',
      icon: MessageCircle,
      availability: 'Available 24/7',
      action: 'Start Chat',
      href: '/support/chat'
    },
    {
      title: 'Email Support',
      description: 'Send us an email',
      icon: Mail,
      availability: 'Response within 24h',
      action: 'Send Email',
      href: '/support/email'
    },
    {
      title: 'Phone Support',
      description: 'Call our support line',
      icon: Phone,
      availability: 'Mon-Fri 9AM-6PM EST',
      action: 'Call Now',
      href: '/support/phone'
    },
  ];

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold mb-4">Help Center</h1>
        <p className="text-xl text-muted-foreground mb-6">
          Find answers to your questions and get the help you need
        </p>
        
        {/* Search */}
        <form onSubmit={handleSearch} className="max-w-md mx-auto">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              type="text"
              placeholder="Search for help articles..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 pr-4 py-2"
            />
            <Button type="submit" size="sm" className="absolute right-1 top-1/2 transform -translate-y-1/2">
              Search
            </Button>
          </div>
        </form>
      </div>

      {/* Popular Articles */}
      <div className="mb-12">
        <h2 className="text-2xl font-bold mb-6">Popular Articles</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {popularArticles.map((article) => (
            <Card 
              key={article.title} 
              className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => handleHelpClick(article.title, article.href)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <article.icon className={`h-5 w-5 ${article.color} mt-1`} />
                  <Badge variant="secondary" className="text-xs">
                    {article.category}
                  </Badge>
                </div>
                <CardTitle className="text-lg leading-tight">{article.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="mb-3">{article.description}</CardDescription>
                <div className="flex items-center justify-between text-sm text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    {article.readTime}
                  </div>
                  <ArrowRight className="h-4 w-4" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Help Categories */}
      <div className="mb-12">
        <h2 className="text-2xl font-bold mb-6">Browse by Category</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {helpCategories.map((category) => (
            <Card 
              key={category.title}
              className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => handleHelpClick(category.title, category.href)}
            >
              <CardHeader>
                <div className="flex items-center gap-3">
                  <div className={`p-2 rounded-lg ${category.color}`}>
                    <category.icon className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <CardTitle className="text-lg">{category.title}</CardTitle>
                    <Badge variant="outline" className="mt-1">
                      {category.articleCount} articles
                    </Badge>
                  </div>
                </div>
                <CardDescription>{category.description}</CardDescription>
              </CardHeader>
            </Card>
          ))}
        </div>
      </div>

      {/* Support Options */}
      <div>
        <h2 className="text-2xl font-bold mb-6">Need More Help?</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {supportOptions.map((option) => (
            <Card key={option.title}>
              <CardHeader className="text-center">
                <div className="mx-auto mb-4 p-3 bg-primary/10 rounded-full w-fit">
                  <option.icon className="h-6 w-6 text-primary" />
                </div>
                <CardTitle>{option.title}</CardTitle>
                <CardDescription>{option.description}</CardDescription>
              </CardHeader>
              <CardContent className="text-center">
                <p className="text-sm text-muted-foreground mb-4">{option.availability}</p>
                <Button 
                  className="w-full"
                  onClick={() => handleHelpClick(option.title, option.href)}
                >
                  {option.action}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
};

export default HelpPage;
