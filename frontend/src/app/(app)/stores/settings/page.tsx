'use client';

import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger, DialogClose } from '@/components/ui/dialog';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Settings, FolderSyncIcon, CheckCircle, Loader2, RefreshCw, Zap, Clock } from 'lucide-react';
import { toast } from 'sonner';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { api } from '@/services/api';

interface SyncStatus {
  products: { status: 'success' | 'syncing' | 'error'; lastSync?: string };
}

interface SyncProgress {
  id: number;
  store_id: number;
  sync_type: string;
  status: 'running' | 'completed' | 'failed' | 'paused';
  total_items: number;
  processed_items: number;
  current_batch: number;
  total_batches: number;
  progress_percentage: number;
  last_update: string;
  created_at: string;
  completed_at?: string;
  error_message?: string;
}

interface StoreDetails {
  id: number;
  shop_domain?: string;
  shop_name?: string;
  is_active?: boolean;
}

const SyncSettings: React.FC = () => {
  const searchParams = useSearchParams();
  const storeId = searchParams.get('storeId');

  const [syncStatus, setSyncStatus] = useState<SyncStatus>({
    products: { status: 'success', lastSync: '5 minutes ago' },
  });
  const [syncProgress, setSyncProgress] = useState<SyncProgress | null>(null);
  const [progressHistory, setProgressHistory] = useState<string[]>([]);
  const [isDisconnected, setIsDisconnected] = useState(false);

  // Fetch store details using React Query
  const {
    data: storeDetails,
    isLoading: isLoadingStoreDetails,
    error: storeDetailsError
  } = useQuery<StoreDetails>({
    queryKey: ['store-details', storeId],
    queryFn: async () => {
      if (!storeId) throw new Error('Store ID is required');
      const response = await api.get(`/api/stores/${storeId}`);
      return response.data;
    },
    enabled: !!storeId,
    retry: 2,
    retryDelay: 1000,
  });

  // Fetch sync progress using React Query with polling
  const {
    data: syncProgressData,
    refetch: refetchProgress
  } = useQuery<SyncProgress>({
    queryKey: ['sync-progress', storeId, 'products'],
    queryFn: async () => {
      if (!storeId) throw new Error('Store ID is required');
      const response = await api.get(`/api/stores/${storeId}/sync-progress/products`);
      return response.data;
    },
    enabled: !!storeId,
    refetchInterval: (data) => {
      // Poll every 2 seconds if sync is running, otherwise stop polling
      return data?.status === 'running' ? 2000 : false;
    },
    retry: 1,
    retryDelay: 1000,
  });

  // Fetch sync checkpoints
  const {
    data: syncCheckpoints,
    isLoading: isLoadingCheckpoints
  } = useQuery({
    queryKey: ['sync-checkpoints', storeId],
    queryFn: async () => {
      if (!storeId) throw new Error('Store ID is required');
      const response = await api.get(`/api/stores/${storeId}/sync-checkpoints`);
      return response.data;
    },
    enabled: !!storeId,
    retry: 2,
    retryDelay: 1000,
  });


  // Update sync progress and history
  useEffect(() => {
    if (syncProgressData) {
      setSyncProgress(syncProgressData);

      // Update sync status based on progress
      if (syncProgressData.status === 'running') {
        setSyncStatus(prev => ({
          ...prev,
          products: { status: 'syncing', lastSync: `Processing ${syncProgressData.processed_items}/${syncProgressData.total_items} items` }
        }));

        // Add to progress history for rolling updates
        const progressMessage = `${syncProgressData.processed_items} products synced`;
        setProgressHistory(prev => {
          const newHistory = [progressMessage, ...prev.slice(0, 4)]; // Keep last 5 updates
          return newHistory;
        });
      } else if (syncProgressData.status === 'completed') {
        setSyncStatus(prev => ({
          ...prev,
          products: { status: 'success', lastSync: `Completed at ${new Date(syncProgressData.completed_at || '').toLocaleTimeString()}` }
        }));
      } else if (syncProgressData.status === 'failed') {
        setSyncStatus(prev => ({
          ...prev,
          products: { status: 'error', lastSync: `Failed: ${syncProgressData.error_message || 'Unknown error'}` }
        }));
      }
    }
  }, [syncProgressData]);

  // Update disconnection status based on store details
  useEffect(() => {
    if (storeDetails) {
      setIsDisconnected(!storeDetails.is_active);
    }
  }, [storeDetails]);

  const queryClient = useQueryClient();

  const syncMutation = useMutation({
    mutationFn: async ({ syncType, mode }: { syncType: string; mode: 'full' | 'incremental' }) => {
      if (!storeId) throw new Error('Store ID is required');

      const response = await api.post(`/api/stores/${storeId}/sync/products`, {
        mode: mode
      });
      return response.data;
    },
    onSuccess: (data, variables) => {
      const modeText = variables.mode === 'full' ? 'Full' : 'Incremental';
      toast.success(`${modeText} product sync job has been queued`);
      // Update sync status to show it's in progress
      setSyncStatus(prev => ({
        ...prev,
        products: { status: 'syncing', lastSync: 'Starting...' }
      }));
      // Clear previous progress history
      setProgressHistory([]);
      // Trigger immediate progress fetch
      refetchProgress();
    },
    onError: (error) => {
      console.error('Failed to start sync:', error);
      toast.error('Failed to start sync job');
    },
  });

  const testConnectionMutation = useMutation({
    mutationFn: async () => {
      if (!storeId) throw new Error('Store ID is required');

      const response = await api.post(`/api/stores/${storeId}/test-connection`);
      return response.data;
    },
    onSuccess: (data) => {
      if (data.success) {
        toast.success(`Connection test successful: ${data.message}`);
      } else {
        toast.error(`Connection test failed: ${data.message}`);
      }
    },
    onError: (error: any) => {
      console.error('Failed to test connection:', error);
      toast.error(`Connection test failed: ${error?.response?.data?.detail || 'Unknown error'}`);
    },
  });

  const disconnectMutation = useMutation({
    mutationFn: async () => {
      if (!storeId) throw new Error('Store ID is required');

      const response = await api.post(`/api/stores/${storeId}/disconnect`);
      return response.data;
    },
    onSuccess: () => {
      setIsDisconnected(true);
      toast.success('Store disconnected successfully');
      queryClient.invalidateQueries({ queryKey: ['store-details', storeId] });
    },
    onError: (error: any) => {
      console.error('Failed to disconnect store:', error);
      toast.error(`Failed to disconnect store: ${error?.response?.data?.detail || 'Unknown error'}`);
    },
  });

  const connectMutation = useMutation({
    mutationFn: async () => {
      if (!storeId) throw new Error('Store ID is required');

      const response = await api.post(`/api/stores/${storeId}/connect`);
      return response.data;
    },
    onSuccess: () => {
      setIsDisconnected(false);
      toast.success('Store connected successfully');
      queryClient.invalidateQueries({ queryKey: ['store-details', storeId] });
    },
    onError: (error: any) => {
      console.error('Failed to connect store:', error);
      const errorMessage = error?.response?.data?.detail || 'Unknown error';

      if (errorMessage.includes('access token is missing')) {
        toast.error('Store access token is missing. Please reconnect using the manual setup option.');
        setTimeout(() => {
          window.location.href = '/stores/connect';
        }, 3000);
      } else if (errorMessage.includes('domain is missing')) {
        toast.error('Store domain is missing. Please update your store information.');
        // Could redirect to store settings or show domain input
      } else if (errorMessage.includes('credentials are missing')) {
        toast.error('Store credentials are missing. Please reconnect using the manual setup option.');
        setTimeout(() => {
          window.location.href = '/stores/connect';
        }, 3000);
      } else {
        toast.error(`Failed to connect store: ${errorMessage}`);
      }
    },
  });

  const deleteMutation = useMutation({
    mutationFn: async () => {
      if (!storeId) throw new Error('Store ID is required');

      const response = await api.delete(`/api/stores/${storeId}`);
      return response.data;
    },
    onSuccess: () => {
      toast.success('Store deleted successfully');
      // Redirect to stores list
      window.location.href = '/stores';
    },
    onError: (error: any) => {
      console.error('Failed to delete store:', error);
      toast.error(`Failed to delete store: ${error?.response?.data?.detail || 'Unknown error'}`);
    },
  });

  if (isLoadingStoreDetails) {
    return (
      <div className="space-y-8">
        {/* Loading Header */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <div className="h-8 bg-gray-200 rounded w-48 animate-pulse"></div>
              <div className="h-4 bg-gray-200 rounded w-64 mt-2 animate-pulse"></div>
            </div>
            <div className="flex items-center space-x-2">
              <div className="h-3 w-3 bg-gray-300 rounded-full animate-pulse"></div>
              <div className="h-4 bg-gray-200 rounded w-16 animate-pulse"></div>
            </div>
          </div>
          <div className="h-24 bg-gray-100 rounded-lg animate-pulse"></div>
        </div>

        {/* Loading Content */}
        <div className="grid gap-6 md:grid-cols-2">
          <div className="h-48 bg-gray-100 rounded-lg animate-pulse"></div>
          <div className="h-48 bg-gray-100 rounded-lg animate-pulse"></div>
        </div>

        <div className="h-96 bg-gray-100 rounded-lg animate-pulse"></div>
      </div>
    );
  }

  if (!storeId) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="text-red-600 mb-4">⚠️ Store ID is required</div>
            <p className="text-muted-foreground">Please navigate to this page from the stores list.</p>
          </div>
        </div>
      </div>
    );
  }

  // Show errors if any queries failed
  const hasErrors = storeDetailsError;

  return (
    <div className="space-y-8">
      {/* Error Display */}
      {hasErrors && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <div className="text-red-600">⚠️</div>
            <div>
              <h4 className="font-medium text-red-900">Failed to load some data</h4>
              <p className="text-sm text-red-700">
                {storeDetailsError && 'Store details could not be loaded. '}
                Please try refreshing the page.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Header Section */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Store Settings</h1>
            <p className="text-muted-foreground mt-2">
              Monitor synchronization status and manage your store connection
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <div className={`h-3 w-3 rounded-full animate-pulse ${
              hasErrors ? 'bg-red-500' : isDisconnected ? 'bg-orange-500' : 'bg-green-500'
            }`}></div>
            <span className="text-sm text-muted-foreground">
              {hasErrors ? 'Issues Detected' : isDisconnected ? 'Disconnected' : 'Connected'}
            </span>
          </div>
        </div>

        {/* Store Info Card */}
        {storeDetails && (
          <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Settings className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">{storeDetails.shop_name || storeDetails.shop_domain || 'Unnamed Store'}</h3>
                    <p className="text-muted-foreground">{storeDetails.shop_domain || `Store ID: ${storeId}`}</p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm text-muted-foreground">Store ID</div>
                  <div className="font-mono text-lg font-semibold">{storeId}</div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Sync Status Overview */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <FolderSyncIcon className="h-5 w-5 text-blue-600" />
              <span>Synchronization Status</span>
            </CardTitle>
            <CardDescription>Real-time sync status across all data types</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {syncCheckpoints && syncCheckpoints.length > 0 ? (
                syncCheckpoints.map((checkpoint: any) => (
                  <div key={checkpoint.id} className={`flex items-center justify-between p-4 rounded-lg border-2 transition-all ${
                    checkpoint.last_sync_status === 'success'
                      ? 'bg-green-50 border-green-200 shadow-sm'
                      : checkpoint.last_sync_status === 'running'
                      ? 'bg-blue-50 border-blue-200 shadow-sm'
                      : checkpoint.last_sync_status === 'failed'
                      ? 'bg-red-50 border-red-200'
                      : 'bg-gray-50 border-gray-200'
                  }`}>
                    <div className="flex items-center space-x-3">
                      {checkpoint.last_sync_status === 'success' ? (
                        <CheckCircle className="h-8 w-8 text-green-600" />
                      ) : checkpoint.last_sync_status === 'running' ? (
                        <Loader2 className="h-8 w-8 text-blue-600 animate-spin" />
                      ) : checkpoint.last_sync_status === 'failed' ? (
                        <Clock className="h-8 w-8 text-red-600" />
                      ) : (
                        <Clock className="h-8 w-8 text-gray-600" />
                      )}
                      <div>
                        <p className={`font-semibold text-lg capitalize ${
                          checkpoint.last_sync_status === 'success'
                            ? 'text-green-900'
                            : checkpoint.last_sync_status === 'running'
                            ? 'text-blue-900'
                            : checkpoint.last_sync_status === 'failed'
                            ? 'text-red-900'
                            : 'text-gray-900'
                        }`}>{checkpoint.entity_type}</p>
                        <div className={`text-sm ${
                          checkpoint.last_sync_status === 'success'
                            ? 'text-green-700'
                            : checkpoint.last_sync_status === 'running'
                            ? 'text-blue-700'
                            : checkpoint.last_sync_status === 'failed'
                            ? 'text-red-700'
                            : 'text-gray-700'
                        }`}>
                          {/* Current sync status */}
                          {checkpoint.current_sync_stage && checkpoint.last_sync_status === 'running' && (
                            <p className="font-medium">
                              {checkpoint.current_sync_stage === 'airbyte_sync' && '🔄 Airbyte syncing...'}
                              {checkpoint.current_sync_stage === 'local_processing' && '⚙️ Processing locally...'}
                              {checkpoint.current_sync_stage === 'completed' && '✅ Sync completed'}
                              {checkpoint.current_sync_stage === 'failed' && '❌ Sync failed'}
                            </p>
                          )}

                          {/* Last successful sync info */}
                          {checkpoint.last_successful_sync_at && (
                            <p>
                              Last sync: {new Date(checkpoint.last_successful_sync_at).toLocaleString()}
                              {checkpoint.total_records > 0 && ` • ${checkpoint.total_records} records`}
                            </p>
                          )}

                          {/* Airbyte sync info */}
                          {checkpoint.airbyte_last_sync_at && (
                            <p className="text-xs opacity-75">
                              Airbyte: {new Date(checkpoint.airbyte_last_sync_at).toLocaleString()}
                            </p>
                          )}

                          {/* Current sync timing */}
                          {checkpoint.last_sync_status === 'running' && checkpoint.airbyte_sync_started_at && (
                            <p className="text-xs opacity-75">
                              Started: {new Date(checkpoint.airbyte_sync_started_at).toLocaleString()}
                              {checkpoint.records_processed_in_sync > 0 && ` • ${checkpoint.records_processed_in_sync} processed`}
                            </p>
                          )}

                          {/* Sync duration for completed syncs */}
                          {checkpoint.sync_duration_seconds && checkpoint.last_sync_status === 'success' && (
                            <p className="text-xs opacity-75">
                              Duration: {Math.round(checkpoint.sync_duration_seconds)}s
                              {checkpoint.sync_trigger_type && ` • ${checkpoint.sync_trigger_type}`}
                            </p>
                          )}

                          {/* Airbyte job info */}
                          {checkpoint.airbyte_job_id && (
                            <p className="text-xs opacity-75">
                              Job ID: {checkpoint.airbyte_job_id}
                            </p>
                          )}

                          {/* Error message */}
                          {checkpoint.last_error_message && (
                            <p className="text-xs text-red-600 mt-1">
                              Error: {checkpoint.last_error_message}
                            </p>
                          )}

                          {/* No sync info */}
                          {!checkpoint.last_successful_sync_at && checkpoint.last_sync_status !== 'running' && (
                            <p className="text-xs opacity-75">Not synced yet</p>
                          )}
                        </div>
                        {checkpoint.last_error_message && (
                          <p className="text-xs text-red-600 mt-1">
                            Error: {checkpoint.last_error_message}
                          </p>
                        )}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className={`text-xs font-medium px-2 py-1 rounded-full ${
                        checkpoint.last_sync_status === 'success'
                          ? 'bg-green-100 text-green-800'
                          : checkpoint.last_sync_status === 'running'
                          ? 'bg-blue-100 text-blue-800'
                          : checkpoint.last_sync_status === 'failed'
                          ? 'bg-red-100 text-red-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {checkpoint.last_sync_status?.toUpperCase() || 'PENDING'}
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  {isLoadingCheckpoints ? 'Loading sync status...' : 'No sync data available'}
                </div>
              )}

              {/* Progress Bar */}
              {syncProgress && syncProgress.status === 'running' && (
                <div className="bg-white p-4 rounded-lg border border-blue-200">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium text-blue-900">Sync Progress</span>
                    <span className="text-sm text-blue-700">
                      {syncProgress.processed_items}/{syncProgress.total_items}
                    </span>
                  </div>
                  <div className="w-full bg-blue-100 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${syncProgress.progress_percentage}%` }}
                    ></div>
                  </div>
                  <div className="text-xs text-blue-600 mt-1">
                    Batch {syncProgress.current_batch}/{syncProgress.total_batches}
                  </div>
                </div>
              )}

              {/* Progress History */}
              {progressHistory.length > 0 && (
                <div className="bg-white p-4 rounded-lg border border-gray-200">
                  <h4 className="text-sm font-medium text-gray-900 mb-2">Recent Updates</h4>
                  <div className="space-y-1 max-h-32 overflow-y-auto">
                    {progressHistory.map((message, index) => (
                      <div key={index} className="text-xs text-gray-600 flex items-center">
                        <div className="w-1 h-1 bg-blue-500 rounded-full mr-2"></div>
                        {message}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Settings className="h-5 w-5 text-purple-600" />
              <span>Quick Actions</span>
            </CardTitle>
            <CardDescription>Common management tasks</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-3">
              <Button
                variant="outline"
                className="h-12"
                onClick={() => syncMutation.mutate({ syncType: 'products', mode: 'incremental' })}
                disabled={syncMutation.isPending}
              >
                {syncMutation.isPending ? (
                  <RefreshCw className="h-5 w-5 mr-2 animate-spin" />
                ) : (
                  <RefreshCw className="h-5 w-5 mr-2" />
                )}
                <div className="text-center">
                  <div className="font-medium">Incremental Sync</div>
                  <div className="text-xs text-muted-foreground">Changed data only</div>
                </div>
              </Button>

              <Button
                variant="outline"
                className="h-12"
                onClick={() => syncMutation.mutate({ syncType: 'products', mode: 'full' })}
                disabled={syncMutation.isPending}
              >
                {syncMutation.isPending ? (
                  <Zap className="h-5 w-5 mr-2 animate-spin" />
                ) : (
                  <Zap className="h-5 w-5 mr-2" />
                )}
                <div className="text-center">
                  <div className="font-medium">Full Sync</div>
                  <div className="text-xs text-muted-foreground">All data</div>
                </div>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>


      {/* Advanced Actions */}
      <Collapsible defaultOpen={false} className="border-dashed rounded-lg">
        <CollapsibleTrigger asChild>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 p-4 cursor-pointer">
            <div className="flex items-center space-x-2">
              <Settings className="h-5 w-5 text-gray-600" />
              <CardTitle>Advanced Actions</CardTitle>
            </div>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth="2"
              stroke="currentColor"
              className="h-5 w-5 text-gray-600 transition-transform duration-200 data-[state=open]:rotate-180"
            >
              <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
            </svg>
          </CardHeader>
        </CollapsibleTrigger>
        <CollapsibleContent>
          <CardContent className="pt-0">
            <div className="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-3">

            <div className="p-4 border rounded-lg hover:bg-gray-50 transition-colors">
              <div className="flex items-center space-x-3 mb-3">
                <div className={`h-8 w-8 rounded-lg flex items-center justify-center ${
                  testConnectionMutation.isPending
                    ? 'bg-yellow-100'
                    : testConnectionMutation.data?.success
                    ? 'bg-green-100'
                    : testConnectionMutation.data?.success === false
                    ? 'bg-red-100'
                    : 'bg-green-100'
                }`}>
                  {testConnectionMutation.isPending ? (
                    <Loader2 className="h-4 w-4 text-yellow-600 animate-spin" />
                  ) : testConnectionMutation.data?.success ? (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  ) : testConnectionMutation.data?.success === false ? (
                    <Clock className="h-4 w-4 text-red-600" />
                  ) : (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  )}
                </div>
                <div>
                  <h4 className="font-medium">Connection Test</h4>
                  <p className="text-sm text-muted-foreground">
                    {testConnectionMutation.isPending
                      ? 'Testing connection...'
                      : testConnectionMutation.data?.success
                      ? 'Connection successful'
                      : testConnectionMutation.data?.success === false
                      ? 'Connection failed'
                      : 'Verify store connection'
                    }
                  </p>
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                className="w-full"
                onClick={() => testConnectionMutation.mutate()}
                disabled={testConnectionMutation.isPending}
              >
                {testConnectionMutation.isPending ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Testing...
                  </>
                ) : (
                  'Test Connection'
                )}
              </Button>
            </div>

            <div className="p-4 border rounded-lg hover:bg-gray-50 transition-colors">
              <div className="flex items-center space-x-3 mb-3">
                <div className={`h-8 w-8 rounded-lg flex items-center justify-center ${
                  (disconnectMutation.isPending || connectMutation.isPending)
                    ? 'bg-yellow-100'
                    : isDisconnected
                    ? 'bg-red-100'
                    : 'bg-blue-100'
                }`}>
                  {(disconnectMutation.isPending || connectMutation.isPending) ? (
                    <Loader2 className="h-4 w-4 text-yellow-600 animate-spin" />
                  ) : isDisconnected ? (
                    <CheckCircle className="h-4 w-4 text-red-600" />
                  ) : (
                    <Settings className="h-4 w-4 text-blue-600" />
                  )}
                </div>
                <div>
                  <h4 className="font-medium">
                    {isDisconnected ? 'Connect Store' : 'Disconnect Store'}
                  </h4>
                  <p className="text-sm text-muted-foreground">
                    {disconnectMutation.isPending
                      ? 'Disconnecting...'
                      : isDisconnected
                      ? 'Store disconnected - go to store connection page to reconnect'
                      : 'Stop synchronization and revoke access'
                    }
                  </p>
                </div>
              </div>
              {isDisconnected ? (
                <Button
                  variant="default"
                  size="sm"
                  className="w-full"
                  onClick={() => {
                    // Redirect to connect page with storeId for auto-population
                    window.location.href = `/stores/connect?storeId=${storeId}`;
                  }}
                >
                  <Settings className="h-4 w-4 mr-2" />
                  Connect to Shopify
                </Button>
              ) : (
                <Dialog>
                  <DialogTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full"
                      disabled={disconnectMutation.isPending}
                    >
                      {disconnectMutation.isPending ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Disconnecting...
                        </>
                      ) : (
                        'Disconnect Store'
                      )}
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Disconnect Store</DialogTitle>
                      <DialogDescription>
                        Are you sure you want to disconnect this store? This will stop all synchronization and revoke access. You can reconnect later if needed.
                      </DialogDescription>
                    </DialogHeader>
                    <DialogFooter>
                      <DialogClose asChild>
                        <Button variant="outline">
                          Cancel
                        </Button>
                      </DialogClose>
                      <Button
                        variant="destructive"
                        onClick={() => disconnectMutation.mutate()}
                        disabled={disconnectMutation.isPending}
                      >
                        {disconnectMutation.isPending ? (
                          <>
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            Disconnecting...
                          </>
                        ) : (
                          'Disconnect'
                        )}
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              )}
            </div>

            <div className="p-4 border rounded-lg hover:bg-gray-50 transition-colors">
              <div className="flex items-center space-x-3 mb-3">
                <div className={`h-8 w-8 rounded-lg flex items-center justify-center ${
                  deleteMutation.isPending
                    ? 'bg-yellow-100'
                    : !isDisconnected
                    ? 'bg-gray-100'
                    : 'bg-red-100'
                }`}>
                  {deleteMutation.isPending ? (
                    <Loader2 className="h-4 w-4 text-yellow-600 animate-spin" />
                  ) : (
                    <Settings className={`h-4 w-4 ${!isDisconnected ? 'text-gray-400' : 'text-red-600'}`} />
                  )}
                </div>
                <div>
                  <h4 className={`font-medium ${!isDisconnected ? 'text-gray-400' : ''}`}>Delete Store</h4>
                  <p className="text-sm text-muted-foreground">
                    {deleteMutation.isPending
                      ? 'Deleting...'
                      : !isDisconnected
                      ? 'Disconnect the store first to enable deletion'
                      : 'Permanently remove store from system'
                    }
                  </p>
                </div>
              </div>
              <Dialog>
                <DialogTrigger asChild>
                  <Button
                    variant="destructive"
                    size="sm"
                    className="w-full"
                    disabled={deleteMutation.isPending || !isDisconnected}
                  >
                    {deleteMutation.isPending ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Deleting...
                      </>
                    ) : !isDisconnected ? (
                      'Disconnect First'
                    ) : (
                      'Delete Store'
                    )}
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Delete Store</DialogTitle>
                    <DialogDescription>
                      Are you sure you want to delete this store? This action cannot be undone. All data associated with this store will be permanently removed.
                    </DialogDescription>
                  </DialogHeader>
                  <DialogFooter>
                    <DialogClose asChild>
                      <Button variant="outline">
                        Cancel
                      </Button>
                    </DialogClose>
                    <DialogClose asChild>
                      <Button variant="destructive" onClick={() => deleteMutation.mutate()}>
                        Delete
                      </Button>
                    </DialogClose>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          </div>
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </div>
  );
};

export default SyncSettings;
