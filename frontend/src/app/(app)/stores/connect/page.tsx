"use client";

import { useState, useEffect } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import {
  ShoppingBag,
  CheckCircle,
  AlertCircle,
  ExternalLink,
  Shield,
  Users,
  BarChart3,
  Video,
  Loader2,
} from "lucide-react";
import { useQuery, useMutation } from "@tanstack/react-query";
import ShopifyService from "@/services/shopifyService";
import { storeService } from "@/services/storeService";
import { toast } from "sonner";

export default function ConnectShopifyPage() {
  const searchParams = useSearchParams();
  const router = useRouter();

  // Check if we're managing a specific store or connecting a new one
  // This must be defined before any hooks that depend on it
  const storeIdParam = searchParams.get("storeId");
  const isManagingSpecificStore = !!storeIdParam;

  const [shopDomain, setShopDomain] = useState("");
  const [isConnecting, setIsConnecting] = useState(false);
  const [manualStore, setManualStore] = useState({
    shopDomain: "",
    adminAccessToken: "",
    shopName: "",
  });
  const [isManualMode, setIsManualMode] = useState(false);
  const [managedStore, setManagedStore] = useState<any>(null);
  const [fetchedStoreId, setFetchedStoreId] = useState<number | null>(null);

  // Fetch store details by ID
  const fetchStoreMutation = useMutation({
    mutationFn: (storeId: number) => storeService.getStore(storeId),
    onSuccess: (store) => {
      if (store) {
        setManagedStore(store);

        // Extract domain prefix from full domain
        let domainPrefix = "";
        if (store.shop_domain) {
          domainPrefix = store.shop_domain.replace(".myshopify.com", "");
        }

        // Pre-fill form with store data
        setShopDomain(domainPrefix);
        setManualStore({
          shopDomain: domainPrefix,
          adminAccessToken: store.admin_access_token || "",
          shopName: store.shop_name || "",
        });

        if (store.admin_access_token) {
          toast.success("Store details loaded successfully!");
        } else {
          toast(
            "Store found but access token not available. Please enter your admin access token."
          );
        }
      }
    },
    onError: (error: any) => {
      console.error("Failed to fetch store:", error);
      if (error?.response?.status === 404) {
        toast.error("Store not found");
      } else {
        toast.error("Failed to load store details");
      }
    },
  });

  // Pre-fill from URL parameters
  useEffect(() => {
    if (storeIdParam) {
      const storeId = parseInt(storeIdParam, 10);
      if (!isNaN(storeId) && storeId !== fetchedStoreId) {
        fetchStoreMutation.mutate(storeId);
        setFetchedStoreId(storeId);
      }
    } else {
      setFetchedStoreId(null);
    }
  }, [storeIdParam, fetchedStoreId]);

  // Fetch connection status - only when not managing a specific store
  const {
    data: connectionData,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ["shopify-connection"],
    queryFn: () => ShopifyService.getConnectionStatus(),
    enabled: !isManagingSpecificStore, // Don't fetch all stores when managing a specific one
  });

  // Connect to Shopify mutation
  const connectMutation = useMutation({
    mutationFn: (domain: string) => ShopifyService.getInstallUrl(domain),
    onSuccess: (data) => {
      // Redirect to Shopify OAuth
      window.location.href = data.install_url;
    },
    onError: (error) => {
      toast.error("Failed to connect to Shopify");
      console.error("Connection error:", error);
      setIsConnecting(false);
    },
  });

  // Disconnect mutation
  const disconnectMutation = useMutation({
    mutationFn: (storeId: string) => ShopifyService.disconnectStore(storeId),
    onSuccess: () => {
      toast.success("Disconnected from Shopify");
      refetch();
    },
    onError: (error) => {
      toast.error("Failed to disconnect from Shopify");
      console.error("Disconnect error:", error);
    },
  });

  // Manual store creation mutation
  const createStoreMutation = useMutation({
    mutationFn: async (storeData: typeof manualStore) => {
      // Ensure Shopify domain has the correct suffix
      let domain = storeData.shopDomain.trim();
      if (!domain.includes(".myshopify.com")) {
        domain = `${domain}.myshopify.com`;
      }

      const storeCreateData = {
        platform: "shopify",
        shop_domain: domain,
        admin_access_token: storeData.adminAccessToken,
        shop_name: storeData.shopName,
        is_active: true,
      };
      return await storeService.createStore(storeCreateData as any);
    },
    onSuccess: (data) => {
      toast.success("Store created successfully!");
      // Enable Airbyte sync for the new store
      enableSyncMutation.mutate(data.id);
    },
    onError: (error: any) => {
      const errorMessage =
        error?.response?.data?.detail ||
        error?.message ||
        "Failed to create store";
      toast.error(errorMessage);
      console.error("Store creation error:", error);
    },
  });

  // Enable Airbyte sync mutation
  const enableSyncMutation = useMutation({
    mutationFn: async (storeId: number) => {
      return await storeService.connectStore(storeId);
    },
    onSuccess: () => {
      toast.success("Store connected and Airbyte sync enabled successfully!");
      refetch();
    },
    onError: (error: any) => {
      const errorMessage =
        error?.response?.data?.detail ||
        error?.message ||
        "Failed to enable Airbyte sync";
      toast.error(errorMessage);
      console.error("Sync enable error:", error);
      // Refetch to update the store status since it might have been deactivated
      refetch();
    },
  });

  // Test connection mutation
  const testConnectionMutation = useMutation({
    mutationFn: async (storeData: typeof manualStore) => {
      // Ensure Shopify domain has the correct suffix
      let domain = storeData.shopDomain.trim();
      if (!domain.includes(".myshopify.com")) {
        domain = `${domain}.myshopify.com`;
      }

      const testData = {
        platform: "shopify",
        shop_domain: domain,
        admin_access_token: storeData.adminAccessToken,
      };
      return await storeService.testConnectionByCredentials(testData);
    },
    onSuccess: (data) => {
      if (data.success) {
        toast.success("Connection test successful!");
      } else {
        toast.error("Connection test failed");
      }
    },
    onError: (error) => {
      toast.error("Connection test failed");
      console.error("Connection test error:", error);
    },
  });

  const handleConnect = () => {
    if (!shopDomain.trim()) {
      toast.error("Please enter your shop domain");
      return;
    }

    // Clean up domain input and ensure .myshopify.com suffix
    let domain = shopDomain.trim().toLowerCase();
    if (domain.includes("https://")) {
      domain = domain.replace("https://", "");
    }
    if (domain.includes("http://")) {
      domain = domain.replace("http://", "");
    }
    // Ensure .myshopify.com suffix is present
    if (!domain.includes(".myshopify.com")) {
      domain = `${domain}.myshopify.com`;
    }

    setIsConnecting(true);
    connectMutation.mutate(domain);
  };

  const handleDisconnect = (storeId: string) => {
    if (
      confirm(
        "Are you sure you want to disconnect from Shopify? This will stop video generation and analytics tracking."
      )
    ) {
      disconnectMutation.mutate(storeId);
    }
  };

  const handleManualStoreSubmit = () => {
    if (
      !manualStore.shopDomain.trim() ||
      !manualStore.adminAccessToken.trim()
    ) {
      toast.error("Please fill in shop domain and admin access token");
      return;
    }

    createStoreMutation.mutate(manualStore);
  };

  const handleTestConnection = () => {
    if (
      !manualStore.shopDomain.trim() ||
      !manualStore.adminAccessToken.trim()
    ) {
      toast.error("Please enter shop domain and admin access token");
      return;
    }

    testConnectionMutation.mutate(manualStore);
  };

  const isConnected = connectionData?.isConnected;
  const stores = connectionData?.stores || [];

  const requiredScopes = [
    {
      name: "read_products",
      description: "Read product information for video generation",
    },
    { name: "write_products", description: "Add generated videos to products" },
    {
      name: "read_product_listings",
      description: "Access product collections and tags",
    },
    { name: "write_files", description: "Upload video files to Shopify" },
    { name: "read_orders", description: "Track conversion analytics" },
  ];

  if (isLoading || (isManagingSpecificStore && fetchStoreMutation.isPending)) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">
            {isManagingSpecificStore
              ? "Loading store details..."
              : "Loading..."}
          </span>
        </div>
      </div>
    );
  }

  return (
    <div
      key={isManagingSpecificStore ? "manage" : "connect"}
      className="container mx-auto p-6 space-y-6"
    >
      {/* Header */}
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">
          {isManagingSpecificStore
            ? "Manage Store Connection"
            : "Connect to Shopify"}
        </h1>
        <p className="text-muted-foreground">
          {isManagingSpecificStore
            ? "Update your store connection and settings"
            : "Connect your Shopify store to start generating AI-powered product videos"}
        </p>
      </div>

      {/* Show store details only when managing a specific connected store */}
      {isManagingSpecificStore && managedStore && managedStore.is_active && (
        <div key="store-management" className="max-w-2xl mx-auto">
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              Your store is connected and ready for video generation.
            </AlertDescription>
          </Alert>

          {/* Store Details - Show the specific store being managed */}
          <div className="space-y-4 mt-6">
            <h3 className="text-lg font-medium">Store Details</h3>
            <Card key={managedStore.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <ShoppingBag className="h-6 w-6 text-green-600" />
                    <div>
                      <CardTitle className="text-lg">
                        {managedStore.shop_domain?.replace(
                          ".myshopify.com",
                          ""
                        ) || "Unknown Store"}
                      </CardTitle>
                      <CardDescription>
                        {managedStore.shop_domain || "No domain"}
                      </CardDescription>
                    </div>
                  </div>
                  <Badge
                    variant="default"
                    className="bg-green-100 text-green-800"
                  >
                    Connected
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex space-x-2">
                  <Button
                    onClick={() =>
                      router.push(
                        `/stores/settings?storeId=${managedStore.id}`
                      )
                    }
                    className="w-full"
                  >
                    Manage Store Settings
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {/* Show connection form when: no parameter OR (parameter exists AND store is not connected) */}
      {(!isManagingSpecificStore || (isManagingSpecificStore && managedStore && !managedStore.is_active)) && (
        <div key="connection-form" className="max-w-2xl mx-auto space-y-6">
        {/* Connection Mode Toggle */}
        <Card>
          <CardHeader>
            <CardTitle>Connection Method</CardTitle>
            <CardDescription>
              Choose how you want to connect your Shopify store
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex space-x-4">
              <Button
                variant={!isManualMode ? "default" : "outline"}
                onClick={() => setIsManualMode(false)}
                className="flex-1"
              >
                <ExternalLink className="mr-2 h-4 w-4" />
                OAuth Connect
              </Button>
              <Button
                variant={isManualMode ? "default" : "outline"}
                onClick={() => setIsManualMode(true)}
                className="flex-1"
              >
                <Shield className="mr-2 h-4 w-4" />
                Manual Setup
              </Button>
            </div>
          </CardContent>
        </Card>

        {isManualMode ? (
          /* Manual Store Creation */
          <Card animate={false}>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Shield className="mr-2 h-5 w-5" />
                Manual Store Setup
              </CardTitle>
              <CardDescription>
                Enter your store details and admin access token to connect
                manually
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="shop-domain">Store Prefix *</Label>
                <div className="flex">
                  <Input
                    id="shop-domain"
                    placeholder="your-store-name"
                    value={manualStore.shopDomain}
                    onChange={(e) =>
                      setManualStore((prev) => ({
                        ...prev,
                        shopDomain: e.target.value,
                      }))
                    }
                    className="rounded-r-none"
                  />
                  <span className="inline-flex items-center px-3 text-sm text-gray-900 bg-gray-200 border border-l-0 border-gray-300 rounded-r-md">
                    .myshopify.com
                  </span>
                </div>
                <p className="text-xs text-muted-foreground">
                  Enter your store prefix (e.g., "dancing-queens-staging")
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="admin-token">
                  Admin Access Token *
                  {fetchStoreMutation.isPending && (
                    <span className="ml-2 text-xs text-blue-600">
                      (Loading store details...)
                    </span>
                  )}
                </Label>
                <Input
                  id="admin-token"
                  type="password"
                  placeholder="shpat_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                  value={manualStore.adminAccessToken}
                  onChange={(e) =>
                    setManualStore((prev) => ({
                      ...prev,
                      adminAccessToken: e.target.value,
                    }))
                  }
                  disabled={fetchStoreMutation.isPending}
                />
                <p className="text-xs text-muted-foreground">
                  Get this from Shopify Admin → Settings → Apps and sales
                  channels → Develop apps
                  {manualStore.adminAccessToken &&
                    !fetchStoreMutation.isPending && (
                      <span className="ml-1 text-green-600 font-medium">
                        (Auto-populated from existing store)
                      </span>
                    )}
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="shop-name">Store Display Name (Optional)</Label>
                <Input
                  id="shop-name"
                  placeholder="My Store"
                  value={manualStore.shopName}
                  onChange={(e) =>
                    setManualStore((prev) => ({
                      ...prev,
                      shopName: e.target.value,
                    }))
                  }
                />
                <p className="text-xs text-muted-foreground">
                  Leave blank to use domain as store name
                </p>
              </div>

              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  onClick={handleTestConnection}
                  disabled={testConnectionMutation.isPending}
                  className="flex-1"
                >
                  {testConnectionMutation.isPending ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <CheckCircle className="mr-2 h-4 w-4" />
                  )}
                  Test Connection
                </Button>

                <Button
                  onClick={handleManualStoreSubmit}
                  disabled={createStoreMutation.isPending}
                  className="flex-1"
                >
                  {createStoreMutation.isPending ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <ShoppingBag className="mr-2 h-4 w-4" />
                  )}
                  Create Store & Enable Sync
                </Button>
              </div>
            </CardContent>
          </Card>
        ) : (
          /* OAuth Connection Form */
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <ShoppingBag className="mr-2 h-5 w-5" />
                Connect Your Store
              </CardTitle>
              <CardDescription>
                Enter your Shopify store domain to get started
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="shop-domain">Store Prefix</Label>
                <div className="flex">
                  <Input
                    id="shop-domain"
                    placeholder="your-store-name"
                    value={shopDomain}
                    onChange={(e) => setShopDomain(e.target.value)}
                    disabled={isConnecting}
                    className="rounded-r-none"
                  />
                  <span className="inline-flex items-center px-3 text-sm text-gray-900 bg-gray-200 border border-l-0 border-gray-300 rounded-r-md">
                    .myshopify.com
                  </span>
                </div>
                <p className="text-xs text-muted-foreground">
                  Enter your store prefix (e.g., "my-store")
                </p>
              </div>

              <Button
                onClick={handleConnect}
                disabled={isConnecting || !shopDomain.trim()}
                className="w-full"
              >
                {isConnecting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Connecting...
                  </>
                ) : (
                  <>
                    <ExternalLink className="mr-2 h-4 w-4" />
                    Connect to Shopify
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Required Permissions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Shield className="mr-2 h-5 w-5" />
              Required Permissions
            </CardTitle>
            <CardDescription>
              ProductVideo will request the following permissions from your
              Shopify store
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {requiredScopes.map((scope) => (
                <div key={scope.name} className="flex items-start space-x-3">
                  <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                  <div>
                    <div className="font-medium text-sm">{scope.name}</div>
                    <div className="text-xs text-muted-foreground">
                      {scope.description}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Benefits */}
        <Card>
          <CardHeader>
            <CardTitle>Why Connect Your Store?</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <h4 className="font-medium">Seamless Integration</h4>
                <p className="text-sm text-muted-foreground">
                  Automatically sync products and push generated videos directly
                  to your store
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium">Advanced Analytics</h4>
                <p className="text-sm text-muted-foreground">
                  Track video performance, conversion rates, and customer
                  engagement
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium">Bulk Operations</h4>
                <p className="text-sm text-muted-foreground">
                  Generate videos for multiple products at once and manage them
                  efficiently
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium">Real-time Sync</h4>
                <p className="text-sm text-muted-foreground">
                  Keep your product catalog and videos in sync with automatic
                  updates
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Security Notice */}
        <Alert>
          <Shield className="h-4 w-4" />
          <AlertDescription>
            Your store data is secure. We only access the minimum required
            information to provide our services and never store sensitive
            customer or payment data.
          </AlertDescription>
        </Alert>
      </div>
      )}
    </div>
  );
}
