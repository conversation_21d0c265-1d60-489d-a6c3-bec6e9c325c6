'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  BookOpen, 
  Video, 
  Code, 
  Settings, 
  ShoppingCart, 
  BarChart3,
  Users,
  Store,
  ArrowRight,
  ExternalLink
} from 'lucide-react';
import useAnalytics from '@/hooks/useAnalytics';
import useNavigationTracking from '@/hooks/useNavigationTracking';

const DocsPage = () => {
  const { trackEvent } = useAnalytics();
  const { trackNavClick } = useNavigationTracking({ section: 'Documentation' });

  const handleDocClick = (title: string, href: string) => {
    trackEvent({
      action: 'documentation_click',
      category: 'Documentation',
      label: `${title} - ${href}`,
    });
    trackNavClick(title, href);
  };

  const documentationSections = [
    {
      title: 'Getting Started',
      description: 'Learn the basics of using our e-commerce platform',
      icon: BookOpen,
      color: 'bg-blue-500',
      items: [
        { title: 'Quick Start Guide', href: '/docs/quick-start', description: 'Get up and running in 5 minutes' },
        { title: 'Platform Overview', href: '/docs/overview', description: 'Understanding the platform features' },
        { title: 'Account Setup', href: '/docs/account-setup', description: 'Setting up your account and preferences' },
        { title: 'First Steps', href: '/docs/first-steps', description: 'Your first actions on the platform' },
      ]
    },
    {
      title: 'Product Management',
      description: 'Manage your products, inventory, and catalogs',
      icon: ShoppingCart,
      color: 'bg-green-500',
      items: [
        { title: 'Adding Products', href: '/docs/products/adding', description: 'How to add and configure products' },
        { title: 'Product Variants', href: '/docs/products/variants', description: 'Managing product variations' },
        { title: 'Inventory Management', href: '/docs/products/inventory', description: 'Track and manage stock levels' },
        { title: 'Product Categories', href: '/docs/products/categories', description: 'Organizing products with categories' },
      ]
    },
    {
      title: 'Video Generation',
      description: 'AI-powered video creation for your products',
      icon: Video,
      color: 'bg-purple-500',
      items: [
        { title: 'Video Templates', href: '/docs/videos/templates', description: 'Choose from pre-built templates' },
        { title: 'Custom Videos', href: '/docs/videos/custom', description: 'Create custom video content' },
        { title: 'Video Settings', href: '/docs/videos/settings', description: 'Configure video generation options' },
        { title: 'Publishing Videos', href: '/docs/videos/publishing', description: 'Share videos to your store' },
      ]
    },
    {
      title: 'Analytics & Reports',
      description: 'Track performance and gain insights',
      icon: BarChart3,
      color: 'bg-orange-500',
      items: [
        { title: 'Dashboard Overview', href: '/docs/analytics/dashboard', description: 'Understanding your analytics dashboard' },
        { title: 'Sales Reports', href: '/docs/analytics/sales', description: 'Track sales performance' },
        { title: 'Video Performance', href: '/docs/analytics/videos', description: 'Monitor video engagement' },
        { title: 'Custom Reports', href: '/docs/analytics/custom', description: 'Create custom analytics reports' },
      ]
    },
    {
      title: 'Store Management',
      description: 'Manage multiple stores and integrations',
      icon: Store,
      color: 'bg-teal-500',
      items: [
        { title: 'Shopify Integration', href: '/docs/stores/shopify', description: 'Connect and sync with Shopify' },
        { title: 'Multi-Store Setup', href: '/docs/stores/multi-store', description: 'Managing multiple stores' },
        { title: 'Store Settings', href: '/docs/stores/settings', description: 'Configure store preferences' },
        { title: 'Webhooks', href: '/docs/stores/webhooks', description: 'Set up automated notifications' },
      ]
    },
    {
      title: 'API & Integrations',
      description: 'Integrate with external systems and APIs',
      icon: Code,
      color: 'bg-red-500',
      items: [
        { title: 'API Documentation', href: '/docs/api/overview', description: 'Complete API reference' },
        { title: 'Authentication', href: '/docs/api/auth', description: 'API authentication methods' },
        { title: 'Webhooks', href: '/docs/api/webhooks', description: 'Real-time event notifications' },
        { title: 'SDKs', href: '/docs/api/sdks', description: 'Official SDKs and libraries' },
      ]
    },
  ];

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-4xl font-bold mb-4">Documentation</h1>
        <p className="text-xl text-muted-foreground mb-6">
          Everything you need to know about using our e-commerce platform
        </p>
        <div className="flex gap-4">
          <Button 
            onClick={() => handleDocClick('Quick Start Guide', '/docs/quick-start')}
            className="gap-2"
          >
            <BookOpen className="h-4 w-4" />
            Quick Start Guide
          </Button>
          <Button 
            variant="outline" 
            onClick={() => handleDocClick('API Documentation', '/docs/api/overview')}
            className="gap-2"
          >
            <Code className="h-4 w-4" />
            API Docs
            <ExternalLink className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Documentation Sections */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {documentationSections.map((section) => (
          <Card key={section.title} className="h-full">
            <CardHeader>
              <div className="flex items-center gap-3 mb-2">
                <div className={`p-2 rounded-lg ${section.color}`}>
                  <section.icon className="h-5 w-5 text-white" />
                </div>
                <div>
                  <CardTitle className="text-lg">{section.title}</CardTitle>
                  <Badge variant="secondary" className="mt-1">
                    {section.items.length} guides
                  </Badge>
                </div>
              </div>
              <CardDescription>{section.description}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {section.items.map((item) => (
                  <div
                    key={item.title}
                    className="flex items-center justify-between p-3 rounded-lg border hover:bg-muted/50 cursor-pointer transition-colors"
                    onClick={() => handleDocClick(item.title, item.href)}
                  >
                    <div className="flex-1">
                      <h4 className="font-medium text-sm">{item.title}</h4>
                      <p className="text-xs text-muted-foreground mt-1">{item.description}</p>
                    </div>
                    <ArrowRight className="h-4 w-4 text-muted-foreground" />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Additional Resources */}
      <div className="mt-12">
        <h2 className="text-2xl font-bold mb-6">Additional Resources</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Video className="h-5 w-5" />
                Video Tutorials
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-4">
                Watch step-by-step video guides for common tasks
              </p>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => handleDocClick('Video Tutorials', '/docs/videos')}
              >
                Watch Videos
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Community
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-4">
                Join our community forum for discussions and support
              </p>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => handleDocClick('Community Forum', '/community')}
              >
                Join Community
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Support
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-4">
                Need help? Contact our support team
              </p>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => handleDocClick('Contact Support', '/support')}
              >
                Get Support
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default DocsPage;
