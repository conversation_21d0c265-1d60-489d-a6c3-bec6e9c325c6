# Frontend Web Application

Next.js-based web dashboard for the E-commerce Platform providing user interface for AI-powered video generation and Shopify integration.

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- Backend API running (see backend README)

### 1. Setup

```bash
git clone <repository-url>
cd frontend
npm install
```

### 2. Environment Configuration

Create `.env.local` file:

```env
# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:8123
NEXT_PUBLIC_WS_URL=ws://localhost:8123

# Shopify Configuration
NEXT_PUBLIC_SHOPIFY_CLIENT_ID=your_shopify_client_id
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Analytics (optional)
NEXT_PUBLIC_GA_ID=your_google_analytics_id
```

### 3. Development Server

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) in your browser.

## 📋 Tech Stack

- **Framework**: Next.js 14 with App Router
- **UI Library**: shadcn/ui with Tailwind CSS
- **State Management**: React Query for server state
- **Forms**: React Hook Form with Zod validation
- **Testing**: Playwright for E2E, Jest for unit tests

## 🧪 Testing

```bash
# Unit tests
npm run test

# E2E tests
npm run test:e2e

# Test coverage
npm run test:coverage
```

## 📚 Documentation

Detailed documentation is available in the `docs/` folder:

- **[Monitoring Architecture](docs/monitoring-architecture.md)** - Comprehensive monitoring setup
- **[Sync Flow Documentation](docs/sync-flow-documentation.md)** - Complete system architecture and flows

## 🚀 Deployment

### Production Build

```bash
npm run build
npm run start
```

### Docker Deployment

```bash
docker build -t frontend .
docker run -p 3000:3000 frontend
```

## 🐛 Troubleshooting

### Common Issues

1. **API connection failed**
   ```bash
   curl http://localhost:8123/health
   ```

2. **Build errors**
   ```bash
   rm -rf .next node_modules
   npm install
   ```

## 📄 License

MIT License
