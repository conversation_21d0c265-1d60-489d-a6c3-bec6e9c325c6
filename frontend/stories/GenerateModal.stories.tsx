import type { Meta, StoryObj } from '@storybook/react';
import { GenerateModal } from '@/components/video-generation/GenerateModal';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

// Create a mock QueryClient for Storybook
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});

const meta: Meta<typeof GenerateModal> = {
  title: 'Video Generation/GenerateModal',
  component: GenerateModal,
  decorators: [
    (Story) => (
      <QueryClientProvider client={queryClient}>
        <div className="p-6 bg-gray-50 min-h-screen">
          <Story />
        </div>
      </QueryClientProvider>
    ),
  ],
  parameters: {
    layout: 'fullscreen',
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof meta>;

// Mock templates data
const mockTemplates = [
  {
    id: 'template-1',
    name: 'Product Showcase',
    description: 'Clean product presentation with smooth transitions and professional lighting',
    category: 'Showcase',
    thumbnailUrl: 'https://via.placeholder.com/200x120/4F46E5/FFFFFF?text=Showcase'
  },
  {
    id: 'template-2',
    name: 'Lifestyle Demo',
    description: 'Product in real-world usage scenarios with lifestyle context',
    category: 'Lifestyle',
    thumbnailUrl: 'https://via.placeholder.com/200x120/7C3AED/FFFFFF?text=Lifestyle'
  },
  {
    id: 'template-3',
    name: 'Feature Focus',
    description: 'Highlight key product features and benefits with callouts',
    category: 'Features',
    thumbnailUrl: 'https://via.placeholder.com/200x120/059669/FFFFFF?text=Features'
  },
  {
    id: 'template-4',
    name: 'Unboxing Experience',
    description: 'Exciting unboxing sequence that builds anticipation',
    category: 'Unboxing',
    thumbnailUrl: 'https://via.placeholder.com/200x120/DC2626/FFFFFF?text=Unboxing'
  },
  {
    id: 'template-5',
    name: 'Comparison View',
    description: 'Side-by-side comparison with competitors or variants',
    category: 'Comparison',
    thumbnailUrl: 'https://via.placeholder.com/200x120/F59E0B/FFFFFF?text=Compare'
  }
];

// Mock voices data
const mockVoices = [
  {
    id: 'voice-1',
    name: 'Sarah',
    gender: 'Female',
    accent: 'American',
    sampleUrl: '/voices/sarah-sample.mp3'
  },
  {
    id: 'voice-2',
    name: 'David',
    gender: 'Male',
    accent: 'British',
    sampleUrl: '/voices/david-sample.mp3'
  },
  {
    id: 'voice-3',
    name: 'Emma',
    gender: 'Female',
    accent: 'Australian',
    sampleUrl: '/voices/emma-sample.mp3'
  },
  {
    id: 'voice-4',
    name: 'Carlos',
    gender: 'Male',
    accent: 'Spanish',
    sampleUrl: '/voices/carlos-sample.mp3'
  },
  {
    id: 'voice-5',
    name: 'Yuki',
    gender: 'Female',
    accent: 'Japanese',
    sampleUrl: '/voices/yuki-sample.mp3'
  }
];

// Mock the video service
const mockVideoService = {
  getTemplates: () => Promise.resolve({ templates: mockTemplates }),
  getVoices: () => Promise.resolve({ voices: mockVoices }),
  generateVideos: (params: any) => {
    console.log('Generate videos with params:', params);
    return Promise.resolve({
      job: {
        id: 'job-123',
        status: 'pending',
        progress: 0
      }
    });
  },
  getJobStatus: (jobId: string) => {
    console.log('Get job status for:', jobId);
    return Promise.resolve({
      id: jobId,
      status: 'processing',
      progress: 45,
      variants: [
        {
          id: 'variant-1',
          productId: 'product-1',
          variantName: 'Variant 1',
          status: 'ready',
          videoUrl: 'https://example.com/video1.mp4',
          thumbnailUrl: 'https://example.com/thumb1.jpg'
        },
        {
          id: 'variant-2',
          productId: 'product-1',
          variantName: 'Variant 2',
          status: 'generating'
        }
      ]
    });
  }
};

// Override the service for Storybook
if (typeof window !== 'undefined') {
  (window as any).mockVideoService = mockVideoService;
}

export const Default: Story = {
  args: {
    open: true,
    onOpenChange: (open: boolean) => {
      console.log('Modal open state changed:', open);
    },
    selectedProducts: ['product-1', 'product-2'],
  },
};

export const SingleProduct: Story = {
  args: {
    open: true,
    onOpenChange: (open: boolean) => {
      console.log('Modal open state changed:', open);
    },
    selectedProducts: ['product-1'],
  },
};

export const ManyProducts: Story = {
  args: {
    open: true,
    onOpenChange: (open: boolean) => {
      console.log('Modal open state changed:', open);
    },
    selectedProducts: ['product-1', 'product-2', 'product-3', 'product-4', 'product-5'],
  },
};

export const Closed: Story = {
  args: {
    open: false,
    onOpenChange: (open: boolean) => {
      console.log('Modal open state changed:', open);
    },
    selectedProducts: ['product-1'],
  },
};

// Story showing the generating state
export const Generating: Story = {
  args: {
    open: true,
    onOpenChange: (open: boolean) => {
      console.log('Modal open state changed:', open);
    },
    selectedProducts: ['product-1', 'product-2'],
  },
  parameters: {
    mockData: {
      step: 'generating',
      currentJob: {
        id: 'job-123',
        status: 'processing',
        progress: 65,
        variants: [
          {
            id: 'variant-1',
            productId: 'product-1',
            variantName: 'Variant 1',
            status: 'ready'
          },
          {
            id: 'variant-2',
            productId: 'product-1',
            variantName: 'Variant 2',
            status: 'generating'
          }
        ]
      }
    }
  }
};

// Story showing the completed state
export const Completed: Story = {
  args: {
    open: true,
    onOpenChange: (open: boolean) => {
      console.log('Modal open state changed:', open);
    },
    selectedProducts: ['product-1'],
  },
  parameters: {
    mockData: {
      step: 'completed',
      currentJob: {
        id: 'job-123',
        status: 'completed',
        progress: 100,
        variants: [
          {
            id: 'variant-1',
            productId: 'product-1',
            variantName: 'Variant 1',
            status: 'ready',
            videoUrl: 'https://example.com/video1.mp4',
            thumbnailUrl: 'https://via.placeholder.com/320x180/4F46E5/FFFFFF?text=Video+1'
          },
          {
            id: 'variant-2',
            productId: 'product-1',
            variantName: 'Variant 2',
            status: 'ready',
            videoUrl: 'https://example.com/video2.mp4',
            thumbnailUrl: 'https://via.placeholder.com/320x180/7C3AED/FFFFFF?text=Video+2'
          }
        ]
      }
    }
  }
};
