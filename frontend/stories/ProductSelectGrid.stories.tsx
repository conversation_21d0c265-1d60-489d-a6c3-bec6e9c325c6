import type { Meta, StoryObj } from '@storybook/react';
import { ProductSelectGrid } from '@/components/video-generation/ProductSelectGrid';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

// Create a mock QueryClient for Storybook
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});

const meta: Meta<typeof ProductSelectGrid> = {
  title: 'Video Generation/ProductSelectGrid',
  component: ProductSelectGrid,
  decorators: [
    (Story) => (
      <QueryClientProvider client={queryClient}>
        <div className="p-6 bg-gray-50 min-h-screen">
          <Story />
        </div>
      </QueryClientProvider>
    ),
  ],
  parameters: {
    layout: 'fullscreen',
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof meta>;

// Mock products data
const mockProducts = [
  {
    id: 'product-1',
    title: 'Wireless Bluetooth Headphones - Premium Sound Quality',
    handle: 'wireless-bluetooth-headphones',
    images: [
      {
        id: 'img-1',
        src: 'https://via.placeholder.com/400x400/4F46E5/FFFFFF?text=Headphones',
        alt: 'Wireless Bluetooth Headphones'
      }
    ],
    variants: [
      {
        id: 'variant-1',
        title: 'Black',
        price: '99.99',
        sku: 'WBH-001-BLK'
      },
      {
        id: 'variant-2',
        title: 'White',
        price: '99.99',
        sku: 'WBH-001-WHT'
      },
      {
        id: 'variant-3',
        title: 'Silver',
        price: '109.99',
        sku: 'WBH-001-SLV'
      }
    ],
    tags: ['electronics', 'audio', 'wireless', 'premium'],
    productType: 'Electronics',
    vendor: 'AudioTech',
    status: 'active'
  },
  {
    id: 'product-2',
    title: 'Smart Fitness Watch with Heart Rate Monitor',
    handle: 'smart-fitness-watch',
    images: [
      {
        id: 'img-2',
        src: 'https://via.placeholder.com/400x400/7C3AED/FFFFFF?text=Watch',
        alt: 'Smart Fitness Watch'
      }
    ],
    variants: [
      {
        id: 'variant-4',
        title: 'Small - Black',
        price: '199.99',
        sku: 'SFW-001-SM-BLK'
      },
      {
        id: 'variant-5',
        title: 'Large - Black',
        price: '199.99',
        sku: 'SFW-001-LG-BLK'
      }
    ],
    tags: ['fitness', 'wearable', 'smart', 'health'],
    productType: 'Wearables',
    vendor: 'FitTech',
    status: 'active'
  },
  {
    id: 'product-3',
    title: 'Organic Cotton T-Shirt - Sustainable Fashion',
    handle: 'organic-cotton-tshirt',
    images: [
      {
        id: 'img-3',
        src: 'https://via.placeholder.com/400x400/059669/FFFFFF?text=T-Shirt',
        alt: 'Organic Cotton T-Shirt'
      }
    ],
    variants: [
      {
        id: 'variant-6',
        title: 'Small - White',
        price: '29.99',
        sku: 'OCT-001-SM-WHT'
      },
      {
        id: 'variant-7',
        title: 'Medium - White',
        price: '29.99',
        sku: 'OCT-001-MD-WHT'
      },
      {
        id: 'variant-8',
        title: 'Large - White',
        price: '29.99',
        sku: 'OCT-001-LG-WHT'
      },
      {
        id: 'variant-9',
        title: 'Small - Black',
        price: '29.99',
        sku: 'OCT-001-SM-BLK'
      }
    ],
    tags: ['clothing', 'organic', 'sustainable', 'cotton'],
    productType: 'Apparel',
    vendor: 'EcoWear',
    status: 'active'
  },
  {
    id: 'product-4',
    title: 'Professional Camera Lens - 50mm f/1.8',
    handle: 'professional-camera-lens',
    images: [
      {
        id: 'img-4',
        src: 'https://via.placeholder.com/400x400/DC2626/FFFFFF?text=Lens',
        alt: 'Professional Camera Lens'
      }
    ],
    variants: [
      {
        id: 'variant-10',
        title: 'Canon Mount',
        price: '299.99',
        sku: 'PCL-50-CAN'
      },
      {
        id: 'variant-11',
        title: 'Nikon Mount',
        price: '299.99',
        sku: 'PCL-50-NIK'
      }
    ],
    tags: ['photography', 'lens', 'professional', 'camera'],
    productType: 'Photography',
    vendor: 'LensCraft',
    status: 'active'
  }
];

const mockCollections = [
  { id: 'collection-1', title: 'Electronics', handle: 'electronics' },
  { id: 'collection-2', title: 'Fitness & Health', handle: 'fitness-health' },
  { id: 'collection-3', title: 'Sustainable Fashion', handle: 'sustainable-fashion' },
  { id: 'collection-4', title: 'Photography Gear', handle: 'photography-gear' }
];

const mockTags = [
  'electronics', 'audio', 'wireless', 'premium', 'fitness', 'wearable', 
  'smart', 'health', 'clothing', 'organic', 'sustainable', 'cotton',
  'photography', 'lens', 'professional', 'camera'
];

// Mock the Shopify service
const mockShopifyService = {
  getProducts: (params: any) => {
    let filteredProducts = [...mockProducts];
    
    // Apply search filter
    if (params.search) {
      filteredProducts = filteredProducts.filter(product =>
        product.title.toLowerCase().includes(params.search.toLowerCase())
      );
    }
    
    // Apply tag filter
    if (params.tag) {
      filteredProducts = filteredProducts.filter(product =>
        product.tags.includes(params.tag)
      );
    }
    
    return Promise.resolve({
      products: filteredProducts,
      total: filteredProducts.length
    });
  },
  getCollections: () => Promise.resolve({ collections: mockCollections }),
  getProductTags: () => Promise.resolve({ tags: mockTags })
};

// Override the service for Storybook
if (typeof window !== 'undefined') {
  (window as any).mockShopifyService = mockShopifyService;
}

export const Default: Story = {
  args: {
    selectedProducts: [],
    onSelectionChange: (productIds: string[]) => {
      console.log('Selection changed:', productIds);
    },
  },
};

export const WithSelection: Story = {
  args: {
    selectedProducts: ['product-1', 'product-3'],
    onSelectionChange: (productIds: string[]) => {
      console.log('Selection changed:', productIds);
    },
  },
};

export const Loading: Story = {
  args: {
    selectedProducts: [],
    onSelectionChange: (productIds: string[]) => {
      console.log('Selection changed:', productIds);
    },
  },
  parameters: {
    mockData: {
      isLoading: true,
    },
  },
};

export const Empty: Story = {
  args: {
    selectedProducts: [],
    onSelectionChange: (productIds: string[]) => {
      console.log('Selection changed:', productIds);
    },
  },
  parameters: {
    mockData: {
      products: [],
      total: 0,
    },
  },
};

export const Error: Story = {
  args: {
    selectedProducts: [],
    onSelectionChange: (productIds: string[]) => {
      console.log('Selection changed:', productIds);
    },
  },
  parameters: {
    mockData: {
      error: new Error('Failed to load products'),
    },
  },
};
