import type { <PERSON>a, StoryObj } from '@storybook/react';
import { VariantCards } from '@/components/video-generation/VariantCards';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

// Create a mock QueryClient for Storybook
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});

const meta: Meta<typeof VariantCards> = {
  title: 'Video Generation/VariantCards',
  component: VariantCards,
  decorators: [
    (Story) => (
      <QueryClientProvider client={queryClient}>
        <div className="p-6 bg-gray-50 min-h-screen">
          <Story />
        </div>
      </QueryClientProvider>
    ),
  ],
  parameters: {
    layout: 'fullscreen',
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof meta>;

// Mock data for stories
const mockVariants = [
  {
    id: 'variant-1',
    jobId: 'job-1',
    productId: 'product-123',
    variantName: 'Energetic Showcase',
    videoUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
    thumbnailUrl: 'https://via.placeholder.com/320x180/4F46E5/FFFFFF?text=Video+1',
    duration: 30,
    status: 'ready' as const,
    isFavorite: false,
    metrics: {
      views: 1250,
      plays: 890,
      completionRate: 78.5,
      avgWatchTime: 23.4,
      ctaClicks: 45,
      addToCarts: 12
    },
    versions: [
      { id: 'v1', createdAt: '2024-01-15T10:30:00Z', isActive: true }
    ]
  },
  {
    id: 'variant-2',
    jobId: 'job-1',
    productId: 'product-123',
    variantName: 'Calm & Professional',
    videoUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4',
    thumbnailUrl: 'https://via.placeholder.com/320x180/7C3AED/FFFFFF?text=Video+2',
    duration: 25,
    status: 'ready' as const,
    isFavorite: true,
    metrics: {
      views: 980,
      plays: 720,
      completionRate: 82.1,
      avgWatchTime: 20.5,
      ctaClicks: 38,
      addToCarts: 15
    },
    versions: [
      { id: 'v1', createdAt: '2024-01-15T10:30:00Z', isActive: true }
    ]
  },
  {
    id: 'variant-3',
    jobId: 'job-2',
    productId: 'product-456',
    variantName: 'Dynamic Features',
    videoUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4',
    thumbnailUrl: 'https://via.placeholder.com/320x180/059669/FFFFFF?text=Video+3',
    duration: 35,
    status: 'generating' as const,
    isFavorite: false,
    metrics: {
      views: 0,
      plays: 0,
      completionRate: 0,
      avgWatchTime: 0,
      ctaClicks: 0,
      addToCarts: 0
    },
    versions: [
      { id: 'v1', createdAt: '2024-01-15T11:00:00Z', isActive: true }
    ]
  },
  {
    id: 'variant-4',
    jobId: 'job-3',
    productId: 'product-789',
    variantName: 'Failed Generation',
    videoUrl: '',
    thumbnailUrl: '',
    duration: 0,
    status: 'failed' as const,
    isFavorite: false,
    metrics: {
      views: 0,
      plays: 0,
      completionRate: 0,
      avgWatchTime: 0,
      ctaClicks: 0,
      addToCarts: 0
    },
    versions: []
  }
];

// Mock the video service
const mockVideoService = {
  getVariants: () => Promise.resolve({ variants: mockVariants }),
  toggleFavorite: (variantId: string) => {
    console.log('Toggle favorite:', variantId);
    return Promise.resolve({ success: true });
  },
  regenerateVariant: (variantId: string) => {
    console.log('Regenerate variant:', variantId);
    return Promise.resolve({ success: true });
  },
  trackEvent: (event: any) => {
    console.log('Track event:', event);
    return Promise.resolve({ success: true });
  }
};

// Override the service for Storybook
if (typeof window !== 'undefined') {
  (window as any).mockVideoService = mockVideoService;
}

export const Default: Story = {
  args: {
    selectedVariants: [],
    onSelectionChange: (variantIds: string[]) => {
      console.log('Selection changed:', variantIds);
    },
    onPushSelected: () => {
      console.log('Push selected variants');
    },
  },
};

export const WithSelection: Story = {
  args: {
    selectedVariants: ['variant-1', 'variant-2'],
    onSelectionChange: (variantIds: string[]) => {
      console.log('Selection changed:', variantIds);
    },
    onPushSelected: () => {
      console.log('Push selected variants');
    },
  },
};

export const Loading: Story = {
  args: {
    selectedVariants: [],
    onSelectionChange: (variantIds: string[]) => {
      console.log('Selection changed:', variantIds);
    },
    onPushSelected: () => {
      console.log('Push selected variants');
    },
  },
  parameters: {
    mockData: {
      isLoading: true,
    },
  },
};

export const Empty: Story = {
  args: {
    selectedVariants: [],
    onSelectionChange: (variantIds: string[]) => {
      console.log('Selection changed:', variantIds);
    },
    onPushSelected: () => {
      console.log('Push selected variants');
    },
  },
  parameters: {
    mockData: {
      variants: [],
    },
  },
};
