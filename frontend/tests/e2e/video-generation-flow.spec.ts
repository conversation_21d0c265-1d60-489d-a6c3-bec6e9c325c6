import { test, expect } from '@playwright/test';

test.describe('Video Generation Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Mock API responses
    await page.route('**/api/shopify/products*', async (route) => {
      await route.fulfill({
        json: {
          products: [
            {
              id: 'product-1',
              title: 'Test Product 1',
              handle: 'test-product-1',
              images: [{ id: 'img-1', src: '/test-image.jpg', alt: 'Test Product' }],
              variants: [{ id: 'var-1', title: 'Default', price: '29.99' }],
              tags: ['test'],
              productType: 'Test',
              vendor: 'Test Vendor',
              status: 'active'
            }
          ],
          total: 1
        }
      });
    });

    await page.route('**/api/video-generation/templates*', async (route) => {
      await route.fulfill({
        json: {
          templates: [
            {
              id: 'template-1',
              name: 'Test Template',
              description: 'A test template',
              category: 'Test'
            }
          ]
        }
      });
    });

    await page.route('**/api/video-generation/voices*', async (route) => {
      await route.fulfill({
        json: {
          voices: [
            {
              id: 'voice-1',
              name: 'Test Voice',
              gender: 'Female',
              accent: 'American'
            }
          ]
        }
      });
    });

    // Navigate to video generation page
    await page.goto('/video-generation');
  });

  test('should complete full video generation flow', async ({ page }) => {
    // Step 1: Select products
    await expect(page.getByText('Select Products')).toBeVisible();
    
    // Wait for products to load
    await expect(page.getByText('Test Product 1')).toBeVisible();
    
    // Select a product
    await page.getByRole('checkbox').first().click();
    
    // Verify product is selected
    await expect(page.getByText('1 product selected')).toBeVisible();
    
    // Step 2: Open generate modal
    await page.getByRole('button', { name: /generate videos/i }).click();
    
    // Verify modal is open
    await expect(page.getByText('Generate AI Videos')).toBeVisible();
    
    // Step 3: Configure video generation
    // Select template
    await page.getByRole('combobox').first().click();
    await page.getByText('Test Template').click();
    
    // Select voice
    await page.getByRole('combobox').nth(1).click();
    await page.getByText('Test Voice').click();
    
    // Mock the generation API
    await page.route('**/api/video-generation/generate*', async (route) => {
      await route.fulfill({
        json: {
          job: {
            id: 'job-123',
            status: 'pending'
          }
        }
      });
    });

    // Mock job status polling
    await page.route('**/api/video-generation/jobs/job-123/status*', async (route) => {
      await route.fulfill({
        json: {
          id: 'job-123',
          status: 'completed',
          progress: 100,
          variants: [
            {
              id: 'variant-1',
              productId: 'product-1',
              variantName: 'Variant 1',
              status: 'ready',
              videoUrl: '/test-video.mp4',
              thumbnailUrl: '/test-thumbnail.jpg'
            }
          ]
        }
      });
    });
    
    // Step 4: Start generation
    await page.getByRole('button', { name: /generate videos/i }).click();
    
    // Verify generation started
    await expect(page.getByText('Generating your videos...')).toBeVisible();
    
    // Wait for completion (mocked to be immediate)
    await expect(page.getByText('Generation Complete!')).toBeVisible({ timeout: 10000 });
    
    // Step 5: Close modal and verify variants appear
    await page.getByRole('button', { name: 'Done' }).click();
    
    // Navigate to variants tab
    await page.getByRole('tab', { name: 'Variants' }).click();
    
    // Verify variants are displayed
    await expect(page.getByText('Variant 1')).toBeVisible();
  });

  test('should handle video preview and controls', async ({ page }) => {
    // Mock variants API
    await page.route('**/api/video-generation/variants*', async (route) => {
      await route.fulfill({
        json: {
          variants: [
            {
              id: 'variant-1',
              jobId: 'job-1',
              productId: 'product-1',
              variantName: 'Test Variant',
              videoUrl: '/test-video.mp4',
              thumbnailUrl: '/test-thumbnail.jpg',
              duration: 30,
              status: 'ready',
              isFavorite: false,
              metrics: {
                views: 100,
                plays: 80,
                completionRate: 75,
                avgWatchTime: 22,
                ctaClicks: 5,
                addToCarts: 2
              },
              versions: [{ id: 'v1', createdAt: '2024-01-01T00:00:00Z', isActive: true }]
            }
          ]
        }
      });
    });

    // Navigate to variants tab
    await page.getByRole('tab', { name: 'Variants' }).click();
    
    // Wait for variants to load
    await expect(page.getByText('Test Variant')).toBeVisible();
    
    // Verify video metrics are displayed
    await expect(page.getByText('100 views')).toBeVisible();
    await expect(page.getByText('80 plays')).toBeVisible();
    await expect(page.getByText('75% completion')).toBeVisible();
    
    // Test video selection
    await page.getByRole('checkbox').first().click();
    await expect(page.getByText('1 variant selected')).toBeVisible();
    
    // Test favorite toggle
    await page.getByRole('button', { name: /more/i }).click();
    await page.getByText('Add to Favorites').click();
    
    // Test regeneration
    await page.getByRole('button', { name: /more/i }).click();
    await page.getByText('Regenerate').click();
  });

  test('should handle push to Shopify flow', async ({ page }) => {
    // Mock variants and stores
    await page.route('**/api/video-generation/variants*', async (route) => {
      await route.fulfill({
        json: {
          variants: [
            {
              id: 'variant-1',
              jobId: 'job-1',
              productId: 'product-1',
              variantName: 'Test Variant',
              videoUrl: '/test-video.mp4',
              status: 'ready',
              isFavorite: false,
              metrics: { views: 0, plays: 0, completionRate: 0, avgWatchTime: 0, ctaClicks: 0, addToCarts: 0 },
              versions: []
            }
          ]
        }
      });
    });

    await page.route('**/api/shopify/stores*', async (route) => {
      await route.fulfill({
        json: {
          stores: [
            {
              id: 'store-1',
              name: 'Test Store',
              domain: 'test-store.myshopify.com',
              isConnected: true
            }
          ]
        }
      });
    });

    // Navigate to variants and select one
    await page.getByRole('tab', { name: 'Variants' }).click();
    await page.getByRole('checkbox').first().click();
    
    // Open push modal
    await page.getByRole('button', { name: /push to shopify/i }).click();
    
    // Verify push modal is open
    await expect(page.getByText('Push Videos to Shopify')).toBeVisible();
    
    // Configure alt text
    await page.getByPlaceholder(/alt text/i).fill('Test product video');
    
    // Mock push API
    await page.route('**/api/video-generation/push-to-shopify*', async (route) => {
      await route.fulfill({
        json: {
          job: {
            id: 'push-job-123',
            status: 'pending'
          }
        }
      });
    });

    await page.route('**/api/video-generation/push-jobs/push-job-123/status*', async (route) => {
      await route.fulfill({
        json: {
          id: 'push-job-123',
          status: 'completed',
          progress: 100,
          results: [
            {
              variantId: 'variant-1',
              productId: 'product-1',
              success: true,
              mediaId: 'media-123'
            }
          ]
        }
      });
    });
    
    // Start push
    await page.getByRole('button', { name: /push to shopify/i }).click();
    
    // Verify push started
    await expect(page.getByText('Pushing to Shopify')).toBeVisible();
    
    // Wait for completion
    await expect(page.getByText('Push Complete!')).toBeVisible({ timeout: 10000 });
  });

  test('should handle gallery search and filtering', async ({ page }) => {
    // Mock gallery API
    await page.route('**/api/video-generation/gallery*', async (route) => {
      const url = new URL(route.request().url());
      const search = url.searchParams.get('search');
      
      let items = [
        {
          id: 'item-1',
          variantName: 'Product A Video',
          productId: 'product-1',
          productTitle: 'Product A',
          videoUrl: '/video1.mp4',
          thumbnailUrl: '/thumb1.jpg',
          duration: 30,
          createdAt: '2024-01-01T00:00:00Z',
          status: 'ready',
          isFavorite: false,
          tags: ['electronics'],
          metrics: { views: 100, plays: 80, completionRate: 75 }
        },
        {
          id: 'item-2',
          variantName: 'Product B Video',
          productId: 'product-2',
          productTitle: 'Product B',
          videoUrl: '/video2.mp4',
          thumbnailUrl: '/thumb2.jpg',
          duration: 25,
          createdAt: '2024-01-02T00:00:00Z',
          status: 'ready',
          isFavorite: true,
          tags: ['clothing'],
          metrics: { views: 150, plays: 120, completionRate: 80 }
        }
      ];

      // Apply search filter
      if (search) {
        items = items.filter(item => 
          item.variantName.toLowerCase().includes(search.toLowerCase()) ||
          item.productTitle.toLowerCase().includes(search.toLowerCase())
        );
      }

      await route.fulfill({
        json: { items, total: items.length }
      });
    });

    await page.route('**/api/video-generation/gallery/tags*', async (route) => {
      await route.fulfill({
        json: { tags: ['electronics', 'clothing'] }
      });
    });

    // Navigate to gallery
    await page.getByRole('tab', { name: 'Gallery' }).click();
    
    // Wait for items to load
    await expect(page.getByText('Product A Video')).toBeVisible();
    await expect(page.getByText('Product B Video')).toBeVisible();
    
    // Test search functionality
    await page.getByPlaceholder('Search videos...').fill('Product A');
    
    // Verify search results
    await expect(page.getByText('Product A Video')).toBeVisible();
    await expect(page.getByText('Product B Video')).not.toBeVisible();
    
    // Clear search
    await page.getByPlaceholder('Search videos...').clear();
    
    // Test bulk selection
    await page.getByRole('checkbox').first().click(); // Select all checkbox
    await expect(page.getByText('2 videos selected')).toBeVisible();
    
    // Test bulk actions
    await expect(page.getByRole('button', { name: /download/i })).toBeVisible();
    await expect(page.getByRole('button', { name: /delete/i })).toBeVisible();
  });

  test('should display analytics dashboard', async ({ page }) => {
    // Mock analytics API
    await page.route('**/api/analytics/dashboard*', async (route) => {
      await route.fulfill({
        json: {
          analytics: {
            overview: {
              totalViews: 5420,
              totalPlays: 3890,
              avgCompletionRate: 78.5,
              avgWatchTime: 23.4,
              totalConversions: 156,
              totalRevenue: 4680.50,
              viewsChange: 12.5,
              playsChange: 8.3,
              completionRateChange: 2.1,
              conversionsChange: 15.7
            },
            chartData: {
              daily: [
                { date: '2024-01-01', views: 100, plays: 80, completions: 60, conversions: 5 },
                { date: '2024-01-02', views: 120, plays: 95, completions: 75, conversions: 8 }
              ],
              hourly: [
                { hour: '00:00', views: 10, plays: 8 },
                { hour: '01:00', views: 5, plays: 4 }
              ]
            },
            conversionFunnel: [
              { stage: 'Views', count: 5420, percentage: 100 },
              { stage: 'Plays', count: 3890, percentage: 71.8 },
              { stage: 'Completions', count: 3050, percentage: 56.3 },
              { stage: 'CTA Clicks', count: 890, percentage: 16.4 },
              { stage: 'Conversions', count: 156, percentage: 2.9 }
            ],
            deviceBreakdown: [
              { device: 'Desktop', count: 3250, percentage: 60 },
              { device: 'Mobile', count: 1890, percentage: 35 },
              { device: 'Tablet', count: 280, percentage: 5 }
            ],
            topPerformers: [
              {
                variantId: 'variant-1',
                variantName: 'Top Video',
                productTitle: 'Best Product',
                views: 1200,
                plays: 950,
                completionRate: 85.2,
                conversions: 45,
                revenue: 1350.00
              }
            ]
          }
        }
      });
    });

    // Navigate to analytics
    await page.getByRole('tab', { name: 'Analytics' }).click();
    
    // Verify overview metrics
    await expect(page.getByText('5.4K')).toBeVisible(); // Total views
    await expect(page.getByText('3.9K')).toBeVisible(); // Total plays
    await expect(page.getByText('78.5%')).toBeVisible(); // Completion rate
    await expect(page.getByText('156')).toBeVisible(); // Conversions
    
    // Test tab navigation
    await page.getByRole('tab', { name: 'Conversion Funnel' }).click();
    await expect(page.getByText('Track how viewers progress')).toBeVisible();
    
    await page.getByRole('tab', { name: 'Audience' }).click();
    await expect(page.getByText('Device Breakdown')).toBeVisible();
    
    await page.getByRole('tab', { name: 'Top Performers' }).click();
    await expect(page.getByText('Top Video')).toBeVisible();
  });
});
