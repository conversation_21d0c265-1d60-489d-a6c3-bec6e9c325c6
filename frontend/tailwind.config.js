/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
    "./node_modules/@tremor/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        // Custom colors from index.css @theme block
        primary: {
          50: 'var(--color-primary-50)',
          100: 'var(--color-primary-100)',
          200: 'var(--color-primary-200)',
          300: 'var(--color-primary-300)',
          400: 'var(--color-primary-400)',
          500: 'var(--color-primary-500)',
          600: 'var(--color-primary-600)',
          700: 'var(--color-primary-700)',
          800: 'var(--color-primary-800)',
          900: 'var(--color-primary-900)',
          950: 'var(--color-primary-950)',
        },
        secondary: {
          50: 'var(--color-secondary-50)',
          100: 'var(--color-secondary-100)',
          200: 'var(--color-secondary-200)',
          300: 'var(--color-secondary-300)',
          400: 'var(--color-secondary-400)',
          500: 'var(--color-secondary-500)',
          600: 'var(--color-secondary-600)',
          700: 'var(--color-secondary-700)',
          800: 'var(--color-secondary-800)',
          900: 'var(--color-secondary-900)',
          950: 'var(--color-secondary-950)',
        },
        accent: {
          50: 'var(--color-accent-50)',
          100: 'var(--color-accent-100)',
          200: 'var(--color-accent-200)',
          300: 'var(--color-accent-300)',
          400: 'var(--color-accent-400)',
          500: 'var(--color-accent-500)',
          600: 'var(--color-accent-600)',
          700: 'var(--color-accent-700)',
          800: 'var(--color-accent-800)',
          900: 'var(--color-accent-900)',
          950: 'var(--color-accent-950)',
        },
        // Tremor default colors (optional, can be overridden by custom colors)
        tremor: {
          brand: {
            faint: 'var(--color-primary-50)',
            muted: 'var(--color-primary-200)',
            subtle: 'var(--color-primary-400)',
            DEFAULT: 'var(--color-primary-500)',
            emphasis: 'var(--color-primary-700)',
            inverted: 'var(--color-primary-950)',
          },
          background: {
            muted: 'var(--color-secondary-50)',
            subtle: 'var(--color-secondary-100)',
            DEFAULT: 'var(--color-secondary-200)',
            emphasis: 'var(--color-secondary-800)',
          },
          border: {
            DEFAULT: 'var(--color-secondary-300)',
          },
          ring: {
            DEFAULT: 'var(--color-primary-300)',
          },
          content: {
            subtle: 'var(--color-secondary-500)',
            DEFAULT: 'var(--color-secondary-700)',
            emphasis: 'var(--color-secondary-900)',
            strong: 'var(--color-secondary-950)',
            inverted: 'var(--color-secondary-50)',
          },
        },
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
      },
      boxShadow: {
        // Custom shadows from index.css @theme block
        'glass': 'var(--shadow-glass)',
        'glass-inset': 'var(--shadow-glass-inset)',
        'elegant': 'var(--shadow-elegant)',
        'elegant-lg': 'var(--shadow-elegant-lg)',
      },
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
  ],
}
