# Backend API Server

FastAPI-based backend for the E-commerce Platform providing REST APIs, background processing, and data synchronization.

## 🚀 Quick Start

### Prerequisites

- Python 3.11+
- Docker & Docker Compose
- PostgreSQL 15
- Redis 7

### 1. Setup

```bash
git clone <repository-url>
cd backend
cp .env.example .env
# Edit .env with your configuration
```

### 2. Start Services

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f backend

# Run database migrations
docker-compose exec backend python -m alembic upgrade head
```

### 3. Access Points

- **API Documentation**: http://localhost:8123/docs
- **Health Check**: http://localhost:8123/health
- **Metrics**: http://localhost:8123/metrics

## 📋 Services

| Service | Port | Purpose |
|---------|------|---------|
| **API Server** | 8123 | REST API with authentication |
| **Worker** | 9091 | Background task processing |
| **PostgreSQL** | 5432 | Primary database |
| **Redis** | 6379 | Caching and task queuing |

## 🔧 Configuration

### Environment Variables

```bash
# Database
DATABASE_URL=postgresql://user:password@localhost:5432/ecommerce_db

# Redis
REDIS_URL=redis://localhost:6379

# Airbyte
AIRBYTE_API_URL=http://localhost:8001
AIRBYTE_USERNAME=airbyte
AIRBYTE_PASSWORD=password

# Shopify
SHOPIFY_API_KEY=your_api_key
SHOPIFY_API_SECRET=your_api_secret
SHOPIFY_WEBHOOK_SECRET=your_webhook_secret

# AI Providers
GOOGLE_VEO_API_KEY=your_veo_key
BANANA_API_KEY=your_banana_key
```

## 🧪 Testing

```bash
# Run tests
cd backend && python -m pytest tests/ -v

# Run with coverage
cd backend && python -m pytest tests/ --cov=src --cov-report=html
```

## 📚 Documentation

Detailed documentation is available in the `docs/` folder:

- **[Monitoring Architecture](docs/monitoring-architecture.md)** - Comprehensive monitoring setup
- **[Sync Flow Documentation](docs/sync-flow-documentation.md)** - Complete system architecture and flows

## 🐛 Troubleshooting

### Common Issues

1. **Service won't start**
   ```bash
   docker-compose logs backend
   ```

2. **Database connection issues**
   ```bash
   docker-compose exec postgres pg_isready
   ```

3. **API not responding**
   ```bash
   curl http://localhost:8123/health
   ```

## 📄 License

MIT License
