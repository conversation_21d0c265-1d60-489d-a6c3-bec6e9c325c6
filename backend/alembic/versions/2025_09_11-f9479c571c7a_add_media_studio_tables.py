"""Add media studio tables

Revision ID: f9479c571c7a
Revises: 5c3703f39429
Create Date: 2025-09-11 11:23:51.710833

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f9479c571c7a'
down_revision: Union[str, Sequence[str], None] = '5c3703f39429'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('generated_assets',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('workspace_id', sa.Integer(), nullable=False),
    sa.Column('product_id', sa.String(), nullable=False),
    sa.Column('type', sa.String(), nullable=False),
    sa.Column('file_uri', sa.String(), nullable=False),
    sa.Column('preview_uri', sa.String(), nullable=True),
    sa.Column('prompt', sa.Text(), nullable=True),
    sa.Column('model', sa.String(), nullable=True),
    sa.Column('settings', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['workspace_id'], ['tenants.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_generated_assets_id'), 'generated_assets', ['id'], unique=False)
    op.create_table('generation_batches',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('workspace_id', sa.Integer(), nullable=False),
    sa.Column('mode', sa.String(), nullable=False),
    sa.Column('aspect_ratio', sa.String(), nullable=True),
    sa.Column('quality', sa.String(), nullable=True),
    sa.Column('model', sa.String(), nullable=False),
    sa.Column('requested_count', sa.Integer(), nullable=True),
    sa.Column('completed_count', sa.Integer(), nullable=True),
    sa.Column('failed_count', sa.Integer(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['workspace_id'], ['tenants.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_generation_batches_id'), 'generation_batches', ['id'], unique=False)
    op.create_table('generation_requests',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('batch_id', sa.String(), nullable=False),
    sa.Column('product_id', sa.String(), nullable=False),
    sa.Column('variant_id', sa.String(), nullable=True),
    sa.Column('prompt', sa.Text(), nullable=False),
    sa.Column('params_json', sa.JSON(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('result_url', sa.String(), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['batch_id'], ['generation_batches.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_generation_requests_id'), 'generation_requests', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_generation_requests_id'), table_name='generation_requests')
    op.drop_table('generation_requests')
    op.drop_index(op.f('ix_generation_batches_id'), table_name='generation_batches')
    op.drop_table('generation_batches')
    op.drop_index(op.f('ix_generated_assets_id'), table_name='generated_assets')
    op.drop_table('generated_assets')
    # ### end Alembic commands ###
