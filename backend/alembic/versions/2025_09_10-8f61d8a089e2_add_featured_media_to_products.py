"""add_featured_media_to_products

Revision ID: 8f61d8a089e2
Revises: 922cb53be8ab
Create Date: 2025-09-10 00:11:01.143511

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '8f61d8a089e2'
down_revision: Union[str, Sequence[str], None] = '922cb53be8ab'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Check if featured_media column already exists
    conn = op.get_bind()
    inspector = sa.inspect(conn)
    columns = [col['name'] for col in inspector.get_columns('products')]

    # Add featured_media column to products table if it doesn't exist
    if 'featured_media' not in columns:
        op.add_column('products', sa.Column('featured_media', sa.Text(), nullable=True))


def downgrade() -> None:
    """Downgrade schema."""
    # Remove featured_media column from products table
    op.drop_column('products', 'featured_media')
