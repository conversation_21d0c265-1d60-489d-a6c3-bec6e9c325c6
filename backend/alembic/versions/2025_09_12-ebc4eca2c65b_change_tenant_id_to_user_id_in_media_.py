"""change_tenant_id_to_user_id_in_media_tables

Revision ID: ebc4eca2c65b
Revises: a7d782ba205d
Create Date: 2025-09-12 13:40:35.979274

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'ebc4eca2c65b'
down_revision: Union[str, Sequence[str], None] = 'a7d782ba205d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Add user_id columns to all media tables
    op.add_column('media_jobs', sa.Column('user_id', sa.Integer(), nullable=True))
    op.add_column('media_variants', sa.Column('user_id', sa.Integer(), nullable=True))
    op.add_column('generation_batches', sa.Column('user_id', sa.Integer(), nullable=True))
    op.add_column('generated_assets', sa.Column('user_id', sa.Integer(), nullable=True))

    # Migrate data from tenant_id to user_id using tenant.owner_id relationship
    op.execute("""
        UPDATE media_jobs
        SET user_id = tenants.owner_id
        FROM tenants
        WHERE media_jobs.tenant_id = tenants.id
    """)

    op.execute("""
        UPDATE media_variants
        SET user_id = tenants.owner_id
        FROM tenants
        WHERE media_variants.tenant_id = tenants.id
    """)

    op.execute("""
        UPDATE generation_batches
        SET user_id = tenants.owner_id
        FROM tenants
        WHERE generation_batches.workspace_id = tenants.id
    """)

    op.execute("""
        UPDATE generated_assets
        SET user_id = tenants.owner_id
        FROM tenants
        WHERE generated_assets.workspace_id = tenants.id
    """)

    # Make user_id columns NOT NULL after data migration
    op.alter_column('media_jobs', 'user_id', nullable=False)
    op.alter_column('media_variants', 'user_id', nullable=False)
    op.alter_column('generation_batches', 'user_id', nullable=False)
    op.alter_column('generated_assets', 'user_id', nullable=False)

    # Add foreign key constraints to users table
    op.create_foreign_key(
        'fk_media_jobs_user_id',
        'media_jobs', 'users',
        ['user_id'], ['id']
    )
    op.create_foreign_key(
        'fk_media_variants_user_id',
        'media_variants', 'users',
        ['user_id'], ['id']
    )
    op.create_foreign_key(
        'fk_generation_batches_user_id',
        'generation_batches', 'users',
        ['user_id'], ['id']
    )
    op.create_foreign_key(
        'fk_generated_assets_user_id',
        'generated_assets', 'users',
        ['user_id'], ['id']
    )

    # Drop old foreign key constraints and tenant_id columns
    op.drop_constraint('media_jobs_tenant_id_fkey', 'media_jobs', type_='foreignkey')
    op.drop_constraint('media_variants_tenant_id_fkey', 'media_variants', type_='foreignkey')
    op.drop_constraint('generation_batches_workspace_id_fkey', 'generation_batches', type_='foreignkey')
    op.drop_constraint('generated_assets_workspace_id_fkey', 'generated_assets', type_='foreignkey')

    op.drop_column('media_jobs', 'tenant_id')
    op.drop_column('media_variants', 'tenant_id')
    op.drop_column('generation_batches', 'workspace_id')
    op.drop_column('generated_assets', 'workspace_id')


def downgrade() -> None:
    """Downgrade schema."""
    # Add back tenant_id/workspace_id columns
    op.add_column('media_jobs', sa.Column('tenant_id', sa.Integer(), nullable=True))
    op.add_column('media_variants', sa.Column('tenant_id', sa.Integer(), nullable=True))
    op.add_column('generation_batches', sa.Column('workspace_id', sa.Integer(), nullable=True))
    op.add_column('generated_assets', sa.Column('workspace_id', sa.Integer(), nullable=True))

    # Migrate data back from user_id to tenant_id (this is approximate - assumes user belongs to one tenant)
    op.execute("""
        UPDATE media_jobs
        SET tenant_id = tenants.id
        FROM tenants
        WHERE media_jobs.user_id = tenants.owner_id
    """)

    op.execute("""
        UPDATE media_variants
        SET tenant_id = tenants.id
        FROM tenants
        WHERE media_variants.user_id = tenants.owner_id
    """)

    op.execute("""
        UPDATE generation_batches
        SET workspace_id = tenants.id
        FROM tenants
        WHERE generation_batches.user_id = tenants.owner_id
    """)

    op.execute("""
        UPDATE generated_assets
        SET workspace_id = tenants.id
        FROM tenants
        WHERE generated_assets.user_id = tenants.owner_id
    """)

    # Make tenant_id/workspace_id columns NOT NULL
    op.alter_column('media_jobs', 'tenant_id', nullable=False)
    op.alter_column('media_variants', 'tenant_id', nullable=False)
    op.alter_column('generation_batches', 'workspace_id', nullable=False)
    op.alter_column('generated_assets', 'workspace_id', nullable=False)

    # Add back foreign key constraints to tenants table
    op.create_foreign_key(
        'media_jobs_tenant_id_fkey',
        'media_jobs', 'tenants',
        ['tenant_id'], ['id']
    )
    op.create_foreign_key(
        'media_variants_tenant_id_fkey',
        'media_variants', 'tenants',
        ['tenant_id'], ['id']
    )
    op.create_foreign_key(
        'generation_batches_workspace_id_fkey',
        'generation_batches', 'tenants',
        ['workspace_id'], ['id']
    )
    op.create_foreign_key(
        'generated_assets_workspace_id_fkey',
        'generated_assets', 'tenants',
        ['workspace_id'], ['id']
    )

    # Drop user_id foreign key constraints and columns
    op.drop_constraint('fk_media_jobs_user_id', 'media_jobs', type_='foreignkey')
    op.drop_constraint('fk_media_variants_user_id', 'media_variants', type_='foreignkey')
    op.drop_constraint('fk_generation_batches_user_id', 'generation_batches', type_='foreignkey')
    op.drop_constraint('fk_generated_assets_user_id', 'generated_assets', type_='foreignkey')

    op.drop_column('media_jobs', 'user_id')
    op.drop_column('media_variants', 'user_id')
    op.drop_column('generation_batches', 'user_id')
    op.drop_column('generated_assets', 'user_id')
