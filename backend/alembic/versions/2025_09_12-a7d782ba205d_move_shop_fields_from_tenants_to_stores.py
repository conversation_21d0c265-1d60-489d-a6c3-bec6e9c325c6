"""move_shop_fields_from_tenants_to_stores

Revision ID: a7d782ba205d
Revises: 5c006f849ca3
Create Date: 2025-09-12 13:40:08.333997

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'a7d782ba205d'
down_revision: Union[str, Sequence[str], None] = '5c006f849ca3'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Note: shop_domain and shop_id columns already exist in stores table from initial migration
    # Just migrate data from tenants to stores (if any exists)
    op.execute("""
        UPDATE stores
        SET shop_domain = tenants.shop_domain,
            shop_id = tenants.shop_id
        FROM tenants
        WHERE stores.tenant_id = tenants.id
    """)

    # Remove shop_domain and shop_id columns from tenants table
    op.drop_column('tenants', 'shop_domain')
    op.drop_column('tenants', 'shop_id')


def downgrade() -> None:
    """Downgrade schema."""
    # Add shop_domain and shop_id columns back to tenants table
    op.add_column('tenants', sa.Column('shop_domain', sa.String(length=255), nullable=True))
    op.add_column('tenants', sa.Column('shop_id', sa.String(length=50), nullable=True))

    # Migrate data back from stores to tenants
    op.execute("""
        UPDATE tenants
        SET shop_domain = stores.shop_domain,
            shop_id = stores.shop_id
        FROM stores
        WHERE stores.tenant_id = tenants.id
    """)

    # Note: Don't drop shop_domain and shop_id from stores table as they were part of the original schema
