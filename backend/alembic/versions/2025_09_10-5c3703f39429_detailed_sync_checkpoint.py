"""detailed sync checkpoint

Revision ID: 5c3703f39429
Revises: add_metafields_to_product_tables
Create Date: 2025-09-10 19:48:27.227526

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '5c3703f39429'
down_revision: Union[str, Sequence[str], None] = 'add_metafields_to_product_tables'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('sync_checkpoints', sa.Column('source_table_name', sa.String(length=100), nullable=True))
    op.add_column('sync_checkpoints', sa.Column('source_database_name', sa.String(length=100), nullable=True))
    op.add_column('sync_checkpoints', sa.Column('source_record_count_before', sa.Integer(), nullable=True))
    op.add_column('sync_checkpoints', sa.Column('source_record_count_after', sa.Integer(), nullable=True))
    op.add_column('sync_checkpoints', sa.Column('source_new_records_count', sa.Integer(), nullable=True))
    op.add_column('sync_checkpoints', sa.Column('source_updated_records_count', sa.Integer(), nullable=True))
    op.add_column('sync_checkpoints', sa.Column('source_deleted_records_count', sa.Integer(), nullable=True))
    op.add_column('sync_checkpoints', sa.Column('source_last_updated_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('sync_checkpoints', sa.Column('source_filter_criteria', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.add_column('sync_checkpoints', sa.Column('destination_table_name', sa.String(length=100), nullable=True))
    op.add_column('sync_checkpoints', sa.Column('destination_database_name', sa.String(length=100), nullable=True))
    op.add_column('sync_checkpoints', sa.Column('destination_record_count_before', sa.Integer(), nullable=True))
    op.add_column('sync_checkpoints', sa.Column('destination_record_count_after', sa.Integer(), nullable=True))
    op.add_column('sync_checkpoints', sa.Column('destination_inserted_count', sa.Integer(), nullable=True))
    op.add_column('sync_checkpoints', sa.Column('destination_updated_count', sa.Integer(), nullable=True))
    op.add_column('sync_checkpoints', sa.Column('destination_deleted_count', sa.Integer(), nullable=True))
    op.add_column('sync_checkpoints', sa.Column('destination_failed_count', sa.Integer(), nullable=True))
    op.add_column('sync_checkpoints', sa.Column('destination_last_updated_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('sync_checkpoints', sa.Column('sync_started_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('sync_checkpoints', sa.Column('sync_finished_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('sync_checkpoints', sa.Column('batch_count', sa.Integer(), nullable=True))
    op.add_column('sync_checkpoints', sa.Column('average_batch_size', sa.Float(), nullable=True))
    op.add_column('sync_checkpoints', sa.Column('max_batch_size', sa.Integer(), nullable=True))
    op.add_column('sync_checkpoints', sa.Column('min_batch_size', sa.Integer(), nullable=True))
    op.add_column('sync_checkpoints', sa.Column('total_batches_processed', sa.Integer(), nullable=True))
    op.add_column('sync_checkpoints', sa.Column('batches_with_errors', sa.Integer(), nullable=True))
    op.add_column('sync_checkpoints', sa.Column('retry_count', sa.Integer(), nullable=True))
    op.add_column('sync_checkpoints', sa.Column('max_retries', sa.Integer(), nullable=True))
    op.add_column('sync_checkpoints', sa.Column('records_per_second', sa.Float(), nullable=True))
    op.add_column('sync_checkpoints', sa.Column('average_batch_processing_time', sa.Float(), nullable=True))
    op.add_column('sync_checkpoints', sa.Column('max_batch_processing_time', sa.Float(), nullable=True))
    op.add_column('sync_checkpoints', sa.Column('min_batch_processing_time', sa.Float(), nullable=True))
    op.add_column('sync_checkpoints', sa.Column('memory_usage_peak', sa.Float(), nullable=True))
    op.add_column('sync_checkpoints', sa.Column('database_connection_count', sa.Integer(), nullable=True))
    op.add_column('sync_checkpoints', sa.Column('error_count_total', sa.Integer(), nullable=True))
    op.add_column('sync_checkpoints', sa.Column('error_count_by_type', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.add_column('sync_checkpoints', sa.Column('last_error_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('sync_checkpoints', sa.Column('consecutive_error_count', sa.Integer(), nullable=True))
    op.add_column('sync_checkpoints', sa.Column('error_rate_percentage', sa.Float(), nullable=True))
    op.add_column('sync_checkpoints', sa.Column('duplicate_records_found', sa.Integer(), nullable=True))
    op.add_column('sync_checkpoints', sa.Column('invalid_records_skipped', sa.Integer(), nullable=True))
    op.add_column('sync_checkpoints', sa.Column('null_values_count', sa.Integer(), nullable=True))
    op.add_column('sync_checkpoints', sa.Column('data_transformation_errors', sa.Integer(), nullable=True))
    op.add_column('sync_checkpoints', sa.Column('batch_statistics', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.add_column('sync_checkpoints', sa.Column('sync_version', sa.String(length=50), nullable=True))
    op.add_column('sync_checkpoints', sa.Column('platform_version', sa.String(length=50), nullable=True))
    op.add_column('sync_checkpoints', sa.Column('airbyte_version', sa.String(length=50), nullable=True))
    op.add_column('sync_checkpoints', sa.Column('configuration_hash', sa.String(length=255), nullable=True))
    op.add_column('sync_checkpoints', sa.Column('environment_info', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('sync_checkpoints', 'environment_info')
    op.drop_column('sync_checkpoints', 'configuration_hash')
    op.drop_column('sync_checkpoints', 'airbyte_version')
    op.drop_column('sync_checkpoints', 'platform_version')
    op.drop_column('sync_checkpoints', 'sync_version')
    op.drop_column('sync_checkpoints', 'batch_statistics')
    op.drop_column('sync_checkpoints', 'data_transformation_errors')
    op.drop_column('sync_checkpoints', 'null_values_count')
    op.drop_column('sync_checkpoints', 'invalid_records_skipped')
    op.drop_column('sync_checkpoints', 'duplicate_records_found')
    op.drop_column('sync_checkpoints', 'error_rate_percentage')
    op.drop_column('sync_checkpoints', 'consecutive_error_count')
    op.drop_column('sync_checkpoints', 'last_error_at')
    op.drop_column('sync_checkpoints', 'error_count_by_type')
    op.drop_column('sync_checkpoints', 'error_count_total')
    op.drop_column('sync_checkpoints', 'database_connection_count')
    op.drop_column('sync_checkpoints', 'memory_usage_peak')
    op.drop_column('sync_checkpoints', 'min_batch_processing_time')
    op.drop_column('sync_checkpoints', 'max_batch_processing_time')
    op.drop_column('sync_checkpoints', 'average_batch_processing_time')
    op.drop_column('sync_checkpoints', 'records_per_second')
    op.drop_column('sync_checkpoints', 'max_retries')
    op.drop_column('sync_checkpoints', 'retry_count')
    op.drop_column('sync_checkpoints', 'batches_with_errors')
    op.drop_column('sync_checkpoints', 'total_batches_processed')
    op.drop_column('sync_checkpoints', 'min_batch_size')
    op.drop_column('sync_checkpoints', 'max_batch_size')
    op.drop_column('sync_checkpoints', 'average_batch_size')
    op.drop_column('sync_checkpoints', 'batch_count')
    op.drop_column('sync_checkpoints', 'sync_finished_at')
    op.drop_column('sync_checkpoints', 'sync_started_at')
    op.drop_column('sync_checkpoints', 'destination_last_updated_at')
    op.drop_column('sync_checkpoints', 'destination_failed_count')
    op.drop_column('sync_checkpoints', 'destination_deleted_count')
    op.drop_column('sync_checkpoints', 'destination_updated_count')
    op.drop_column('sync_checkpoints', 'destination_inserted_count')
    op.drop_column('sync_checkpoints', 'destination_record_count_after')
    op.drop_column('sync_checkpoints', 'destination_record_count_before')
    op.drop_column('sync_checkpoints', 'destination_database_name')
    op.drop_column('sync_checkpoints', 'destination_table_name')
    op.drop_column('sync_checkpoints', 'source_filter_criteria')
    op.drop_column('sync_checkpoints', 'source_last_updated_at')
    op.drop_column('sync_checkpoints', 'source_deleted_records_count')
    op.drop_column('sync_checkpoints', 'source_updated_records_count')
    op.drop_column('sync_checkpoints', 'source_new_records_count')
    op.drop_column('sync_checkpoints', 'source_record_count_after')
    op.drop_column('sync_checkpoints', 'source_record_count_before')
    op.drop_column('sync_checkpoints', 'source_database_name')
    op.drop_column('sync_checkpoints', 'source_table_name')
    # ### end Alembic commands ###
