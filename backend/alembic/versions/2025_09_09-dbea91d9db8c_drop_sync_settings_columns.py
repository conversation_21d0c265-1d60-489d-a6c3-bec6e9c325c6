"""drop_sync_settings_columns

Revision ID: dbea91d9db8c
Revises: 40b349c6bef4
Create Date: 2025-09-09 18:55:45.767943

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'dbea91d9db8c'
down_revision: Union[str, Sequence[str], None] = '40b349c6bef4'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Drop sync settings columns from stores table
    op.drop_column('stores', 'product_sync_interval')
    op.drop_column('stores', 'inventory_sync_interval')
    op.drop_column('stores', 'customer_sync_interval')


def downgrade() -> None:
    """Downgrade schema."""
    # Add back sync settings columns to stores table
    op.add_column('stores', sa.Column('product_sync_interval', sa.String(), nullable=True))
    op.add_column('stores', sa.Column('inventory_sync_interval', sa.String(), nullable=True))
    op.add_column('stores', sa.Column('customer_sync_interval', sa.String(), nullable=True))
