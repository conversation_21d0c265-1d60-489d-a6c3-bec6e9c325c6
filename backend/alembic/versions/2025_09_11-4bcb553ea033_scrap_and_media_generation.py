"""scrap and media generation

Revision ID: 4bcb553ea033
Revises: f9479c571c7a
Create Date: 2025-09-11 16:08:35.792877

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '4bcb553ea033'
down_revision: Union[str, Sequence[str], None] = 'f9479c571c7a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('scraping_platforms',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('domains', sa.JSON(), nullable=False),
    sa.Column('features', sa.JSO<PERSON>(), nullable=False),
    sa.Column('limitations', sa.JSON(), nullable=True),
    sa.Column('is_active', sa.<PERSON>an(), nullable=True),
    sa.Column('config', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_index(op.f('ix_scraping_platforms_id'), 'scraping_platforms', ['id'], unique=False)
    op.create_table('scraped_documents',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('workspace_id', sa.Integer(), nullable=False),
    sa.Column('url', sa.String(), nullable=False),
    sa.Column('domain', sa.String(), nullable=False),
    sa.Column('title', sa.String(), nullable=True),
    sa.Column('status', sa.Enum('PENDING', 'SCRAPING', 'COMPLETED', 'FAILED', name='scrapingstatus'), nullable=False),
    sa.Column('progress', sa.Float(), nullable=True),
    sa.Column('product_count', sa.Integer(), nullable=True),
    sa.Column('collection_count', sa.Integer(), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('scraping_metadata', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['workspace_id'], ['tenants.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_scraped_documents_domain'), 'scraped_documents', ['domain'], unique=False)
    op.create_index(op.f('ix_scraped_documents_id'), 'scraped_documents', ['id'], unique=False)
    op.create_table('scraped_collections',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('document_id', sa.String(), nullable=False),
    sa.Column('slug', sa.String(), nullable=False),
    sa.Column('title', sa.String(), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('image_url', sa.String(), nullable=True),
    sa.Column('product_count', sa.Integer(), nullable=True),
    sa.Column('domain', sa.String(), nullable=False),
    sa.Column('scraped_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['document_id'], ['scraped_documents.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_scraped_collections_domain'), 'scraped_collections', ['domain'], unique=False)
    op.create_index(op.f('ix_scraped_collections_id'), 'scraped_collections', ['id'], unique=False)
    op.create_table('scraped_products',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('document_id', sa.String(), nullable=False),
    sa.Column('product_id', sa.String(), nullable=False),
    sa.Column('title', sa.String(), nullable=False),
    sa.Column('handle', sa.String(), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('vendor', sa.String(), nullable=True),
    sa.Column('product_type', sa.String(), nullable=True),
    sa.Column('tags', sa.JSON(), nullable=True),
    sa.Column('images', sa.JSON(), nullable=True),
    sa.Column('variants', sa.JSON(), nullable=True),
    sa.Column('collections', sa.JSON(), nullable=True),
    sa.Column('url', sa.String(), nullable=False),
    sa.Column('domain', sa.String(), nullable=False),
    sa.Column('scraped_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['document_id'], ['scraped_documents.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_scraped_products_domain'), 'scraped_products', ['domain'], unique=False)
    op.create_index(op.f('ix_scraped_products_id'), 'scraped_products', ['id'], unique=False)
    op.create_table('scraping_jobs',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('document_id', sa.String(), nullable=False),
    sa.Column('url', sa.String(), nullable=False),
    sa.Column('status', sa.Enum('QUEUED', 'RUNNING', 'COMPLETED', 'FAILED', 'CANCELLED', name='jobstatus'), nullable=False),
    sa.Column('progress', sa.Float(), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('job_metadata', sa.JSON(), nullable=True),
    sa.Column('started_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['document_id'], ['scraped_documents.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_scraping_jobs_id'), 'scraping_jobs', ['id'], unique=False)
    op.drop_index(op.f('idx_dlq_created_at'), table_name='dead_letter_queue')
    op.drop_index(op.f('idx_dlq_store_id'), table_name='dead_letter_queue')
    op.drop_index(op.f('ix_dead_letter_queue_id'), table_name='dead_letter_queue')
    op.drop_index(op.f('ix_dead_letter_queue_resolved'), table_name='dead_letter_queue')
    op.drop_index(op.f('ix_dead_letter_queue_source_type'), table_name='dead_letter_queue')
    op.drop_table('dead_letter_queue')
    op.drop_index(op.f('idx_sync_jobs_created_at'), table_name='sync_jobs')
    op.drop_index(op.f('idx_sync_jobs_store_entity'), table_name='sync_jobs')
    op.drop_index(op.f('ix_sync_jobs_airbyte_job_id'), table_name='sync_jobs')
    op.drop_index(op.f('ix_sync_jobs_celery_task_id'), table_name='sync_jobs')
    op.drop_index(op.f('ix_sync_jobs_id'), table_name='sync_jobs')
    op.drop_index(op.f('ix_sync_jobs_status'), table_name='sync_jobs')
    op.drop_table('sync_jobs')
    op.drop_index(op.f('idx_sync_checkpoints_store_entity'), table_name='sync_checkpoints')
    op.drop_index(op.f('idx_sync_checkpoints_updated_at'), table_name='sync_checkpoints')
    op.drop_index(op.f('ix_sync_checkpoints_id'), table_name='sync_checkpoints')
    op.drop_table('sync_checkpoints')
    op.drop_index(op.f('idx_webhook_events_created_at'), table_name='webhook_events')
    op.drop_index(op.f('idx_webhook_events_shop_topic'), table_name='webhook_events')
    op.drop_index(op.f('ix_webhook_events_event_id'), table_name='webhook_events')
    op.drop_index(op.f('ix_webhook_events_id'), table_name='webhook_events')
    op.drop_index(op.f('ix_webhook_events_shop_domain'), table_name='webhook_events')
    op.drop_index(op.f('ix_webhook_events_status'), table_name='webhook_events')
    op.drop_index(op.f('ix_webhook_events_topic'), table_name='webhook_events')
    op.drop_table('webhook_events')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('webhook_events',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('event_id', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('topic', sa.VARCHAR(length=100), autoincrement=False, nullable=False),
    sa.Column('shop_domain', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('store_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('tenant_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('event_type', sa.VARCHAR(length=50), autoincrement=False, nullable=True),
    sa.Column('payload', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=False),
    sa.Column('headers', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('hmac_verified', sa.BOOLEAN(), autoincrement=False, nullable=True),
    sa.Column('status', sa.VARCHAR(length=20), autoincrement=False, nullable=True),
    sa.Column('retry_count', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('max_retries', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('last_error', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('processing_started_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('completed_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['store_id'], ['stores.id'], name=op.f('webhook_events_store_id_fkey'), ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], name=op.f('webhook_events_tenant_id_fkey'), ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id', name=op.f('webhook_events_pkey'))
    )
    op.create_index(op.f('ix_webhook_events_topic'), 'webhook_events', ['topic'], unique=False)
    op.create_index(op.f('ix_webhook_events_status'), 'webhook_events', ['status'], unique=False)
    op.create_index(op.f('ix_webhook_events_shop_domain'), 'webhook_events', ['shop_domain'], unique=False)
    op.create_index(op.f('ix_webhook_events_id'), 'webhook_events', ['id'], unique=False)
    op.create_index(op.f('ix_webhook_events_event_id'), 'webhook_events', ['event_id'], unique=True)
    op.create_index(op.f('idx_webhook_events_shop_topic'), 'webhook_events', ['shop_domain', 'topic'], unique=False)
    op.create_index(op.f('idx_webhook_events_created_at'), 'webhook_events', ['created_at'], unique=False)
    op.create_table('sync_checkpoints',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('store_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('entity_type', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('last_updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('last_id', sa.VARCHAR(length=255), autoincrement=False, nullable=True),
    sa.Column('airbyte_state', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('total_records', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('last_sync_status', sa.VARCHAR(length=20), autoincrement=False, nullable=True),
    sa.Column('last_error_message', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('last_successful_sync_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('airbyte_last_sync_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('airbyte_sync_started_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('airbyte_sync_finished_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('local_sync_started_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('local_sync_finished_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('sync_duration_seconds', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('airbyte_job_id', sa.VARCHAR(length=255), autoincrement=False, nullable=True),
    sa.Column('sync_trigger_type', sa.VARCHAR(length=20), autoincrement=False, nullable=True),
    sa.Column('current_sync_stage', sa.VARCHAR(length=30), autoincrement=False, nullable=True),
    sa.Column('records_processed_in_sync', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('source_table_name', sa.VARCHAR(length=100), autoincrement=False, nullable=True),
    sa.Column('source_database_name', sa.VARCHAR(length=100), autoincrement=False, nullable=True),
    sa.Column('source_record_count_before', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('source_record_count_after', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('source_new_records_count', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('source_updated_records_count', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('source_deleted_records_count', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('source_last_updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('source_filter_criteria', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('destination_table_name', sa.VARCHAR(length=100), autoincrement=False, nullable=True),
    sa.Column('destination_database_name', sa.VARCHAR(length=100), autoincrement=False, nullable=True),
    sa.Column('destination_record_count_before', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('destination_record_count_after', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('destination_inserted_count', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('destination_updated_count', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('destination_deleted_count', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('destination_failed_count', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('destination_last_updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('sync_started_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('sync_finished_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('batch_count', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('average_batch_size', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('max_batch_size', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('min_batch_size', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('total_batches_processed', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('batches_with_errors', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('retry_count', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('max_retries', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('records_per_second', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('average_batch_processing_time', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('max_batch_processing_time', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('min_batch_processing_time', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('memory_usage_peak', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('database_connection_count', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('error_count_total', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('error_count_by_type', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('last_error_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('consecutive_error_count', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('error_rate_percentage', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('duplicate_records_found', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('invalid_records_skipped', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('null_values_count', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('data_transformation_errors', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('batch_statistics', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('sync_version', sa.VARCHAR(length=50), autoincrement=False, nullable=True),
    sa.Column('platform_version', sa.VARCHAR(length=50), autoincrement=False, nullable=True),
    sa.Column('airbyte_version', sa.VARCHAR(length=50), autoincrement=False, nullable=True),
    sa.Column('configuration_hash', sa.VARCHAR(length=255), autoincrement=False, nullable=True),
    sa.Column('environment_info', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['store_id'], ['stores.id'], name=op.f('sync_checkpoints_store_id_fkey'), ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name=op.f('sync_checkpoints_pkey')),
    sa.UniqueConstraint('store_id', 'entity_type', name=op.f('uq_sync_checkpoints_store_entity'), postgresql_include=[], postgresql_nulls_not_distinct=False)
    )
    op.create_index(op.f('ix_sync_checkpoints_id'), 'sync_checkpoints', ['id'], unique=False)
    op.create_index(op.f('idx_sync_checkpoints_updated_at'), 'sync_checkpoints', ['last_updated_at'], unique=False)
    op.create_index(op.f('idx_sync_checkpoints_store_entity'), 'sync_checkpoints', ['store_id', 'entity_type'], unique=False)
    op.create_table('sync_jobs',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('store_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('entity_type', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('job_type', sa.VARCHAR(length=20), autoincrement=False, nullable=False),
    sa.Column('status', sa.VARCHAR(length=20), autoincrement=False, nullable=False),
    sa.Column('triggered_by', sa.VARCHAR(length=50), autoincrement=False, nullable=True),
    sa.Column('airbyte_job_id', sa.BIGINT(), autoincrement=False, nullable=True),
    sa.Column('airbyte_connection_id', sa.VARCHAR(length=255), autoincrement=False, nullable=True),
    sa.Column('started_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('finished_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('duration_seconds', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('records_processed', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('records_failed', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('retry_count', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('max_retries', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('error_message', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('error_details', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('celery_task_id', sa.VARCHAR(length=255), autoincrement=False, nullable=True),
    sa.Column('job_metadata', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['store_id'], ['stores.id'], name=op.f('sync_jobs_store_id_fkey'), ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name=op.f('sync_jobs_pkey'))
    )
    op.create_index(op.f('ix_sync_jobs_status'), 'sync_jobs', ['status'], unique=False)
    op.create_index(op.f('ix_sync_jobs_id'), 'sync_jobs', ['id'], unique=False)
    op.create_index(op.f('ix_sync_jobs_celery_task_id'), 'sync_jobs', ['celery_task_id'], unique=False)
    op.create_index(op.f('ix_sync_jobs_airbyte_job_id'), 'sync_jobs', ['airbyte_job_id'], unique=False)
    op.create_index(op.f('idx_sync_jobs_store_entity'), 'sync_jobs', ['store_id', 'entity_type'], unique=False)
    op.create_index(op.f('idx_sync_jobs_created_at'), 'sync_jobs', ['created_at'], unique=False)
    op.create_table('dead_letter_queue',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('source_type', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('source_id', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('entity_type', sa.VARCHAR(length=50), autoincrement=False, nullable=True),
    sa.Column('store_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('original_payload', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=False),
    sa.Column('failure_reason', sa.VARCHAR(length=100), autoincrement=False, nullable=False),
    sa.Column('error_message', sa.TEXT(), autoincrement=False, nullable=False),
    sa.Column('error_details', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('retry_count', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('last_retry_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('resolved', sa.BOOLEAN(), autoincrement=False, nullable=True),
    sa.Column('resolved_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['store_id'], ['stores.id'], name=op.f('dead_letter_queue_store_id_fkey'), ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id', name=op.f('dead_letter_queue_pkey'))
    )
    op.create_index(op.f('ix_dead_letter_queue_source_type'), 'dead_letter_queue', ['source_type'], unique=False)
    op.create_index(op.f('ix_dead_letter_queue_resolved'), 'dead_letter_queue', ['resolved'], unique=False)
    op.create_index(op.f('ix_dead_letter_queue_id'), 'dead_letter_queue', ['id'], unique=False)
    op.create_index(op.f('idx_dlq_store_id'), 'dead_letter_queue', ['store_id'], unique=False)
    op.create_index(op.f('idx_dlq_created_at'), 'dead_letter_queue', ['created_at'], unique=False)
    op.drop_index(op.f('ix_scraping_jobs_id'), table_name='scraping_jobs')
    op.drop_table('scraping_jobs')
    op.drop_index(op.f('ix_scraped_products_id'), table_name='scraped_products')
    op.drop_index(op.f('ix_scraped_products_domain'), table_name='scraped_products')
    op.drop_table('scraped_products')
    op.drop_index(op.f('ix_scraped_collections_id'), table_name='scraped_collections')
    op.drop_index(op.f('ix_scraped_collections_domain'), table_name='scraped_collections')
    op.drop_table('scraped_collections')
    op.drop_index(op.f('ix_scraped_documents_id'), table_name='scraped_documents')
    op.drop_index(op.f('ix_scraped_documents_domain'), table_name='scraped_documents')
    op.drop_table('scraped_documents')
    op.drop_index(op.f('ix_scraping_platforms_id'), table_name='scraping_platforms')
    op.drop_table('scraping_platforms')
    # ### end Alembic commands ###
