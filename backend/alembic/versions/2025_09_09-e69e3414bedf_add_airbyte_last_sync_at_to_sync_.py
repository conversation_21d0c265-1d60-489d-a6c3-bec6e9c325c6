"""add_airbyte_last_sync_at_to_sync_checkpoints

Revision ID: e69e3414bedf
Revises: dbea91d9db8c
Create Date: 2025-09-09 19:01:42.345464

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'e69e3414bedf'
down_revision: Union[str, Sequence[str], None] = 'dbea91d9db8c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Add airbyte_last_sync_at column to sync_checkpoints table
    op.add_column('sync_checkpoints', sa.Column('airbyte_last_sync_at', sa.DateTime(timezone=True), nullable=True))


def downgrade() -> None:
    """Downgrade schema."""
    # Remove airbyte_last_sync_at column from sync_checkpoints table
    op.drop_column('sync_checkpoints', 'airbyte_last_sync_at')
