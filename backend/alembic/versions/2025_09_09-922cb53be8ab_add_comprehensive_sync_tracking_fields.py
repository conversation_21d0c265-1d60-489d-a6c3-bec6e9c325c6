"""add_comprehensive_sync_tracking_fields

Revision ID: 922cb53be8ab
Revises: e69e3414bedf
Create Date: 2025-09-09 19:09:23.544410

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '922cb53be8ab'
down_revision: Union[str, Sequence[str], None] = 'e69e3414bedf'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Add comprehensive sync tracking fields to sync_checkpoints table
    op.add_column('sync_checkpoints', sa.Column('airbyte_sync_started_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('sync_checkpoints', sa.Column('airbyte_sync_finished_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('sync_checkpoints', sa.Column('local_sync_started_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('sync_checkpoints', sa.Column('local_sync_finished_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('sync_checkpoints', sa.Column('sync_duration_seconds', sa.Float(), nullable=True))
    op.add_column('sync_checkpoints', sa.Column('airbyte_job_id', sa.String(length=255), nullable=True))
    op.add_column('sync_checkpoints', sa.Column('sync_trigger_type', sa.String(length=20), nullable=True))
    op.add_column('sync_checkpoints', sa.Column('current_sync_stage', sa.String(length=30), nullable=True))
    op.add_column('sync_checkpoints', sa.Column('records_processed_in_sync', sa.Integer(), nullable=True))


def downgrade() -> None:
    """Downgrade schema."""
    # Remove comprehensive sync tracking fields from sync_checkpoints table
    op.drop_column('sync_checkpoints', 'records_processed_in_sync')
    op.drop_column('sync_checkpoints', 'current_sync_stage')
    op.drop_column('sync_checkpoints', 'sync_trigger_type')
    op.drop_column('sync_checkpoints', 'airbyte_job_id')
    op.drop_column('sync_checkpoints', 'sync_duration_seconds')
    op.drop_column('sync_checkpoints', 'local_sync_finished_at')
    op.drop_column('sync_checkpoints', 'local_sync_started_at')
    op.drop_column('sync_checkpoints', 'airbyte_sync_finished_at')
    op.drop_column('sync_checkpoints', 'airbyte_sync_started_at')
