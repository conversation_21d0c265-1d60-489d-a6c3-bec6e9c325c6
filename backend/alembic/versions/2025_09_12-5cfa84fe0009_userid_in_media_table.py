"""userid in media table

Revision ID: 5cfa84fe0009
Revises: ebc4eca2c65b
Create Date: 2025-09-12 14:52:11.444193

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '5cfa84fe0009'
down_revision: Union[str, Sequence[str], None] = 'ebc4eca2c65b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('media_jobs', sa.Column('job_id', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('media_jobs', 'job_id')
    # ### end Alembic commands ###
