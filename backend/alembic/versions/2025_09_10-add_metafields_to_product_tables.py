"""add_metafields_to_product_tables

Revision ID: add_metafields_to_product_tables
Revises: 8f61d8a089e2
Create Date: 2025-09-10 10:54:30.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'add_metafields_to_product_tables'
down_revision: Union[str, None] = '8f61d8a089e2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Add metafields columns to product tables."""

    # Check existing columns
    conn = op.get_bind()
    inspector = sa.inspect(conn)

    # Check product_variants columns
    variant_columns = [col['name'] for col in inspector.get_columns('product_variants')]
    if 'metafields' not in variant_columns:
        op.add_column('product_variants', sa.Column('metafields', sa.Text(), nullable=True))

    # Check product_images columns
    image_columns = [col['name'] for col in inspector.get_columns('product_images')]
    if 'metafields' not in image_columns:
        op.add_column('product_images', sa.Column('metafields', sa.Text(), nullable=True))

    # Note: products table already has metafields column from previous migration


def downgrade() -> None:
    """Remove metafields columns from product tables."""

    # Remove metafields column from product_images table
    op.drop_column('product_images', 'metafields')

    # Remove metafields column from product_variants table
    op.drop_column('product_variants', 'metafields')

    # Note: products.metafields column is kept as it was added in a previous migration