"""add_full_payload_to_media_jobs

Revision ID: 5c006f849ca3
Revises: f9c30b4ba20b
Create Date: 2025-09-12 13:37:24.198480

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '5c006f849ca3'
down_revision: Union[str, Sequence[str], None] = 'f9c30b4ba20b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.add_column('media_jobs', sa.Column('full_payload', sa.JSON(), nullable=True))


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_column('media_jobs', 'full_payload')
