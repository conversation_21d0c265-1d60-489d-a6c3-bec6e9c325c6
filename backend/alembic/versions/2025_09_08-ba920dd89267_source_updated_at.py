"""source_updated_at

Revision ID: ba920dd89267
Revises: f6be7177fab3
Create Date: 2025-09-08 23:14:30.668246

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'ba920dd89267'
down_revision: Union[str, Sequence[str], None] = 'f6be7177fab3'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('inventory_levels', sa.Column('source_updated_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('product_images', sa.Column('source_updated_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('product_variants', sa.Column('source_updated_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('products', sa.Column('source_updated_at', sa.DateTime(timezone=True), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('products', 'source_updated_at')
    op.drop_column('product_variants', 'source_updated_at')
    op.drop_column('product_images', 'source_updated_at')
    op.drop_column('inventory_levels', 'source_updated_at')
    # ### end Alembic commands ###
