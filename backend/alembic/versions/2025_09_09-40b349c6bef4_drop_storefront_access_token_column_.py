"""drop storefront_access_token column from stores table

Revision ID: 40b349c6bef4
Revises: a522c65e5832
Create Date: 2025-09-09 18:24:35.326670

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '40b349c6bef4'
down_revision: Union[str, Sequence[str], None] = 'a522c65e5832'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Drop the storefront_access_token column from stores table
    op.drop_column('stores', 'storefront_access_token')


def downgrade() -> None:
    """Downgrade schema."""
    # Add back the storefront_access_token column to stores table
    op.add_column('stores', sa.Column('storefront_access_token', sa.String(), nullable=True))
