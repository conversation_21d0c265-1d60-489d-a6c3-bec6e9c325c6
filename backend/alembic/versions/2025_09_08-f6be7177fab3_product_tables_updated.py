"""product tables updated

Revision ID: f6be7177fab3
Revises: 41cae0401133
Create Date: 2025-09-08 22:38:55.428021

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f6be7177fab3'
down_revision: Union[str, Sequence[str], None] = '41cae0401133'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('inventory_levels',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('external_id', sa.String(), nullable=False),
    sa.Column('inventory_item_id', sa.String(), nullable=False),
    sa.Column('location_id', sa.String(), nullable=False),
    sa.Column('available', sa.Integer(), nullable=True),
    sa.Column('updated_at_location', sa.DateTime(timezone=True), nullable=True),
    sa.Column('full_json', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('external_id')
    )
    op.create_index(op.f('ix_inventory_levels_id'), 'inventory_levels', ['id'], unique=False)
    op.create_table('product_images',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('external_id', sa.String(), nullable=False),
    sa.Column('product_id', sa.Integer(), nullable=False),
    sa.Column('variant_id', sa.Integer(), nullable=True),
    sa.Column('src', sa.String(), nullable=False),
    sa.Column('alt', sa.String(), nullable=True),
    sa.Column('width', sa.Integer(), nullable=True),
    sa.Column('height', sa.Integer(), nullable=True),
    sa.Column('position', sa.Integer(), nullable=True),
    sa.Column('full_json', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['product_id'], ['products.id'], ),
    sa.ForeignKeyConstraint(['variant_id'], ['product_variants.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('external_id')
    )
    op.create_index(op.f('ix_product_images_id'), 'product_images', ['id'], unique=False)
    op.add_column('product_variants', sa.Column('inventory_item_id', sa.String(), nullable=True))
    op.add_column('product_variants', sa.Column('available_for_sale', sa.Boolean(), nullable=True))
    op.add_column('product_variants', sa.Column('full_json', sa.Text(), nullable=True))
    op.alter_column('product_variants', 'product_id',
               existing_type=sa.INTEGER(),
               nullable=False)
    op.alter_column('product_variants', 'price',
               existing_type=sa.DOUBLE_PRECISION(precision=53),
               nullable=True)
    op.alter_column('product_variants', 'fulfillment_service',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=True)
    op.drop_column('product_variants', 'inventory_item')
    op.alter_column('products', 'tags',
               existing_type=sa.VARCHAR(),
               type_=sa.Text(),
               existing_nullable=True)
    op.drop_column('products', 'cost')
    op.drop_column('products', 'quantity')
    op.drop_column('products', 'barcode')
    op.drop_column('products', 'weight')
    op.drop_column('products', 'images')
    op.drop_column('products', 'compare_at_price')
    op.drop_column('products', 'featured_image')
    op.drop_column('products', 'weight_unit')
    op.drop_column('products', 'platform_data')
    op.drop_column('products', 'sku')
    op.drop_column('products', 'price')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('products', sa.Column('price', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=False))
    op.add_column('products', sa.Column('sku', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.add_column('products', sa.Column('platform_data', sa.TEXT(), autoincrement=False, nullable=True))
    op.add_column('products', sa.Column('weight_unit', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.add_column('products', sa.Column('featured_image', sa.TEXT(), autoincrement=False, nullable=True))
    op.add_column('products', sa.Column('compare_at_price', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True))
    op.add_column('products', sa.Column('images', sa.TEXT(), autoincrement=False, nullable=True))
    op.add_column('products', sa.Column('weight', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True))
    op.add_column('products', sa.Column('barcode', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.add_column('products', sa.Column('quantity', sa.INTEGER(), autoincrement=False, nullable=True))
    op.add_column('products', sa.Column('cost', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True))
    op.alter_column('products', 'tags',
               existing_type=sa.Text(),
               type_=sa.VARCHAR(),
               existing_nullable=True)
    op.add_column('product_variants', sa.Column('inventory_item', sa.TEXT(), autoincrement=False, nullable=True))
    op.alter_column('product_variants', 'fulfillment_service',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=True)
    op.alter_column('product_variants', 'price',
               existing_type=sa.DOUBLE_PRECISION(precision=53),
               nullable=False)
    op.alter_column('product_variants', 'product_id',
               existing_type=sa.INTEGER(),
               nullable=True)
    op.drop_column('product_variants', 'full_json')
    op.drop_column('product_variants', 'available_for_sale')
    op.drop_column('product_variants', 'inventory_item_id')
    op.drop_index(op.f('ix_product_images_id'), table_name='product_images')
    op.drop_table('product_images')
    op.drop_index(op.f('ix_inventory_levels_id'), table_name='inventory_levels')
    op.drop_table('inventory_levels')
    # ### end Alembic commands ###
