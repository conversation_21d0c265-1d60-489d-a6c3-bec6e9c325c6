"""drop name column from stores table

Revision ID: a522c65e5832
Revises: ba920dd89267
Create Date: 2025-09-09 12:49:23.198309

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'a522c65e5832'
down_revision: Union[str, Sequence[str], None] = 'ba920dd89267'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Drop the name column from stores table
    op.drop_column('stores', 'name')


def downgrade() -> None:
    """Downgrade schema."""
    # Add back the name column
    op.add_column('stores', sa.Column('name', sa.String(), nullable=False))
