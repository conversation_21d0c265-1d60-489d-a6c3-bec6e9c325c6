#!/usr/bin/env python3
"""
Test script to validate the updated MediaGenerateRequest schema.
Standalone version that doesn't require backend dependencies.
"""

import json
from pydantic import BaseModel, Field, validator
from typing import List, Optional, Dict, Any, Union

# Copy the schema definitions here for standalone testing
class GenerationSettings(BaseModel):
    """Detailed generation settings for media creation."""

    # Image/Video settings
    size: Optional[str] = None  # e.g., "1024x1024"
    guidance: Optional[float] = None  # e.g., 7.5
    steps: Optional[int] = None  # e.g., 25
    strength: Optional[float] = None  # e.g., 0.8
    seed: Optional[int] = None  # e.g., 91678

    # Quality and processing
    upscale: Optional[bool] = None  # e.g., True
    safety: Optional[bool] = None  # e.g., True
    quality: Optional[str] = None  # e.g., "Standard"

    # Layout and format
    aspect_ratio: Optional[str] = Field(default=None, alias='aspectRatio')  # e.g., "1:1", "16:9"

    model_config = {
        "populate_by_name": True
    }


class ProductItem(BaseModel):
    """Individual product item for media generation."""

    product_id: Union[str, int] = Field(alias='productId')  # Product identifier
    prompt: str  # Generation prompt for this product
    reference_image_urls: Optional[List[str]] = Field(default=None, alias='referenceImageUrls')  # Reference images
    store_id: Optional[int] = Field(default=None, alias='storeId')  # Store identifier (avoid duplication with shop_id)

    model_config = {
        "populate_by_name": True
    }

    @validator('product_id', pre=True)
    def convert_product_id_to_string(cls, v):
        """Convert product ID to string for consistency."""
        return str(v)


class MediaGenerateRequest(BaseModel):
    """Request to generate media for products - matches user payload structure exactly."""

    # Core generation parameters (matches user payload order)
    mode: str  # 'image', 'video', 'voice'
    model: Optional[str] = None  # 'banana', 'veo3', etc.

    # Detailed generation settings
    settings: Optional[GenerationSettings] = None

    # Product items with individual prompts and references
    items: Optional[List[ProductItem]] = None

    # Basic fields (for backward compatibility and API consistency)
    media_type: str  # 'video', 'image', 'voice' (derived from mode if not provided)
    shop_id: int
    product_ids: List[Union[str, int]]  # List of product IDs to process

    # Optional enhancement fields
    template_id: Optional[str] = None
    voice_id: Optional[str] = None
    aspect_ratio: Optional[str] = None  # Can be derived from settings.aspect_ratio
    locale: Optional[str] = "en"
    text_input: Optional[str] = None  # For voice generation

    @validator('product_ids', pre=True, each_item=True)
    def convert_product_ids_to_strings(cls, v):
        """Convert product IDs to strings to ensure consistency."""
        return str(v)

    @validator('media_type', pre=True, always=True)
    def derive_media_type_from_mode(cls, v, values):
        """Derive media_type from mode if not explicitly provided."""
        if v is None and 'mode' in values:
            mode = values.get('mode')
            if mode == 'image':
                return 'image'
            elif mode in ['video', 'voice']:
                return mode
        return v or 'image'

# User's test data (same as in test_media_generate.py)
USER_PAYLOAD = {
    "mode": "image",
    "model": "banana",
    "settings": {
        "size": "1024x1024",
        "guidance": 7.5,
        "steps": 25,
        "strength": 0.8,
        "seed": 91678,
        "upscale": True,
        "safety": True,
        "aspectRatio": "1:1",
        "quality": "Standard"
    },
    "items": [
        {
            "productId": 69283,
            "prompt": "A professional product shot of a smove dance sneaker in weiss, high-resolution, on a clean white background.",
            "referenceImageUrls": [
                "https://cdn.shopify.com/s/files/1/0935/6414/1914/files/Smove_Sneaker_white_1_1.jpg?v=1739555921",
                "https://cdn.shopify.com/s/files/1/0935/6414/1914/files/Smove_Sneaker_white_2_1.jpg?v=1739555921",
                "https://cdn.shopify.com/s/files/1/0935/6414/1914/files/Smove_Sneaker_white_4_1.jpg?v=1739555921",
                "https://cdn.shopify.com/s/files/1/0935/6414/1914/files/Smove_Sneaker_white_3_1.jpg?v=1739555921",
                "https://cdn.shopify.com/s/files/1/0935/6414/1914/files/4_c0ed0d8b-12de-4496-97c0-3f14942665a1.png?v=1739555921",
                "https://cdn.shopify.com/s/files/1/0935/6414/1914/files/Smove_Sneaker_white_1_1.jpg?v=1739555921",
                "https://cdn.shopify.com/s/files/1/0935/6414/1914/files/Smove_Sneaker_white_2_1.jpg?v=1739555921",
                "https://cdn.shopify.com/s/files/1/0935/6414/1914/files/Smove_Sneaker_white_4_1.jpg?v=1739555921",
                "https://cdn.shopify.com/s/files/1/0935/6414/1914/files/Smove_Sneaker_white_3_1.jpg?v=1739555921",
                "https://cdn.shopify.com/s/files/1/0935/6414/1914/files/4_c0ed0d8b-12de-4496-97c0-3f14942665a1.png?v=1739555921"
            ],
            "storeId": 2
        }
    ],
    "media_type": "image",
    "shop_id": 2,
    "product_ids": [69283]
}

def test_schema_validation():
    """Test that the updated schema accepts the user payload."""
    print("🧪 Testing MediaGenerateRequest Schema Validation")
    print("=" * 50)

    try:
        # Try to create MediaGenerateRequest from user payload
        request = MediaGenerateRequest(**USER_PAYLOAD)

        print("✅ SUCCESS: Schema validation passed!")
        print("\n📋 Validated Request Details:")
        print(f"   - Mode: {request.mode}")
        print(f"   - Model: {request.model}")
        print(f"   - Media Type: {request.media_type}")
        print(f"   - Shop ID: {request.shop_id}")
        print(f"   - Product IDs: {request.product_ids}")

        if request.settings:
            print("\n📋 Settings:")
            print(f"   - Size: {request.settings.size}")
            print(f"   - Guidance: {request.settings.guidance}")
            print(f"   - Steps: {request.settings.steps}")
            print(f"   - Quality: {request.settings.quality}")
            print(f"   - Aspect Ratio: {request.settings.aspect_ratio}")

        if request.items:
            print("\n📋 Items:")
            for i, item in enumerate(request.items):
                print(f"   - Item {i+1}: Product {item.product_id}")
                print(f"     Prompt: {item.prompt[:50]}...")
                print(f"     Reference Images: {len(item.reference_image_urls or [])}")

        print("\n🎉 Schema validation successful! The user payload is fully compatible.")

        return True

    except Exception as e:
        print(f"❌ Schema validation failed: {e}")
        print("\n💡 This indicates the schema doesn't match the user payload structure.")
        return False

def test_individual_models():
    """Test individual model components."""
    print("\n🧪 Testing Individual Model Components")
    print("=" * 40)

    # Test GenerationSettings
    settings_data = USER_PAYLOAD["settings"]
    try:
        settings = GenerationSettings(**settings_data)
        print("✅ GenerationSettings validation: PASSED")
    except Exception as e:
        print(f"❌ GenerationSettings validation: FAILED - {e}")

    # Test ProductItem
    item_data = USER_PAYLOAD["items"][0]
    try:
        item = ProductItem(**item_data)
        print("✅ ProductItem validation: PASSED")
    except Exception as e:
        print(f"❌ ProductItem validation: FAILED - {e}")

def main():
    """Main test function."""
    print("🚀 Schema Validation Test Suite")
    print("=" * 50)

    # Test individual models first
    test_individual_models()

    # Test complete schema
    print("\n" + "=" * 50)
    success = test_schema_validation()

    print("\n" + "=" * 50)
    if success:
        print("🎯 RESULT: Schema update successful!")
        print("   ✅ User payload structure is fully supported")
        print("   ✅ No more generic Dict[str, Any] types")
        print("   ✅ Proper type validation and documentation")
        print("   ✅ Duplicates removed, cleaner structure")
    else:
        print("❌ RESULT: Schema update needs more work")

if __name__ == "__main__":
    main()