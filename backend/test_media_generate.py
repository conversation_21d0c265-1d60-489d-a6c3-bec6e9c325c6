#!/usr/bin/env python3
"""
Test script for the /api/media/generate endpoint.

This script tests the media generation API with the provided data format.
It transforms the user's JSON payload into the expected MediaGenerateRequest format.
"""

import json
import requests
import sys
from typing import Dict, Any

# Configuration
API_BASE_URL = "http://localhost:8123/api"
AUTH_TOKEN = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************.C4Jd6zgxrdXRe0w9kpYpbU-yzxmndIE9mZI4gbNFAuk"

# User's test data
USER_PAYLOAD = {
    "mode": "image",
    "model": "banana",
    "settings": {
        "size": "1024x1024",
        "guidance": 7.5,
        "steps": 25,
        "strength": 0.8,
        "seed": 91678,
        "upscale": True,
        "safety": True,
        "aspectRatio": "1:1",
        "quality": "Standard"
    },
    "items": [
        {
            "productId": 69283,
            "prompt": "A professional product shot of a smove dance sneaker in weiss, high-resolution, on a clean white background.",
            "referenceImageUrls": [
                "https://cdn.shopify.com/s/files/1/0935/6414/1914/files/Smove_Sneaker_white_1_1.jpg?v=1739555921",
                "https://cdn.shopify.com/s/files/1/0935/6414/1914/files/Smove_Sneaker_white_2_1.jpg?v=1739555921",
                "https://cdn.shopify.com/s/files/1/0935/6414/1914/files/Smove_Sneaker_white_4_1.jpg?v=1739555921",
                "https://cdn.shopify.com/s/files/1/0935/6414/1914/files/Smove_Sneaker_white_3_1.jpg?v=1739555921",
                "https://cdn.shopify.com/s/files/1/0935/6414/1914/files/4_c0ed0d8b-12de-4496-97c0-3f14942665a1.png?v=1739555921",
                "https://cdn.shopify.com/s/files/1/0935/6414/1914/files/Smove_Sneaker_white_1_1.jpg?v=1739555921",
                "https://cdn.shopify.com/s/files/1/0935/6414/1914/files/Smove_Sneaker_white_2_1.jpg?v=1739555921",
                "https://cdn.shopify.com/s/files/1/0935/6414/1914/files/Smove_Sneaker_white_4_1.jpg?v=1739555921",
                "https://cdn.shopify.com/s/files/1/0935/6414/1914/files/Smove_Sneaker_white_3_1.jpg?v=1739555921",
                "https://cdn.shopify.com/s/files/1/0935/6414/1914/files/4_c0ed0d8b-12de-4496-97c0-3f14942665a1.png?v=1739555921"
            ],
            "storeId": 2
        }
    ],
    "media_type": "image",
    "shop_id": 2,
    "product_ids": [69283]
}

def transform_payload_to_api_format(user_payload: Dict[str, Any]) -> Dict[str, Any]:
    """
    Transform the user's payload into the expected MediaGenerateRequest format.

    The API expects:
    {
        "media_type": str,  # 'video', 'image', 'voice'
        "shop_id": int,
        "product_ids": List[Union[str, int]],
        "template_id": Optional[str] = None,
        "voice_id": Optional[str] = None,
        "aspect_ratio": Optional[str] = "16:9",
        "locale": Optional[str] = "en",
        "text_input": Optional[str] = None
    }
    """
    print("🔄 TRANSFORMATION: Starting payload transformation...")
    print(f"📥 Original payload keys: {list(user_payload.keys())}")

    # Extract values from user's payload
    media_type = user_payload.get("media_type", "image")
    shop_id = user_payload.get("shop_id", 2)
    product_ids = user_payload.get("product_ids", [])

    # Extract additional settings
    settings = user_payload.get("settings", {})
    aspect_ratio = settings.get("aspectRatio", "1:1")

    # Extract from items if product_ids is empty
    if not product_ids and "items" in user_payload:
        items = user_payload["items"]
        if items:
            product_ids = [item.get("productId") for item in items if item.get("productId")]
            print(f"📋 Extracted product_ids from items: {product_ids}")

    # Create the API-compliant payload
    api_payload = {
        "media_type": media_type,
        "shop_id": shop_id,
        "product_ids": product_ids,
        "aspect_ratio": aspect_ratio,
        "locale": "en"
    }

    # Add optional fields if they make sense
    if media_type == "voice":
        # For voice generation, we might want to use the prompt as text_input
        items = user_payload.get("items", [])
        if items and items[0].get("prompt"):
            api_payload["text_input"] = items[0]["prompt"]

    print(f"📤 Transformed payload keys: {list(api_payload.keys())}")
    print(f"📊 Transformation summary:")
    print(f"   - media_type: {media_type}")
    print(f"   - shop_id: {shop_id}")
    print(f"   - product_ids: {product_ids}")
    print(f"   - aspect_ratio: {aspect_ratio}")
    print(f"   - Lost fields: {[k for k in user_payload.keys() if k not in api_payload]}")

    return api_payload

def test_health_check():
    """Test the health check endpoint to verify API is running."""
    print("🔍 Testing health check endpoint...")

    try:
        # Try multiple approaches to test connectivity
        response = requests.get(f"{API_BASE_URL}/", timeout=10)
        print(f"Root endpoint status: {response.status_code}")

        if response.status_code == 200:
            root_data = response.json()
            print(f"✅ API is responding: {root_data.get('message', 'unknown')}")
            return True
        else:
            print(f"❌ Root endpoint failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False

    except requests.exceptions.RequestException as e:
        print(f"❌ API connection failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return False

def test_media_generate_endpoint():
    """Test the media generate endpoint with original user payload (no transformation)."""
    print("\n🎨 Testing media generate endpoint with original payload...")

    # Use the original payload directly (no transformation needed)
    api_payload = USER_PAYLOAD

    print("📤 Sending original user payload (no transformation):")
    print(f"   - Keys: {list(api_payload.keys())}")
    print(f"   - Media type: {api_payload.get('media_type')}")
    print(f"   - Product IDs: {api_payload.get('product_ids')}")
    print(f"   - Has settings: {'settings' in api_payload}")
    print(f"   - Has items: {'items' in api_payload}")

    headers = {
        "Authorization": AUTH_TOKEN,
        "Content-Type": "application/json",
        "Accept": "application/json"
    }

    try:
        response = requests.post(
            f"{API_BASE_URL}/media/generate",
            json=api_payload,
            headers=headers,
            timeout=30
        )

        print(f"\n📥 Response status: {response.status_code}")
        print(f"📥 Response headers: {dict(response.headers)}")

        if response.status_code == 200:
            print("✅ Request successful with original payload!")
            response_data = response.json()
            print("📄 Response data:")
            print(json.dumps(response_data, indent=2))
            return True, response_data
        else:
            print(f"❌ Request failed with status {response.status_code}")
            print("📄 Error response:")

            try:
                error_data = response.json()
                print(json.dumps(error_data, indent=2))
            except json.JSONDecodeError:
                print(response.text)

            return False, None

    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return False, None

def test_with_original_payload():
    """Test with the original user payload to see if it works directly."""
    print("\n🔍 Testing with original user payload (full format)...")
    print("📋 Original payload structure:")
    print(f"   - Keys: {list(USER_PAYLOAD.keys())}")
    print(f"   - Has 'mode': {'mode' in USER_PAYLOAD}")
    print(f"   - Has 'model': {'model' in USER_PAYLOAD}")
    print(f"   - Has 'settings': {'settings' in USER_PAYLOAD}")
    print(f"   - Has 'items': {'items' in USER_PAYLOAD}")

    headers = {
        "Authorization": AUTH_TOKEN,
        "Content-Type": "application/json",
        "Accept": "application/json"
    }

    try:
        response = requests.post(
            f"{API_BASE_URL}/media/generate",
            json=USER_PAYLOAD,
            headers=headers,
            timeout=30
        )

        print(f"📥 Original payload response status: {response.status_code}")
        print(f"📥 Response headers: {dict(response.headers)}")

        if response.status_code == 200:
            print("✅ SUCCESS! Original payload works directly!")
            print("📄 Response data:")

            try:
                response_data = response.json()
                print(json.dumps(response_data, indent=2))
                return True, response_data
            except json.JSONDecodeError:
                print("Raw response:", response.text)
                return True, None

        elif response.status_code == 422:
            print("⚠️  Validation error - API doesn't accept this format")
            print("📄 Error details:")

            try:
                error_data = response.json()
                print(json.dumps(error_data, indent=2))
            except json.JSONDecodeError:
                print("Raw response:", response.text)

        elif response.status_code == 500:
            print("❌ Server error with original payload")
            print("📄 Error details:")

            try:
                error_data = response.json()
                print(json.dumps(error_data, indent=2))
            except json.JSONDecodeError:
                print("Raw response:", response.text)

        return False, None

    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return False, None

def main():
    """Main test function."""
    print("🚀 Starting Media Generation API Test")
    print("=" * 50)

    # Test 1: Basic connectivity test
    print("🔍 Testing API connectivity...")
    try:
        response = requests.get(f"{API_BASE_URL}/", timeout=5)
        if response.status_code == 200:
            print("✅ API is responding to root endpoint")
        else:
            print(f"⚠️  API responded with status {response.status_code}")
    except Exception as e:
        print(f"❌ Cannot connect to API: {e}")
        print("💡 Make sure the API server is running on localhost:8123")
        sys.exit(1)

    # Test 1: Test with original payload (primary approach)
    print("\n" + "=" * 30)
    print("🧪 PRIMARY TEST: Original User Payload (Full Format)")
    print("=" * 30)
    primary_success, primary_response = test_media_generate_endpoint()

    # Test 2: Test with transformed payload (for comparison)
    print("\n" + "=" * 30)
    print("🧪 COMPARISON TEST: Transformed Payload (Simplified Format)")
    print("=" * 30)
    transformed_payload = transform_payload_to_api_format(USER_PAYLOAD)
    print("📤 Transformed payload for comparison:")
    print(json.dumps(transformed_payload, indent=2))

    headers = {
        "Authorization": AUTH_TOKEN,
        "Content-Type": "application/json",
        "Accept": "application/json"
    }

    try:
        response = requests.post(
            f"{API_BASE_URL}/media/generate",
            json=transformed_payload,
            headers=headers,
            timeout=30
        )

        transformed_success = response.status_code == 200
        transformed_response = response.json() if transformed_success else None

        print(f"📥 Transformed payload response status: {response.status_code}")
        if transformed_success:
            print("✅ Transformed payload also works")
        else:
            print("❌ Transformed payload failed")

    except Exception as e:
        print(f"❌ Transformed payload test failed: {e}")
        transformed_success = False
        transformed_response = None

    print("\n" + "=" * 50)
    print("📊 TEST RESULTS:")
    print("=" * 50)
    print(f"Primary test (original payload): {'✅ SUCCESS' if primary_success else '❌ FAILED'}")
    print(f"Comparison test (transformed): {'✅ SUCCESS' if transformed_success else '❌ FAILED'}")

    # Analysis and Recommendation
    print("\n" + "=" * 30)
    print("🔍 FINAL ANALYSIS")
    print("=" * 30)

    if primary_success:
        print("🎯 SUCCESS! Original payload works perfectly!")
        print("📋 Key findings:")
        print("   ✅ API natively supports full user payload format")
        print("   ✅ No transformation required")
        print("   ✅ All user data preserved (mode, model, settings, items)")
        print("   ✅ More flexible for future enhancements")
        print("")
        print("💡 IMPLEMENTATION: Script updated to use original payload directly")
        print("   - Removed unnecessary transformation")
        print("   - Simplified client-side logic")
        print("   - Better user experience")

    elif transformed_success:
        print("⚠️  Original payload failed, but transformed works")
        print("📋 Finding: May need to fall back to transformation")
        print("💡 Consider investigating why original payload fails")

    else:
        print("❌ BOTH APPROACHES FAILED!")
        print("📋 Possible issues:")
        print("   - Authentication problems")
        print("   - Database connectivity issues")
        print("   - Provider configuration issues")
        print("   - Server-side bugs")
        print("💡 Check server logs for more details")

    print("\n🔧 To run this test again:")
    print("   python test_media_generate.py")

if __name__ == "__main__":
    main()