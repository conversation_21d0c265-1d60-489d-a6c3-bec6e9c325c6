# Outputs for ProductVideo Infrastructure

# VPC Outputs
output "vpc_id" {
  description = "ID of the VPC"
  value       = aws_vpc.main.id
}

output "vpc_cidr_block" {
  description = "CIDR block of the VPC"
  value       = aws_vpc.main.cidr_block
}

output "public_subnet_ids" {
  description = "IDs of the public subnets"
  value       = aws_subnet.public[*].id
}

output "private_subnet_ids" {
  description = "IDs of the private subnets"
  value       = aws_subnet.private[*].id
}

# Database Outputs
output "database_endpoint" {
  description = "RDS instance endpoint"
  value       = aws_db_instance.main.endpoint
  sensitive   = true
}

output "database_port" {
  description = "RDS instance port"
  value       = aws_db_instance.main.port
}

output "database_name" {
  description = "Database name"
  value       = aws_db_instance.main.db_name
}

# Redis Outputs
output "redis_endpoint" {
  description = "ElastiCache Redis endpoint"
  value       = aws_elasticache_replication_group.main.primary_endpoint_address
  sensitive   = true
}

output "redis_port" {
  description = "ElastiCache Redis port"
  value       = aws_elasticache_replication_group.main.port
}

# S3 Outputs
output "s3_bucket_name" {
  description = "Name of the S3 bucket for videos"
  value       = aws_s3_bucket.videos.bucket
}

output "s3_bucket_arn" {
  description = "ARN of the S3 bucket for videos"
  value       = aws_s3_bucket.videos.arn
}

output "s3_bucket_domain_name" {
  description = "Domain name of the S3 bucket"
  value       = aws_s3_bucket.videos.bucket_domain_name
}

# CloudFront Outputs
output "cloudfront_distribution_id" {
  description = "ID of the CloudFront distribution"
  value       = aws_cloudfront_distribution.videos.id
}

output "cloudfront_domain_name" {
  description = "Domain name of the CloudFront distribution"
  value       = aws_cloudfront_distribution.videos.domain_name
}

output "cloudfront_hosted_zone_id" {
  description = "Hosted zone ID of the CloudFront distribution"
  value       = aws_cloudfront_distribution.videos.hosted_zone_id
}

# Security Group Outputs
output "alb_security_group_id" {
  description = "ID of the ALB security group"
  value       = aws_security_group.alb.id
}

output "ecs_security_group_id" {
  description = "ID of the ECS security group"
  value       = aws_security_group.ecs.id
}

output "rds_security_group_id" {
  description = "ID of the RDS security group"
  value       = aws_security_group.rds.id
}

output "redis_security_group_id" {
  description = "ID of the Redis security group"
  value       = aws_security_group.redis.id
}

# Environment Configuration
output "environment_variables" {
  description = "Environment variables for the application"
  value = {
    DATABASE_URL = "postgresql://${var.db_username}:${var.db_password}@${aws_db_instance.main.endpoint}/${var.db_name}"
    REDIS_URL    = "redis://${aws_elasticache_replication_group.main.primary_endpoint_address}:${aws_elasticache_replication_group.main.port}"
    S3_BUCKET_NAME = aws_s3_bucket.videos.bucket
    AWS_REGION     = var.aws_region
    ENVIRONMENT    = var.environment
    CDN_DOMAIN     = aws_cloudfront_distribution.videos.domain_name
  }
  sensitive = true
}

# Connection Information
output "connection_info" {
  description = "Connection information for services"
  value = {
    database = {
      host     = aws_db_instance.main.address
      port     = aws_db_instance.main.port
      database = aws_db_instance.main.db_name
      username = var.db_username
    }
    redis = {
      host = aws_elasticache_replication_group.main.primary_endpoint_address
      port = aws_elasticache_replication_group.main.port
    }
    storage = {
      bucket     = aws_s3_bucket.videos.bucket
      cdn_domain = aws_cloudfront_distribution.videos.domain_name
    }
  }
  sensitive = true
}

# Resource ARNs
output "resource_arns" {
  description = "ARNs of created resources"
  value = {
    vpc                = aws_vpc.main.arn
    database          = aws_db_instance.main.arn
    redis             = aws_elasticache_replication_group.main.arn
    s3_bucket         = aws_s3_bucket.videos.arn
    cloudfront        = aws_cloudfront_distribution.videos.arn
  }
}

# Tags
output "common_tags" {
  description = "Common tags applied to resources"
  value       = local.common_tags
}
