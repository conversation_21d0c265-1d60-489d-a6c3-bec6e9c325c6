"""
Load testing script for ProductVideo backend.
Tests API endpoints under load to ensure performance and stability.
"""

import asyncio
import aiohttp
import time
import json
import random
import statistics
from typing import List, Dict, Any
from dataclasses import dataclass
from datetime import datetime


@dataclass
class LoadTestResult:
    """Result of a load test."""
    endpoint: str
    total_requests: int
    successful_requests: int
    failed_requests: int
    avg_response_time: float
    min_response_time: float
    max_response_time: float
    p95_response_time: float
    requests_per_second: float
    errors: List[str]


class LoadTester:
    """Load testing utility for API endpoints."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session = None
        
    async def __aenter__(self):
        """Async context manager entry."""
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()
    
    async def make_request(
        self, 
        method: str, 
        endpoint: str, 
        data: Dict[str, Any] = None,
        headers: Dict[str, str] = None
    ) -> Dict[str, Any]:
        """Make a single HTTP request and measure response time."""
        start_time = time.time()
        
        try:
            url = f"{self.base_url}{endpoint}"
            
            if method.upper() == "GET":
                async with self.session.get(url, headers=headers) as response:
                    response_data = await response.json() if response.content_type == 'application/json' else await response.text()
                    return {
                        "success": True,
                        "status_code": response.status,
                        "response_time": time.time() - start_time,
                        "data": response_data
                    }
            elif method.upper() == "POST":
                async with self.session.post(url, json=data, headers=headers) as response:
                    response_data = await response.json() if response.content_type == 'application/json' else await response.text()
                    return {
                        "success": True,
                        "status_code": response.status,
                        "response_time": time.time() - start_time,
                        "data": response_data
                    }
                    
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "response_time": time.time() - start_time
            }
    
    async def run_load_test(
        self,
        endpoint: str,
        method: str = "GET",
        concurrent_users: int = 10,
        requests_per_user: int = 100,
        data_generator=None,
        headers: Dict[str, str] = None
    ) -> LoadTestResult:
        """Run load test on a specific endpoint."""
        print(f"Starting load test: {method} {endpoint}")
        print(f"Concurrent users: {concurrent_users}")
        print(f"Requests per user: {requests_per_user}")
        print(f"Total requests: {concurrent_users * requests_per_user}")
        
        # Collect all response times and errors
        response_times = []
        errors = []
        successful_requests = 0
        failed_requests = 0
        
        start_time = time.time()
        
        async def user_session(user_id: int):
            """Simulate a single user making multiple requests."""
            for request_num in range(requests_per_user):
                # Generate request data if generator provided
                request_data = data_generator(user_id, request_num) if data_generator else None
                
                # Make request
                result = await self.make_request(method, endpoint, request_data, headers)
                
                # Record results
                response_times.append(result["response_time"])
                
                if result["success"] and result.get("status_code", 0) < 400:
                    nonlocal successful_requests
                    successful_requests += 1
                else:
                    nonlocal failed_requests
                    failed_requests += 1
                    error_msg = result.get("error", f"HTTP {result.get('status_code', 'Unknown')}")
                    errors.append(error_msg)
                
                # Small delay to simulate realistic user behavior
                await asyncio.sleep(0.01)
        
        # Run concurrent user sessions
        tasks = [user_session(user_id) for user_id in range(concurrent_users)]
        await asyncio.gather(*tasks)
        
        total_time = time.time() - start_time
        total_requests = len(response_times)
        
        # Calculate statistics
        avg_response_time = statistics.mean(response_times) if response_times else 0
        min_response_time = min(response_times) if response_times else 0
        max_response_time = max(response_times) if response_times else 0
        
        # Calculate 95th percentile
        sorted_times = sorted(response_times)
        p95_index = int(0.95 * len(sorted_times))
        p95_response_time = sorted_times[p95_index] if sorted_times else 0
        
        requests_per_second = total_requests / total_time if total_time > 0 else 0
        
        return LoadTestResult(
            endpoint=endpoint,
            total_requests=total_requests,
            successful_requests=successful_requests,
            failed_requests=failed_requests,
            avg_response_time=avg_response_time,
            min_response_time=min_response_time,
            max_response_time=max_response_time,
            p95_response_time=p95_response_time,
            requests_per_second=requests_per_second,
            errors=list(set(errors))  # Unique errors
        )
    
    def print_results(self, result: LoadTestResult):
        """Print load test results in a formatted way."""
        print(f"\n{'='*60}")
        print(f"LOAD TEST RESULTS: {result.endpoint}")
        print(f"{'='*60}")
        print(f"Total Requests:      {result.total_requests}")
        print(f"Successful:          {result.successful_requests} ({result.successful_requests/result.total_requests*100:.1f}%)")
        print(f"Failed:              {result.failed_requests} ({result.failed_requests/result.total_requests*100:.1f}%)")
        print(f"Requests/Second:     {result.requests_per_second:.2f}")
        print(f"Avg Response Time:   {result.avg_response_time*1000:.2f}ms")
        print(f"Min Response Time:   {result.min_response_time*1000:.2f}ms")
        print(f"Max Response Time:   {result.max_response_time*1000:.2f}ms")
        print(f"95th Percentile:     {result.p95_response_time*1000:.2f}ms")
        
        if result.errors:
            print(f"\nErrors ({len(result.errors)} unique):")
            for error in result.errors[:10]:  # Show first 10 unique errors
                print(f"  - {error}")
            if len(result.errors) > 10:
                print(f"  ... and {len(result.errors) - 10} more")


# Data generators for different endpoints
def analytics_event_generator(user_id: int, request_num: int) -> Dict[str, Any]:
    """Generate analytics event data for load testing."""
    event_types = ["video_view", "video_play", "video_complete", "cta_click", "add_to_cart", "purchase"]
    
    return {
        "event_type": random.choice(event_types),
        "session_id": f"load-test-session-{user_id}",
        "user_id": f"load-test-user-{user_id}",
        "product_id": f"product-{random.randint(1, 100)}",
        "video_variant_id": random.randint(1, 10),
        "duration": random.uniform(10, 60),
        "position": random.uniform(0, 30),
        "properties": {
            "test": "load_test",
            "user_id": user_id,
            "request_num": request_num
        }
    }


def video_generation_request_generator(user_id: int, request_num: int) -> Dict[str, Any]:
    """Generate video generation request data for load testing."""
    return {
        "product_ids": [f"product-{random.randint(1, 1000)}"],
        "template_id": f"template-{random.randint(1, 5)}",
        "voice_id": f"voice-{random.randint(1, 10)}",
        "script": f"Load test video script for user {user_id}, request {request_num}"
    }


async def run_comprehensive_load_test():
    """Run comprehensive load tests on all major endpoints."""
    
    async with LoadTester() as tester:
        # Test results storage
        results = []
        
        # 1. Health check endpoint (baseline)
        print("Testing health check endpoint...")
        health_result = await tester.run_load_test(
            endpoint="/health",
            method="GET",
            concurrent_users=50,
            requests_per_user=20
        )
        results.append(health_result)
        tester.print_results(health_result)
        
        # 2. Analytics event ingestion (high load)
        print("\nTesting analytics event ingestion...")
        analytics_result = await tester.run_load_test(
            endpoint="/api/analytics/events/ingest",
            method="POST",
            concurrent_users=20,
            requests_per_user=50,
            data_generator=analytics_event_generator,
            headers={"Content-Type": "application/json"}
        )
        results.append(analytics_result)
        tester.print_results(analytics_result)
        
        # 3. Video generation endpoint (moderate load)
        print("\nTesting video generation endpoint...")
        video_result = await tester.run_load_test(
            endpoint="/api/video-generation/generate",
            method="POST",
            concurrent_users=5,
            requests_per_user=10,
            data_generator=video_generation_request_generator,
            headers={"Content-Type": "application/json"}
        )
        results.append(video_result)
        tester.print_results(video_result)
        
        # 4. Analytics dashboard (read-heavy)
        print("\nTesting analytics dashboard...")
        dashboard_result = await tester.run_load_test(
            endpoint="/api/analytics/dashboard",
            method="GET",
            concurrent_users=30,
            requests_per_user=20
        )
        results.append(dashboard_result)
        tester.print_results(dashboard_result)
        
        # 5. Billing usage endpoint
        print("\nTesting billing usage endpoint...")
        billing_result = await tester.run_load_test(
            endpoint="/api/billing/tenants/1/usage",
            method="GET",
            concurrent_users=15,
            requests_per_user=30
        )
        results.append(billing_result)
        tester.print_results(billing_result)
        
        # Summary
        print(f"\n{'='*80}")
        print("LOAD TEST SUMMARY")
        print(f"{'='*80}")
        
        for result in results:
            success_rate = result.successful_requests / result.total_requests * 100
            print(f"{result.endpoint:40} | {result.requests_per_second:8.2f} req/s | {success_rate:6.1f}% success | {result.avg_response_time*1000:8.2f}ms avg")
        
        # Check for performance issues
        print(f"\n{'='*80}")
        print("PERFORMANCE ANALYSIS")
        print(f"{'='*80}")
        
        for result in results:
            issues = []
            
            if result.avg_response_time > 1.0:  # > 1 second
                issues.append("High average response time")
            
            if result.p95_response_time > 2.0:  # > 2 seconds
                issues.append("High 95th percentile response time")
            
            if result.failed_requests / result.total_requests > 0.05:  # > 5% failure rate
                issues.append("High failure rate")
            
            if result.requests_per_second < 10:  # < 10 req/s
                issues.append("Low throughput")
            
            if issues:
                print(f"⚠️  {result.endpoint}: {', '.join(issues)}")
            else:
                print(f"✅ {result.endpoint}: Performance looks good")


async def run_queue_load_test():
    """Test job queue performance under load."""
    print("Testing job queue performance...")
    
    async with LoadTester() as tester:
        # Simulate enqueueing many video generation jobs
        result = await tester.run_load_test(
            endpoint="/api/video-generation/generate",
            method="POST",
            concurrent_users=10,
            requests_per_user=100,  # 1000 total jobs
            data_generator=video_generation_request_generator,
            headers={"Content-Type": "application/json"}
        )
        
        tester.print_results(result)
        
        # Check queue depth
        queue_result = await tester.make_request("GET", "/api/admin/queue/stats")
        if queue_result["success"]:
            print(f"\nQueue Stats: {queue_result['data']}")


if __name__ == "__main__":
    print("ProductVideo Backend Load Testing")
    print("=" * 50)
    
    # Run comprehensive load test
    asyncio.run(run_comprehensive_load_test())
    
    # Optionally run queue-specific test
    print("\n" + "=" * 50)
    print("Running queue load test...")
    asyncio.run(run_queue_load_test())
