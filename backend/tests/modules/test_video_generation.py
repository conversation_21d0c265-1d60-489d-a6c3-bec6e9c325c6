"""
Tests for video generation module.
"""

import pytest
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime

from modules.video_generation.service import video_generation_service
from modules.media.video_service import ai_video_service
from modules.video_generation.transcoding_service import video_transcoding_service
from modules.video_generation.models import VideoJob, VideoVariant, JobStatus, VideoVariantStatus
from modules.video_generation.schemas import VideoGenerationRequest


class TestVideoGenerationService:
    """Test video generation service functionality."""

    @pytest.mark.asyncio
    async def test_create_video_job(self, db_session, test_utils):
        """Test video job creation."""
        # Create tenant
        tenant = await test_utils.create_test_tenant(db_session)
        
        # Create video job
        job_data = {
            "tenant_id": tenant.id,
            "product_ids": ["product-1", "product-2"],
            "template_id": "template-1",
            "voice_id": "voice-1",
            "script": "Test video script"
        }
        
        job = await video_generation_service.create_video_job(db_session, job_data)
        
        assert job.id is not None
        assert job.tenant_id == tenant.id
        assert job.status == JobStatus.PENDING
        assert "product-1" in job.product_ids

    @pytest.mark.asyncio
    async def test_get_video_job(self, db_session, test_utils):
        """Test getting video job by ID."""
        # Create job
        job = await test_utils.create_test_video_job(db_session)
        
        # Get job
        retrieved_job = await video_generation_service.get_video_job(db_session, job.id)
        
        assert retrieved_job is not None
        assert retrieved_job.id == job.id

    @pytest.mark.asyncio
    async def test_update_job_status(self, db_session, test_utils):
        """Test updating job status."""
        # Create job
        job = await test_utils.create_test_video_job(db_session)
        
        # Update status
        updated_job = await video_generation_service.update_job_status(
            db_session, job.id, JobStatus.PROCESSING
        )
        
        assert updated_job.status == JobStatus.PROCESSING

    @pytest.mark.asyncio
    async def test_create_video_variant(self, db_session, test_utils):
        """Test video variant creation."""
        # Create job
        job = await test_utils.create_test_video_job(db_session)
        
        # Create variant
        variant_data = {
            "job_id": job.id,
            "product_id": "product-123",
            "variant_name": "Test Variant",
            "video_url": "https://example.com/video.mp4",
            "thumbnail_url": "https://example.com/thumb.jpg",
            "duration_seconds": 30
        }
        
        variant = await video_generation_service.create_video_variant(db_session, variant_data)
        
        assert variant.id is not None
        assert variant.job_id == job.id
        assert variant.variant_name == "Test Variant"
        assert variant.status == VideoVariantStatus.READY

    @pytest.mark.asyncio
    async def test_get_job_variants(self, db_session, test_utils):
        """Test getting variants for a job."""
        # Create job
        job = await test_utils.create_test_video_job(db_session)
        
        # Create variants
        for i in range(3):
            variant_data = {
                "job_id": job.id,
                "product_id": f"product-{i}",
                "variant_name": f"Variant {i+1}",
                "video_url": f"https://example.com/video{i}.mp4"
            }
            await video_generation_service.create_video_variant(db_session, variant_data)
        
        # Get variants
        variants = await video_generation_service.get_job_variants(db_session, job.id)
        
        assert len(variants) == 3
        assert all(v.job_id == job.id for v in variants)


class TestAIVideoService:
    """Test AI video generation service."""

    @pytest.mark.asyncio
    async def test_generate_video_mock_provider(self):
        """Test video generation with mock provider."""
        request = VideoGenerationRequest(
            product_title="Test Product",
            product_description="A great test product",
            product_images=["https://example.com/image.jpg"],
            template_id="template-1",
            voice_id="voice-1",
            variants_count=2
        )
        
        result = await ai_video_service.generate_video(request)
        
        assert result.success is True
        assert len(result.variants) == 2
        assert all("video_url" in variant for variant in result.variants)

    @pytest.mark.asyncio
    @patch('modules.video_generation.ai_service.httpx.AsyncClient.post')
    async def test_generate_video_revid_provider(self, mock_post):
        """Test video generation with Revid provider."""
        # Mock API response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "success": True,
            "job_id": "revid-job-123",
            "videos": [
                {
                    "id": "video-1",
                    "url": "https://revid.com/video1.mp4",
                    "thumbnail": "https://revid.com/thumb1.jpg"
                }
            ]
        }
        mock_post.return_value = mock_response
        
        # Set provider to revid
        ai_video_service.provider = "revid_ai"
        
        request = VideoGenerationRequest(
            product_title="Test Product",
            product_description="A great test product",
            template_id="template-1",
            voice_id="voice-1",
            variants_count=1
        )
        
        result = await ai_video_service.generate_video(request)
        
        assert result.success is True
        assert len(result.variants) == 1

    @pytest.mark.asyncio
    async def test_generate_video_error_handling(self):
        """Test error handling in video generation."""
        # Test with invalid request
        request = VideoGenerationRequest(
            product_title="",  # Empty title should cause error
            product_description="",
            template_id="invalid-template",
            voice_id="invalid-voice",
            variants_count=0
        )
        
        result = await ai_video_service.generate_video(request)
        
        assert result.success is False
        assert result.error_message is not None


class TestVideoTranscodingService:
    """Test video transcoding service."""

    @pytest.mark.asyncio
    @patch('modules.video_generation.transcoding_service.httpx.AsyncClient.stream')
    @patch('modules.video_generation.transcoding_service.subprocess.run')
    async def test_process_video(self, mock_subprocess, mock_stream):
        """Test video processing pipeline."""
        # Mock video download
        mock_response = Mock()
        mock_response.aiter_bytes = AsyncMock(return_value=[b'fake_video_data'])
        mock_stream.return_value.__aenter__.return_value = mock_response
        
        # Mock ffmpeg commands
        mock_subprocess.return_value.returncode = 0
        mock_subprocess.return_value.stdout = b'{"format": {"duration": "30.0"}}'
        
        # Process video
        result = await video_transcoding_service.process_video(
            video_url="https://example.com/video.mp4",
            output_formats=['mp4'],
            generate_thumbnails=True,
            generate_subtitles=True
        )
        
        assert 'source_info' in result
        assert 'outputs' in result
        assert 'thumbnails' in result
        assert 'subtitles' in result

    @pytest.mark.asyncio
    @patch('modules.video_generation.transcoding_service.subprocess.run')
    async def test_get_video_info(self, mock_subprocess):
        """Test video info extraction."""
        # Mock ffprobe output
        mock_subprocess.return_value.returncode = 0
        mock_subprocess.return_value.stdout = b'''{
            "format": {
                "duration": "30.5",
                "size": "1024000"
            },
            "streams": [{
                "codec_type": "video",
                "width": 1920,
                "height": 1080,
                "codec_name": "h264",
                "r_frame_rate": "30/1"
            }]
        }'''
        
        info = await video_transcoding_service._get_video_info("/fake/path.mp4")
        
        assert info['duration'] == 30.5
        assert info['width'] == 1920
        assert info['height'] == 1080

    @pytest.mark.asyncio
    @patch('modules.video_generation.transcoding_service.subprocess.run')
    async def test_transcode_to_mp4(self, mock_subprocess):
        """Test MP4 transcoding."""
        mock_subprocess.return_value.returncode = 0
        
        output_path = await video_transcoding_service._transcode_to_mp4("/fake/input.mp4")
        
        assert output_path.endswith('.mp4')
        mock_subprocess.assert_called_once()

    @pytest.mark.asyncio
    @patch('modules.video_generation.transcoding_service.subprocess.run')
    async def test_generate_thumbnails(self, mock_subprocess):
        """Test thumbnail generation."""
        # Mock ffprobe for duration
        mock_subprocess.side_effect = [
            # First call for video info
            Mock(returncode=0, stdout=b'{"format": {"duration": "30.0"}}'),
            # Subsequent calls for thumbnail generation
            Mock(returncode=0),
            Mock(returncode=0),
            Mock(returncode=0)
        ]
        
        thumbnails = await video_transcoding_service._generate_thumbnails("/fake/video.mp4", 3)
        
        assert len(thumbnails) == 3
        assert all(thumb.endswith('.jpg') for thumb in thumbnails)


class TestVideoGenerationIntegration:
    """Integration tests for video generation."""

    @pytest.mark.asyncio
    async def test_full_video_generation_flow(self, db_session, test_utils):
        """Test complete video generation flow."""
        # 1. Create tenant
        tenant = await test_utils.create_test_tenant(db_session)
        
        # 2. Create video job
        job_data = {
            "tenant_id": tenant.id,
            "product_ids": ["product-123"],
            "template_id": "template-1",
            "voice_id": "voice-1",
            "script": "Test product video"
        }
        job = await video_generation_service.create_video_job(db_session, job_data)
        
        # 3. Generate video (mocked)
        request = VideoGenerationRequest(
            product_title="Test Product",
            product_description="Great product",
            template_id="template-1",
            voice_id="voice-1",
            variants_count=2
        )
        
        generation_result = await ai_video_service.generate_video(request)
        
        # 4. Create variants from generation result
        variants = []
        for i, variant_data in enumerate(generation_result.variants):
            variant = await video_generation_service.create_video_variant(db_session, {
                "job_id": job.id,
                "product_id": "product-123",
                "variant_name": f"Variant {i+1}",
                "video_url": variant_data["video_url"],
                "thumbnail_url": variant_data.get("thumbnail_url"),
                "duration_seconds": variant_data.get("duration", 30)
            })
            variants.append(variant)
        
        # 5. Update job status
        await video_generation_service.update_job_status(db_session, job.id, JobStatus.COMPLETED)
        
        # Verify results
        final_job = await video_generation_service.get_video_job(db_session, job.id)
        job_variants = await video_generation_service.get_job_variants(db_session, job.id)
        
        assert final_job.status == JobStatus.COMPLETED
        assert len(job_variants) == 2
        assert all(v.status == VideoVariantStatus.READY for v in job_variants)

    @pytest.mark.asyncio
    async def test_video_generation_error_handling(self, db_session, test_utils):
        """Test error handling in video generation flow."""
        # Create job
        job = await test_utils.create_test_video_job(db_session)
        
        # Simulate generation failure
        with patch.object(ai_video_service, 'generate_video') as mock_generate:
            mock_generate.return_value = Mock(
                success=False,
                error_message="Generation failed",
                variants=[]
            )
            
            # Update job status to failed
            await video_generation_service.update_job_status(
                db_session, job.id, JobStatus.FAILED, "Generation failed"
            )
            
            # Verify error state
            failed_job = await video_generation_service.get_video_job(db_session, job.id)
            assert failed_job.status == JobStatus.FAILED
            assert failed_job.error_message == "Generation failed"

    @pytest.mark.asyncio
    async def test_variant_regeneration(self, db_session, test_utils):
        """Test video variant regeneration."""
        # Create job with variant
        job = await test_utils.create_test_video_job(db_session)
        
        original_variant = await video_generation_service.create_video_variant(db_session, {
            "job_id": job.id,
            "product_id": "product-123",
            "variant_name": "Original Variant",
            "video_url": "https://example.com/original.mp4"
        })
        
        # Regenerate variant
        new_variant = await video_generation_service.create_video_variant(db_session, {
            "job_id": job.id,
            "product_id": "product-123",
            "variant_name": "Regenerated Variant",
            "video_url": "https://example.com/regenerated.mp4",
            "parent_variant_id": original_variant.id
        })
        
        # Verify regeneration
        assert new_variant.id != original_variant.id
        assert new_variant.variant_name == "Regenerated Variant"
        assert new_variant.job_id == job.id
