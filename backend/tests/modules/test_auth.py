import pytest
from httpx import As<PERSON><PERSON><PERSON>
from sqlalchemy.ext.asyncio import AsyncSession

from modules.auth.models import User


@pytest.mark.asyncio
async def test_register_user(client: AsyncClient):
    """Test user registration."""
    user_data = {
        "email": "<EMAIL>",
        "password": "testpassword123",
        "full_name": "New User"
    }
    
    response = await client.post("/api/auth/register", json=user_data)
    assert response.status_code == 200
    
    data = response.json()
    assert data["email"] == user_data["email"]
    assert data["full_name"] == user_data["full_name"]
    assert data["is_active"] is True
    assert "id" in data


@pytest.mark.asyncio
async def test_register_duplicate_email(client: AsyncClient, test_user: User):
    """Test registration with duplicate email."""
    user_data = {
        "email": test_user.email,
        "password": "testpassword123",
        "full_name": "Duplicate User"
    }
    
    response = await client.post("/api/auth/register", json=user_data)
    assert response.status_code == 400
    assert "Email already registered" in response.json()["detail"]


@pytest.mark.asyncio
async def test_login_user(client: AsyncClient, test_user: User):
    """Test user login."""
    login_data = {
        "email": test_user.email,
        "password": "testpassword"
    }
    
    response = await client.post("/api/auth/login", json=login_data)
    assert response.status_code == 200
    
    data = response.json()
    assert "access_token" in data
    assert data["token_type"] == "bearer"


@pytest.mark.asyncio
async def test_login_invalid_credentials(client: AsyncClient):
    """Test login with invalid credentials."""
    login_data = {
        "email": "<EMAIL>",
        "password": "wrongpassword"
    }
    
    response = await client.post("/api/auth/login", json=login_data)
    assert response.status_code == 401
    assert "Incorrect email or password" in response.json()["detail"]


@pytest.mark.asyncio
async def test_get_current_user(authenticated_client: AsyncClient, test_user: User):
    """Test getting current user information."""
    response = await authenticated_client.get("/api/auth/me")
    assert response.status_code == 200
    
    data = response.json()
    assert data["email"] == test_user.email
    assert data["full_name"] == test_user.full_name
    assert data["is_active"] is True


@pytest.mark.asyncio
async def test_get_current_user_unauthorized(client: AsyncClient):
    """Test getting current user without authentication."""
    response = await client.get("/api/auth/me")
    assert response.status_code == 401
