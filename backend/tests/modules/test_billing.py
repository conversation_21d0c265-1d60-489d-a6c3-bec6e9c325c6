"""
Tests for billing module.
"""

import pytest
from unittest.mock import Mock, patch
from decimal import Decimal

from modules.billing.service import billing_service
from modules.billing.stripe_service import stripe_service
from modules.billing.models import Tenant, Subscription, BillingUsage, UsageType, BillingPlan
from modules.billing.schemas import (
    TenantCreate, CreateSubscriptionRequest, RecordUsageRequest
)


class TestBillingService:
    """Test billing service functionality."""

    @pytest.mark.asyncio
    async def test_create_tenant(self, db_session, test_utils):
        """Test tenant creation."""
        tenant_data = TenantCreate(
            name="Test Tenant",
            slug="test-tenant",
            shop_domain="test-shop.myshopify.com",
            billing_email="<EMAIL>",
            billing_plan=BillingPlan.STARTER
        )
        
        tenant = await billing_service.create_tenant(
            db_session, tenant_data, create_stripe_customer=False
        )
        
        assert tenant.id is not None
        assert tenant.name == "Test Tenant"
        assert tenant.slug == "test-tenant"
        assert tenant.billing_plan == BillingPlan.STARTER

    @pytest.mark.asyncio
    async def test_get_tenant_by_shop_domain(self, db_session, test_utils):
        """Test getting tenant by shop domain."""
        # Create tenant
        tenant = await test_utils.create_test_tenant(db_session)

        # Find by domain
        found_tenant = await billing_service.get_tenant_by_shop_domain(
            db_session, tenant.shop_domain
        )
        
        assert found_tenant is not None
        assert found_tenant.id == tenant.id

    @pytest.mark.asyncio
    async def test_record_video_generation_usage(self, db_session, test_utils):
        """Test recording video generation usage."""
        # Create tenant
        tenant = await test_utils.create_test_tenant(db_session)
        
        # Record usage
        usage_record = await billing_service.record_video_generation_usage(
            db_session,
            tenant_id=tenant.id,
            video_job_id="test-job-123",
            variant_count=4,
            metadata={"test": "data"}
        )
        
        assert usage_record.tenant_id == tenant.id
        assert usage_record.usage_type == UsageType.VIDEO_GENERATION
        assert usage_record.quantity == 4.0
        assert usage_record.resource_id == "test-job-123"

    @pytest.mark.asyncio
    async def test_get_usage_summary(self, db_session, test_utils):
        """Test getting usage summary."""
        # Create tenant
        tenant = await test_utils.create_test_tenant(db_session)
        
        # Record some usage
        await billing_service.record_video_generation_usage(
            db_session, tenant.id, "job-1", 2
        )
        await billing_service.record_storage_usage(
            db_session, tenant.id, 1.5
        )
        
        # Get summary
        summary = await billing_service.get_usage_summary(db_session, tenant.id)
        
        assert summary.tenant_id == tenant.id
        assert summary.usage_by_type[UsageType.VIDEO_GENERATION.value] == 2.0
        assert summary.usage_by_type[UsageType.STORAGE_GB.value] == 1.5

    @pytest.mark.asyncio
    async def test_check_usage_limits(self, db_session, test_utils):
        """Test usage limit checking."""
        # Create tenant
        tenant = await test_utils.create_test_tenant(db_session)
        
        # Check limits for starter plan
        result = await billing_service.check_usage_limits(
            db_session, tenant.id, UsageType.VIDEO_GENERATION, 5
        )
        
        assert result["can_proceed"] is True
        assert result["limit"] == 10  # Starter plan limit
        assert result["current_usage"] == 0
        assert result["remaining"] == 10

    @pytest.mark.asyncio
    async def test_usage_limit_exceeded(self, db_session, test_utils):
        """Test usage limit exceeded scenario."""
        # Create tenant
        tenant = await test_utils.create_test_tenant(db_session)
        
        # Record usage near limit
        await billing_service.record_video_generation_usage(
            db_session, tenant.id, "job-1", 8
        )
        
        # Check if we can add more
        result = await billing_service.check_usage_limits(
            db_session, tenant.id, UsageType.VIDEO_GENERATION, 5
        )
        
        assert result["can_proceed"] is False
        assert result["would_exceed_by"] == 3


class TestStripeService:
    """Test Stripe integration service."""

    @pytest.mark.asyncio
    @patch('stripe.Customer.create')
    async def test_create_customer(self, mock_create, test_utils):
        """Test Stripe customer creation."""
        # Mock Stripe response
        mock_create.return_value = Mock(id="cus_test123")
        
        # Create tenant
        tenant = Mock()
        tenant.id = 1
        tenant.billing_email = "<EMAIL>"
        tenant.name = "Test Tenant"
        tenant.shop_domain = "test.myshopify.com"
        
        # Create customer
        customer_id = await stripe_service.create_customer(tenant)
        
        assert customer_id == "cus_test123"
        mock_create.assert_called_once()

    @pytest.mark.asyncio
    @patch('stripe.Subscription.create')
    async def test_create_subscription(self, mock_create, db_session, test_utils):
        """Test Stripe subscription creation."""
        # Mock Stripe response
        mock_subscription = Mock()
        mock_subscription.id = "sub_test123"
        mock_subscription.status = "active"
        mock_subscription.current_period_start = 1609459200
        mock_subscription.current_period_end = 1612137600
        mock_subscription.trial_start = None
        mock_subscription.trial_end = None
        mock_subscription.items.data = [
            Mock(price=Mock(unit_amount=2999, currency="usd"))
        ]
        mock_subscription.metadata = {}
        mock_create.return_value = mock_subscription
        
        # Create tenant with Stripe customer
        tenant = await test_utils.create_test_tenant(db_session, {
            "name": "Test Tenant",
            "slug": "test-tenant",
            "stripe_customer_id": "cus_test123",
            "billing_email": "<EMAIL>"
        })
        
        # Create subscription request
        request = CreateSubscriptionRequest(
            tenant_id=tenant.id,
            price_id="price_test123"
        )
        
        # Create subscription
        response = await stripe_service.create_subscription(db_session, request)
        
        assert response.subscription.stripe_subscription_id == "sub_test123"
        assert response.status == "active"

    @pytest.mark.asyncio
    @patch('stripe.UsageRecord.create')
    async def test_record_usage(self, mock_create, db_session, test_utils):
        """Test Stripe usage recording."""
        # Mock Stripe response
        mock_create.return_value = Mock(id="ur_test123")
        
        # Create tenant
        tenant = await test_utils.create_test_tenant(db_session)
        
        # Create usage request
        request = RecordUsageRequest(
            tenant_id=tenant.id,
            usage_type=UsageType.VIDEO_GENERATION,
            quantity=2.0,
            resource_id="job-123"
        )
        
        # Record usage
        response = await stripe_service.record_usage(db_session, request)
        
        assert response.usage_record.tenant_id == tenant.id
        assert response.usage_record.usage_type == UsageType.VIDEO_GENERATION
        assert response.usage_record.quantity == 2.0

    @pytest.mark.asyncio
    @patch('stripe.Webhook.construct_event')
    async def test_process_webhook(self, mock_construct, db_session):
        """Test Stripe webhook processing."""
        # Mock webhook event
        mock_event = {
            "type": "customer.subscription.created",
            "data": {
                "object": {
                    "id": "sub_test123",
                    "customer": "cus_test123",
                    "status": "active"
                }
            }
        }
        mock_construct.return_value = mock_event
        
        # Process webhook
        result = await stripe_service.process_webhook(
            db_session, b'{"test": "payload"}', "test_signature"
        )
        
        assert result["status"] == "success"
        assert result["event_type"] == "customer.subscription.created"


class TestBillingModels:
    """Test billing models."""

    def test_tenant_model(self):
        """Test Tenant model."""
        tenant = Tenant(
            name="Test Tenant",
            slug="test-tenant",
            shop_domain="test.myshopify.com",
            billing_email="<EMAIL>",
            billing_plan=BillingPlan.PROFESSIONAL
        )
        
        assert tenant.name == "Test Tenant"
        assert tenant.billing_plan == BillingPlan.PROFESSIONAL
        assert str(tenant) == "<Tenant(id=None, name='Test Tenant', shop_domain='test.myshopify.com')>"

    def test_billing_usage_model(self):
        """Test BillingUsage model."""
        from datetime import datetime
        
        usage = BillingUsage(
            tenant_id=1,
            usage_type=UsageType.VIDEO_GENERATION,
            quantity=5.0,
            unit_price=Decimal("0.50"),
            total_cost=Decimal("2.50"),
            billing_period_start=datetime.utcnow(),
            billing_period_end=datetime.utcnow(),
            resource_id="job-123"
        )
        
        assert usage.usage_type == UsageType.VIDEO_GENERATION
        assert usage.quantity == 5.0
        assert usage.total_cost == Decimal("2.50")


class TestBillingIntegration:
    """Integration tests for billing functionality."""

    @pytest.mark.asyncio
    async def test_full_billing_flow(self, db_session, test_utils):
        """Test complete billing flow."""
        # 1. Create tenant
        tenant = await test_utils.create_test_tenant(db_session)
        
        # 2. Record video generation usage
        usage1 = await billing_service.record_video_generation_usage(
            db_session, tenant.id, "job-1", 2
        )
        
        # 3. Record storage usage
        usage2 = await billing_service.record_storage_usage(
            db_session, tenant.id, 1.5, "video-1"
        )
        
        # 4. Get usage summary
        summary = await billing_service.get_usage_summary(db_session, tenant.id)
        
        # 5. Get billing dashboard
        dashboard = await billing_service.get_billing_dashboard_data(db_session, tenant.id)
        
        # Verify results
        assert len(dashboard.current_usage) == 2
        assert summary.usage_by_type[UsageType.VIDEO_GENERATION.value] == 2.0
        assert summary.usage_by_type[UsageType.STORAGE_GB.value] == 1.5
        assert dashboard.tenant.id == tenant.id

    @pytest.mark.asyncio
    async def test_billing_limits_enforcement(self, db_session, test_utils):
        """Test billing limits are properly enforced."""
        # Create tenant with starter plan
        tenant = await test_utils.create_test_tenant(db_session, {
            "name": "Starter Tenant",
            "slug": "starter-tenant",
            "billing_plan": BillingPlan.STARTER
        })
        
        # Use up most of the limit
        await billing_service.record_video_generation_usage(
            db_session, tenant.id, "job-1", 9
        )
        
        # Check if we can generate one more
        result = await billing_service.check_usage_limits(
            db_session, tenant.id, UsageType.VIDEO_GENERATION, 1
        )
        assert result["can_proceed"] is True
        
        # Check if we can generate two more (should fail)
        result = await billing_service.check_usage_limits(
            db_session, tenant.id, UsageType.VIDEO_GENERATION, 2
        )
        assert result["can_proceed"] is False
