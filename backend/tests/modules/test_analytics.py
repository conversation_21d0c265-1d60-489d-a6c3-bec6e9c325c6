"""
Tests for analytics module.
"""

import pytest
from unittest.mock import Mock, patch
from datetime import datetime, timedelta

from modules.analytics.event_service import analytics_event_service
from modules.analytics.event_models import AnalyticsEvent, EventType, ConversionFunnel
from modules.analytics.event_schemas import (
    EventIngestionRequest, BatchEventIngestionRequest,
    VideoAnalyticsRequest, ConversionFunnelRequest
)


class TestAnalyticsEventService:
    """Test analytics event service functionality."""

    @pytest.mark.asyncio
    async def test_ingest_event(self, db_session, test_utils):
        """Test single event ingestion."""
        # Create tenant
        tenant = await test_utils.create_test_tenant(db_session)
        
        # Create event request
        event_request = EventIngestionRequest(
            event_type=EventType.VIDEO_PLAY,
            session_id="test-session-123",
            user_id="test-user-456",
            video_variant_id=1,
            product_id="product-123",
            duration=30.5,
            position=15.2,
            properties={"test": "data"}
        )
        
        # Ingest event
        response = await analytics_event_service.ingest_event(
            db_session, tenant.id, event_request, "192.168.1.1"
        )
        
        assert response.status == "success"
        assert response.event_id is not None

    @pytest.mark.asyncio
    async def test_ingest_duplicate_event(self, db_session, test_utils):
        """Test duplicate event handling."""
        # Create tenant
        tenant = await test_utils.create_test_tenant(db_session)
        
        # Create event request with specific dedup token
        event_request = EventIngestionRequest(
            event_type=EventType.VIDEO_PLAY,
            session_id="test-session-123",
            dedup_token="unique-token-123"
        )
        
        # Ingest event first time
        response1 = await analytics_event_service.ingest_event(
            db_session, tenant.id, event_request
        )
        assert response1.status == "success"
        
        # Ingest same event again
        response2 = await analytics_event_service.ingest_event(
            db_session, tenant.id, event_request
        )
        assert response2.status == "duplicate"

    @pytest.mark.asyncio
    async def test_ingest_batch_events(self, db_session, test_utils):
        """Test batch event ingestion."""
        # Create tenant
        tenant = await test_utils.create_test_tenant(db_session)
        
        # Create batch request
        events = [
            EventIngestionRequest(
                event_type=EventType.VIDEO_VIEW,
                session_id=f"session-{i}",
                product_id=f"product-{i}"
            )
            for i in range(5)
        ]
        
        batch_request = BatchEventIngestionRequest(
            tenant_id=tenant.id,
            events=events
        )
        
        # Ingest batch
        response = await analytics_event_service.ingest_batch_events(
            db_session, batch_request
        )
        
        assert response.total_events == 5
        assert response.successful == 5
        assert response.duplicates == 0
        assert response.errors == 0

    @pytest.mark.asyncio
    async def test_get_video_analytics(self, db_session, test_utils):
        """Test video analytics retrieval."""
        # Create tenant
        tenant = await test_utils.create_test_tenant(db_session)
        
        # Create some events
        events = [
            EventIngestionRequest(
                event_type=EventType.VIDEO_VIEW,
                session_id="session-1",
                product_id="product-123"
            ),
            EventIngestionRequest(
                event_type=EventType.VIDEO_PLAY,
                session_id="session-1",
                product_id="product-123"
            ),
            EventIngestionRequest(
                event_type=EventType.VIDEO_COMPLETE,
                session_id="session-1",
                product_id="product-123",
                duration=30.0
            ),
            EventIngestionRequest(
                event_type=EventType.CTA_CLICK,
                session_id="session-1",
                product_id="product-123"
            )
        ]
        
        # Ingest events
        for event in events:
            await analytics_event_service.ingest_event(db_session, tenant.id, event)
        
        # Get analytics
        request = VideoAnalyticsRequest(product_id="product-123")
        response = await analytics_event_service.get_video_analytics(
            db_session, tenant.id, request
        )
        
        assert response.metrics.views == 1
        assert response.metrics.plays == 1
        assert response.metrics.completions == 1
        assert response.metrics.cta_clicks == 1
        assert response.metrics.completion_rate == 100.0  # 1/1 * 100

    @pytest.mark.asyncio
    async def test_get_conversion_funnel(self, db_session, test_utils):
        """Test conversion funnel analysis."""
        # Create tenant
        tenant = await test_utils.create_test_tenant(db_session)
        
        # Create conversion events for same session
        session_id = "conversion-session-123"
        events = [
            EventIngestionRequest(
                event_type=EventType.VIDEO_VIEW,
                session_id=session_id,
                product_id="product-123"
            ),
            EventIngestionRequest(
                event_type=EventType.VIDEO_PLAY,
                session_id=session_id,
                product_id="product-123"
            ),
            EventIngestionRequest(
                event_type=EventType.CTA_CLICK,
                session_id=session_id,
                product_id="product-123"
            ),
            EventIngestionRequest(
                event_type=EventType.ADD_TO_CART,
                session_id=session_id,
                product_id="product-123"
            ),
            EventIngestionRequest(
                event_type=EventType.PURCHASE,
                session_id=session_id,
                product_id="product-123",
                order_id="order-123",
                order_value=99.99
            )
        ]
        
        # Ingest events
        for event in events:
            await analytics_event_service.ingest_event(db_session, tenant.id, event)
        
        # Get funnel analysis
        request = ConversionFunnelRequest(product_id="product-123")
        response = await analytics_event_service.get_conversion_funnel(
            db_session, tenant.id, request
        )
        
        assert response.total_sessions >= 1
        assert len(response.stages) == 6  # All funnel stages
        assert response.overall_conversion_rate > 0

    @pytest.mark.asyncio
    async def test_device_type_detection(self):
        """Test device type detection from user agent."""
        # Test mobile detection
        mobile_ua = "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)"
        device_type = analytics_event_service._detect_device_type(mobile_ua)
        assert device_type == "mobile"
        
        # Test tablet detection
        tablet_ua = "Mozilla/5.0 (iPad; CPU OS 14_0 like Mac OS X)"
        device_type = analytics_event_service._detect_device_type(tablet_ua)
        assert device_type == "tablet"
        
        # Test desktop detection
        desktop_ua = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        device_type = analytics_event_service._detect_device_type(desktop_ua)
        assert device_type == "desktop"

    @pytest.mark.asyncio
    async def test_conversion_funnel_update(self, db_session, test_utils):
        """Test conversion funnel update logic."""
        # Create tenant
        tenant = await test_utils.create_test_tenant(db_session)
        
        # Create conversion event
        event = AnalyticsEvent(
            event_id="test-event-123",
            event_type=EventType.PURCHASE.value,
            tenant_id=tenant.id,
            session_id="test-session",
            product_id="product-123",
            timestamp=datetime.utcnow(),
            order_id="order-123",
            order_value=99.99,
            is_conversion=True
        )
        
        # Update funnel
        await analytics_event_service._update_conversion_funnel(db_session, event)
        
        # Verify funnel was created/updated
        from sqlalchemy import select
        result = await db_session.execute(
            select(ConversionFunnel).where(
                ConversionFunnel.session_id == "test-session"
            )
        )
        funnel = result.scalar_one_or_none()
        
        assert funnel is not None
        assert funnel.purchase_event_id == "test-event-123"
        assert funnel.conversion_value == 99.99


class TestAnalyticsModels:
    """Test analytics models."""

    def test_analytics_event_model(self):
        """Test AnalyticsEvent model."""
        event = AnalyticsEvent(
            event_id="test-event-123",
            event_type=EventType.VIDEO_PLAY.value,
            tenant_id=1,
            session_id="session-123",
            product_id="product-123",
            timestamp=datetime.utcnow(),
            duration=30.5,
            properties={"test": "data"}
        )
        
        assert event.event_type == EventType.VIDEO_PLAY.value
        assert event.duration == 30.5
        assert event.properties["test"] == "data"

    def test_conversion_funnel_model(self):
        """Test ConversionFunnel model."""
        funnel = ConversionFunnel(
            tenant_id=1,
            session_id="session-123",
            product_id="product-123",
            funnel_start=datetime.utcnow(),
            video_view_event_id="view-event",
            purchase_event_id="purchase-event",
            conversion_value=99.99,
            time_to_conversion=3600  # 1 hour
        )
        
        assert funnel.session_id == "session-123"
        assert funnel.conversion_value == 99.99
        assert funnel.time_to_conversion == 3600


class TestAnalyticsIntegration:
    """Integration tests for analytics functionality."""

    @pytest.mark.asyncio
    async def test_full_analytics_flow(self, db_session, test_utils):
        """Test complete analytics flow."""
        # 1. Create tenant
        tenant = await test_utils.create_test_tenant(db_session)
        
        # 2. Simulate user journey
        session_id = "user-journey-123"
        product_id = "product-456"
        
        # Video view
        await analytics_event_service.ingest_event(
            db_session, tenant.id,
            EventIngestionRequest(
                event_type=EventType.VIDEO_VIEW,
                session_id=session_id,
                product_id=product_id
            )
        )
        
        # Video play
        await analytics_event_service.ingest_event(
            db_session, tenant.id,
            EventIngestionRequest(
                event_type=EventType.VIDEO_PLAY,
                session_id=session_id,
                product_id=product_id
            )
        )
        
        # Video complete
        await analytics_event_service.ingest_event(
            db_session, tenant.id,
            EventIngestionRequest(
                event_type=EventType.VIDEO_COMPLETE,
                session_id=session_id,
                product_id=product_id,
                duration=30.0
            )
        )
        
        # CTA click
        await analytics_event_service.ingest_event(
            db_session, tenant.id,
            EventIngestionRequest(
                event_type=EventType.CTA_CLICK,
                session_id=session_id,
                product_id=product_id
            )
        )
        
        # Purchase
        await analytics_event_service.ingest_event(
            db_session, tenant.id,
            EventIngestionRequest(
                event_type=EventType.PURCHASE,
                session_id=session_id,
                product_id=product_id,
                order_id="order-789",
                order_value=149.99
            )
        )
        
        # 3. Get analytics
        analytics_request = VideoAnalyticsRequest(product_id=product_id)
        analytics_response = await analytics_event_service.get_video_analytics(
            db_session, tenant.id, analytics_request
        )
        
        # 4. Get funnel analysis
        funnel_request = ConversionFunnelRequest(product_id=product_id)
        funnel_response = await analytics_event_service.get_conversion_funnel(
            db_session, tenant.id, funnel_request
        )
        
        # Verify results
        assert analytics_response.metrics.views == 1
        assert analytics_response.metrics.plays == 1
        assert analytics_response.metrics.completions == 1
        assert analytics_response.metrics.cta_clicks == 1
        assert analytics_response.metrics.purchases == 1
        assert analytics_response.metrics.total_revenue == 149.99
        
        assert funnel_response.total_sessions >= 1
        assert funnel_response.overall_conversion_rate > 0

    @pytest.mark.asyncio
    async def test_analytics_with_multiple_sessions(self, db_session, test_utils):
        """Test analytics with multiple user sessions."""
        # Create tenant
        tenant = await test_utils.create_test_tenant(db_session)
        product_id = "product-multi-session"
        
        # Create events for multiple sessions
        for session_num in range(3):
            session_id = f"session-{session_num}"
            
            # Each session has view and play
            await analytics_event_service.ingest_event(
                db_session, tenant.id,
                EventIngestionRequest(
                    event_type=EventType.VIDEO_VIEW,
                    session_id=session_id,
                    product_id=product_id
                )
            )
            
            await analytics_event_service.ingest_event(
                db_session, tenant.id,
                EventIngestionRequest(
                    event_type=EventType.VIDEO_PLAY,
                    session_id=session_id,
                    product_id=product_id
                )
            )
            
            # Only first session converts
            if session_num == 0:
                await analytics_event_service.ingest_event(
                    db_session, tenant.id,
                    EventIngestionRequest(
                        event_type=EventType.PURCHASE,
                        session_id=session_id,
                        product_id=product_id,
                        order_id=f"order-{session_num}",
                        order_value=99.99
                    )
                )
        
        # Get analytics
        analytics_request = VideoAnalyticsRequest(product_id=product_id)
        response = await analytics_event_service.get_video_analytics(
            db_session, tenant.id, analytics_request
        )
        
        # Verify metrics
        assert response.metrics.views == 3
        assert response.metrics.plays == 3
        assert response.metrics.purchases == 1
        assert response.metrics.conversion_rate == 33.33  # 1/3 * 100
