"""
Integration tests for Shopify sync system.

Tests end-to-end workflows including webhook processing, sync orchestration,
and data consumption.
"""

import json
import time
import pytest
from datetime import datetime, timezone
from unittest.mock import patch, MagicMock

from sqlalchemy.orm import Session
from fastapi.testclient import TestClient

from src.main import app
from src.modules.stores.models import Store
from src.modules.sync.models import WebhookEvent, SyncJob, Sync<PERSON>heckpoint, StagingProduct
from src.modules.products.models import Product
from servers.worker.tasks import process_webhook_event, trigger_airbyte_sync, consumer_upsert
from src.core.services.airbyte_service import AirbyteService


@pytest.fixture
def client():
    """Test client for FastAPI app."""
    return TestClient(app)


@pytest.fixture
def test_store(db_session):
    """Create test store."""
    store = Store(
        shop_domain="test-shop.myshopify.com",
        shop_name="Test Shop",
        access_token="test-token",
        is_active=True,
        airbyte_source_id="test-source-id",
        airbyte_destination_id="test-destination-id",
        airbyte_connection_id="test-connection-id"
    )
    db_session.add(store)
    db_session.commit()
    db_session.refresh(store)
    return store


@pytest.fixture
def sample_product_data():
    """Sample product data for testing."""
    return {
        "id": "123456789",
        "title": "Integration Test Product",
        "body_html": "<p>Test product description</p>",
        "vendor": "Test Vendor",
        "product_type": "Test Type",
        "handle": "integration-test-product",
        "status": "active",
        "created_at": "2025-09-06T12:00:00Z",
        "updated_at": "2025-09-06T12:00:00Z",
        "admin_graphql_api_id": "gid://shopify/Product/123456789"
    }


class TestWebhookToSyncFlow:
    """Test webhook processing to sync job creation."""
    
    @patch('servers.worker.tasks.trigger_airbyte_sync.delay')
    def test_webhook_triggers_sync(self, mock_trigger_sync, db_session, test_store):
        """Test that webhook event triggers sync job."""
        # Create webhook event
        webhook_event = WebhookEvent(
            event_id="test-event-123",
            topic="products/update",
            shop_domain=test_store.shop_domain,
            store_id=test_store.id,
            payload={"id": 123456789, "title": "Test Product"},
            hmac_verified=True,
            status="pending"
        )
        db_session.add(webhook_event)
        db_session.commit()
        
        # Process webhook event
        process_webhook_event(webhook_event.id)
        
        # Verify sync was triggered
        mock_trigger_sync.assert_called_once()
        args = mock_trigger_sync.call_args[0]
        assert args[0] == test_store.id  # store_id
        assert args[1] == 'products'     # entity_type
        assert args[2] == 'webhook'      # triggered_by
        
        # Verify webhook event status
        db_session.refresh(webhook_event)
        assert webhook_event.status == 'completed'
        assert webhook_event.completed_at is not None


class TestAirbyteSyncFlow:
    """Test Airbyte sync job management."""
    
    @patch('requests.post')
    @patch('servers.worker.tasks.monitor_airbyte_job.delay')
    def test_trigger_airbyte_sync_success(self, mock_monitor, mock_post, db_session, test_store):
        """Test successful Airbyte sync trigger."""
        # Mock Airbyte API response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"job": {"id": 12345}}
        mock_post.return_value = mock_response
        
        # Trigger sync
        trigger_airbyte_sync(test_store.id, 'products', 'webhook')
        
        # Verify sync job was created
        sync_job = db_session.query(SyncJob).filter(
            SyncJob.store_id == test_store.id,
            SyncJob.entity_type == 'products'
        ).first()
        
        assert sync_job is not None
        assert sync_job.status == 'running'
        assert sync_job.airbyte_job_id == 12345
        assert sync_job.triggered_by == 'webhook'
        
        # Verify monitoring was started
        mock_monitor.assert_called_once_with(sync_job.id)
    
    @patch('requests.post')
    def test_trigger_airbyte_sync_rate_limited(self, mock_post, db_session, test_store):
        """Test Airbyte sync with rate limiting."""
        # Mock rate limited response
        mock_response = MagicMock()
        mock_response.status_code = 429
        mock_post.return_value = mock_response
        
        # Trigger sync should handle rate limiting
        with pytest.raises(Exception):  # Should retry
            trigger_airbyte_sync(test_store.id, 'products', 'webhook')
        
        # Verify sync job was created but failed
        sync_job = db_session.query(SyncJob).filter(
            SyncJob.store_id == test_store.id,
            SyncJob.entity_type == 'products'
        ).first()
        
        assert sync_job is not None
        assert sync_job.status == 'failed'
        assert 'Rate limited' in sync_job.error_message


class TestConsumerUpsertFlow:
    """Test consumer processing and upserts."""
    
    def test_process_staging_products(self, db_session, test_store, sample_product_data):
        """Test processing staging products to production tables."""
        # Create staging product
        staging_product = StagingProduct(
            _airbyte_ab_id="test-ab-id-123",
            _airbyte_emitted_at=datetime.now(timezone.utc),
            id=sample_product_data["id"],
            title=sample_product_data["title"],
            body_html=sample_product_data["body_html"],
            vendor=sample_product_data["vendor"],
            product_type=sample_product_data["product_type"],
            handle=sample_product_data["handle"],
            status=sample_product_data["status"],
            created_at=datetime.fromisoformat(sample_product_data["created_at"].replace('Z', '+00:00')),
            updated_at=datetime.fromisoformat(sample_product_data["updated_at"].replace('Z', '+00:00')),
            admin_graphql_api_id=sample_product_data["admin_graphql_api_id"],
            shop_id=test_store.id,
            processed=False
        )
        db_session.add(staging_product)
        db_session.commit()
        
        # Process staging data
        consumer_upsert(test_store.id, 'products', batch_size=10)
        
        # Verify product was created
        product = db_session.query(Product).filter(
            Product.external_id == sample_product_data["id"],
            Product.store_id == test_store.id
        ).first()
        
        assert product is not None
        assert product.title == sample_product_data["title"]
        assert product.vendor == sample_product_data["vendor"]
        assert product.handle == sample_product_data["handle"]
        
        # Verify staging record was marked as processed
        db_session.refresh(staging_product)
        assert staging_product.processed is True
        assert staging_product.processed_at is not None
        
        # Verify checkpoint was updated
        checkpoint = db_session.query(SyncCheckpoint).filter(
            SyncCheckpoint.store_id == test_store.id,
            SyncCheckpoint.entity_type == 'products'
        ).first()
        
        assert checkpoint is not None
        assert checkpoint.total_records == 1
        assert checkpoint.last_sync_status == 'completed'
    
    def test_upsert_conflict_resolution(self, db_session, test_store, sample_product_data):
        """Test conflict resolution during upserts."""
        # Create existing product
        existing_product = Product(
            store_id=test_store.id,
            external_id=sample_product_data["id"],
            title="Old Title",
            vendor="Old Vendor",
            handle="old-handle",
            created_at=datetime(2025, 9, 5, tzinfo=timezone.utc),
            updated_at=datetime(2025, 9, 5, tzinfo=timezone.utc)
        )
        db_session.add(existing_product)
        db_session.commit()
        
        # Create staging product with newer data
        staging_product = StagingProduct(
            _airbyte_ab_id="test-ab-id-456",
            _airbyte_emitted_at=datetime.now(timezone.utc),
            id=sample_product_data["id"],
            title=sample_product_data["title"],
            vendor=sample_product_data["vendor"],
            handle=sample_product_data["handle"],
            updated_at=datetime(2025, 9, 6, tzinfo=timezone.utc),  # Newer
            shop_id=test_store.id,
            processed=False
        )
        db_session.add(staging_product)
        db_session.commit()
        
        # Process staging data
        consumer_upsert(test_store.id, 'products', batch_size=10)
        
        # Verify product was updated (newer data wins)
        db_session.refresh(existing_product)
        assert existing_product.title == sample_product_data["title"]
        assert existing_product.vendor == sample_product_data["vendor"]
        assert existing_product.handle == sample_product_data["handle"]


class TestCrashRecoveryFlow:
    """Test crash recovery and resumption."""
    
    def test_consumer_crash_recovery(self, db_session, test_store):
        """Test consumer recovery after crash mid-processing."""
        # Create multiple staging products
        staging_products = []
        for i in range(5):
            staging_product = StagingProduct(
                _airbyte_ab_id=f"test-ab-id-{i}",
                _airbyte_emitted_at=datetime.now(timezone.utc),
                id=f"12345678{i}",
                title=f"Test Product {i}",
                handle=f"test-product-{i}",
                updated_at=datetime.now(timezone.utc),
                shop_id=test_store.id,
                processed=False
            )
            staging_products.append(staging_product)
            db_session.add(staging_product)
        
        db_session.commit()
        
        # Simulate partial processing (mark first 2 as processed)
        staging_products[0].processed = True
        staging_products[1].processed = True
        db_session.commit()
        
        # Process remaining (simulating recovery)
        consumer_upsert(test_store.id, 'products', batch_size=10)
        
        # Verify all products were processed
        processed_count = db_session.query(StagingProduct).filter(
            StagingProduct.shop_id == test_store.id,
            StagingProduct.processed == True
        ).count()
        
        assert processed_count == 5
        
        # Verify all products exist in production table
        product_count = db_session.query(Product).filter(
            Product.store_id == test_store.id
        ).count()
        
        assert product_count == 3  # Only 3 new ones (2 were already processed)


class TestAirbyteManagerIntegration:
    """Test Airbyte manager integration."""
    
    @patch('requests.post')
    def test_setup_shop_sync(self, mock_post, db_session, test_store):
        """Test complete shop sync setup."""
        # Mock Airbyte API responses
        mock_responses = [
            # Create source
            MagicMock(status_code=200, json=lambda: {"sourceId": "source-123"}),
            # Create destination  
            MagicMock(status_code=200, json=lambda: {"destinationId": "dest-123"}),
            # Discover schema
            MagicMock(status_code=200, json=lambda: {"catalog": {"streams": []}}),
            # Create connection
            MagicMock(status_code=200, json=lambda: {"connectionId": "conn-123"})
        ]
        mock_post.side_effect = mock_responses
        
        # Setup sync
        airbyte_service = AirbyteService()
        success = airbyte_service.setup_shop_sync(
            db_session,
            test_store,
            "test-access-token",
            "2025-09-01"
        )
        
        assert success is True
        
        # Verify store was updated
        db_session.refresh(test_store)
        assert test_store.airbyte_source_id == "source-123"
        assert test_store.airbyte_destination_id == "dest-123"
        assert test_store.airbyte_connection_id == "conn-123"


class TestExponentialBackoffAndDLQ:
    """Test exponential backoff and dead letter queue."""
    
    @patch('requests.post')
    def test_repeated_failures_to_dlq(self, mock_post, db_session, test_store):
        """Test that repeated failures move items to DLQ."""
        # Mock repeated failures
        mock_response = MagicMock()
        mock_response.status_code = 500
        mock_response.raise_for_status.side_effect = Exception("API Error")
        mock_post.return_value = mock_response
        
        # Create webhook event
        webhook_event = WebhookEvent(
            event_id="test-event-dlq",
            topic="products/update",
            shop_domain=test_store.shop_domain,
            store_id=test_store.id,
            payload={"id": 123456789},
            hmac_verified=True,
            status="pending",
            max_retries=2  # Low retry count for testing
        )
        db_session.add(webhook_event)
        db_session.commit()
        
        # Process webhook event multiple times (should fail and retry)
        for _ in range(3):  # Exceed max retries
            try:
                process_webhook_event(webhook_event.id)
            except:
                pass
        
        # Verify webhook event failed
        db_session.refresh(webhook_event)
        assert webhook_event.status == 'failed'
        assert webhook_event.retry_count >= webhook_event.max_retries
        
        # Verify item was moved to DLQ
        from src.modules.sync.models import DeadLetterQueue
        dlq_item = db_session.query(DeadLetterQueue).filter(
            DeadLetterQueue.source_type == 'webhook',
            DeadLetterQueue.source_id == str(webhook_event.id)
        ).first()
        
        assert dlq_item is not None
        assert dlq_item.failure_reason == 'max_retries_exceeded'
        assert dlq_item.resolved is False
