# Use a slim, modern Python base image
FROM python:3.12-slim

# Set the working directory
WORKDIR /app

# Install system dependencies including C++ compiler for forecasting libraries
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libpq-dev \
    curl \
    build-essential \
    ffmpeg \
    && rm -rf /var/lib/apt/lists/*

COPY pyproject.toml ./

# Copy application code
COPY . .

# Set PYTHONPATH to prioritize local modules
ENV PYTHONPATH=/app/src:$PYTHONPATH

# Prevent Python from writing bytecode to disk
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

RUN pip install --no-cache-dir --upgrade pip &&   pip install --no-cache-dir .

# Create non-root user
# RUN useradd --create-home --shell /bin/bash app \
#     && chown -R app:app /app
# USER app

# Expose port
EXPOSE 8123

# Run the application
CMD ["uvicorn", "src.servers.api.main:app", "--host", "0.0.0.0", "--port", "8123"]