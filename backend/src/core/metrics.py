"""
Prometheus metrics for Shopify sync system.
"""

from prometheus_client import Counter, Histogram, Gauge

# Webhook metrics
webhook_requests_total = Counter(
    'webhook_requests_total',
    'Total number of webhook requests received',
    ['shop_domain', 'topic', 'status']
)

webhook_processing_duration = Histogram(
    'webhook_processing_duration_seconds',
    'Time spent processing webhook requests',
    ['shop_domain', 'topic']
)

webhook_hmac_failures = Counter(
    'webhook_hmac_failures_total',
    'Number of HMAC validation failures',
    ['shop_domain']
)

webhook_dedup_hits = Counter(
    'webhook_dedup_hits_total',
    'Number of duplicate webhooks detected',
    ['shop_domain', 'topic']
)

# Sync job metrics
sync_jobs_total = Counter(
    'sync_jobs_total',
    'Total number of sync jobs',
    ['store_domain', 'entity_type', 'status']
)

sync_duration = Histogram(
    'sync_duration_seconds',
    'Time spent on sync operations',
    ['store_domain', 'entity_type']
)

sync_job_failures = Counter(
    'sync_job_failures_total',
    'Number of sync job failures',
    ['store_domain', 'entity_type', 'failure_reason']
)

active_sync_jobs = Gauge(
    'active_sync_jobs',
    'Number of currently active sync jobs',
    ['store_domain']
)

# Consumer metrics
consumer_records_processed = Counter(
    'consumer_records_processed_total',
    'Total number of records processed by consumer',
    ['store_domain', 'entity_type']
)

consumer_processing_duration = Histogram(
    'consumer_processing_duration_seconds',
    'Time spent processing consumer batches',
    ['store_domain', 'entity_type']
)

staging_records_pending = Gauge(
    'staging_records_pending',
    'Number of unprocessed records in staging tables',
    ['store_domain', 'entity_type']
)

# Airbyte API metrics
airbyte_api_requests = Counter(
    'airbyte_api_requests_total',
    'Total Airbyte API requests',
    ['endpoint', 'status']
)

airbyte_api_duration = Histogram(
    'airbyte_api_duration_seconds',
    'Airbyte API request duration',
    ['endpoint']
)

# Dead letter queue metrics
dlq_items_total = Counter(
    'dlq_items_total',
    'Total items in dead letter queue',
    ['source_type', 'failure_reason']
)

dlq_items_pending = Gauge(
    'dlq_items_pending',
    'Number of unresolved items in dead letter queue',
    ['source_type']
)

# Media generation metrics
media_generation_duration = Histogram(
    'media_generation_duration_seconds',
    'Time spent on media generation operations',
    ['media_type', 'status']
)

media_generation_failures = Counter(
    'media_generation_failures_total',
    'Number of media generation failures',
    ['media_type', 'failure_reason']
)

# Media push metrics
media_push_duration = Histogram(
    'media_push_duration_seconds',
    'Time spent on media push operations',
    ['platform', 'status']
)

media_push_failures = Counter(
    'media_push_failures_total',
    'Number of media push failures',
    ['platform', 'failure_reason']
)

# Analytics processing metrics
analytics_processing_duration = Histogram(
    'analytics_processing_duration_seconds',
    'Time spent on analytics processing operations',
    []
)

analytics_processing_failures = Counter(
    'analytics_processing_failures_total',
    'Number of analytics processing failures',
    ['failure_reason']
)

# Sync process metrics (for direct Airbyte syncs)
sync_triggered_total = Counter(
    'sync_triggered_total',
    'Total number of sync operations triggered',
    ['trigger_type', 'entity_type', 'store_domain']
)

sync_completed_total = Counter(
    'sync_completed_total',
    'Total number of sync operations completed',
    ['trigger_type', 'entity_type', 'store_domain', 'status']
)

sync_duration_seconds_triggered = Histogram(
    'sync_duration_seconds_triggered',
    'Time spent on sync operations',
    ['trigger_type', 'entity_type', 'store_domain']
)

sync_progress_updates = Counter(
    'sync_progress_updates_total',
    'Number of sync progress updates',
    ['entity_type', 'store_domain', 'stage']
)

airbyte_sync_jobs_created = Counter(
    'airbyte_sync_jobs_created_total',
    'Number of Airbyte sync jobs created',
    ['store_domain', 'connection_id']
)

airbyte_sync_jobs_completed = Counter(
    'airbyte_sync_jobs_completed_total',
    'Number of Airbyte sync jobs completed',
    ['store_domain', 'connection_id', 'status']
)

webhook_sync_triggers = Counter(
    'webhook_sync_triggers_total',
    'Number of syncs triggered by webhooks',
    ['shop_domain', 'topic', 'entity_type']
)

direct_sync_api_calls = Counter(
    'direct_sync_api_calls_total',
    'Number of direct sync API calls',
    ['store_domain', 'mode', 'status']
)

sync_checkpoint_updates = Counter(
    'sync_checkpoint_updates_total',
    'Number of sync checkpoint updates',
    ['store_domain', 'entity_type', 'stage']
)

# Queue and worker metrics
celery_active_tasks = Gauge(
    'celery_active_tasks',
    'Number of currently active Celery tasks',
    ['queue', 'worker']
)

celery_scheduled_tasks = Gauge(
    'celery_scheduled_tasks',
    'Number of scheduled Celery tasks',
    ['queue']
)

celery_worker_status = Gauge(
    'celery_worker_status',
    'Celery worker status (1=active, 0=inactive)',
    ['worker', 'status']
)

celery_task_processing_duration = Histogram(
    'celery_task_processing_duration_seconds',
    'Time spent processing Celery tasks',
    ['queue', 'task_name']
)

celery_task_failures = Counter(
    'celery_task_failures_total',
    'Number of Celery task failures',
    ['queue', 'task_name', 'failure_reason']
)

celery_queue_length = Gauge(
    'celery_queue_length',
    'Current length of Celery queues',
    ['queue']
)

celery_worker_pool_size = Gauge(
    'celery_worker_pool_size',
    'Size of Celery worker pools',
    ['worker', 'pool_type']
)

celery_task_rate = Counter(
    'celery_task_rate_total',
    'Rate of Celery task processing',
    ['queue', 'task_name', 'status']
)
