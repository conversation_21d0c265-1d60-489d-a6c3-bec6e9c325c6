{"airbyte": {"api_url": "http://host.docker.internal:8000", "user_id": "<EMAIL>", "password": "HCdZzsBgyzZjOGYtvnlulDVFIqBS7cuQ", "workspace_id": "984ea807-5d26-438b-ab1c-6abd3f0fc829", "source_definition_name": "Shopify", "destination_definition_name": "Postgres", "database": {"host": "host.docker.internal", "port": 5432, "user": "airbyte", "password": "airbyte", "name": "airbyte", "schema": "public", "ssl": false}, "timeouts": {"default": 30, "discovery": 60, "creation": 60}}, "shopify_source": {"start_date": "2020-01-01", "bulk_window_in_days": 30, "job_termination_threshold": 7200, "job_checkpoint_interval": 100000, "job_heartbeat_interval": 10800}, "sync_streams": ["products", "product_variants", "product_images", "inventory_levels", "metafield_products", "metafield_product_variants", "metafield_product_images"], "stream_config": {"products": {"sync_mode": "incremental", "destination_sync_mode": "append_dedup", "cursor_field": ["updated_at"], "primary_key": [["id"]]}, "product_variants": {"sync_mode": "incremental", "destination_sync_mode": "append_dedup", "cursor_field": ["updated_at"], "primary_key": [["id"]]}, "product_images": {"sync_mode": "incremental", "destination_sync_mode": "append_dedup", "cursor_field": ["updated_at"], "primary_key": [["id"]]}, "inventory_levels": {"sync_mode": "incremental", "destination_sync_mode": "append_dedup", "cursor_field": ["updated_at"], "primary_key": [["inventory_item_id"], ["location_id"]]}, "metafield_products": {"sync_mode": "incremental", "destination_sync_mode": "append_dedup", "cursor_field": ["updated_at"], "primary_key": [["owner_id"], ["key"], ["namespace"]]}, "metafield_product_variants": {"sync_mode": "incremental", "destination_sync_mode": "append_dedup", "cursor_field": ["updated_at"], "primary_key": [["owner_id"], ["key"], ["namespace"]]}, "metafield_product_images": {"sync_mode": "incremental", "destination_sync_mode": "append_dedup", "cursor_field": ["updated_at"], "primary_key": [["owner_id"], ["key"], ["namespace"]]}}, "connection": {"schedule": {"time_unit": "hours", "units": 24}, "resource_requirements": {"cpu_request": "0.25", "cpu_limit": "0.5", "memory_request": "512Mi", "memory_limit": "1Gi"}}}