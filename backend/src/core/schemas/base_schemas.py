from datetime import datetime
from typing import Optional

from pydantic import BaseModel, ConfigDict


class BaseSchema(BaseModel):
    """Base schema with common configuration."""
    
    model_config = ConfigDict(
        from_attributes=True,
        validate_assignment=True,
        arbitrary_types_allowed=True,
    )


class TimestampMixin(BaseModel):
    """Mixin for models with timestamp fields."""
    
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


class BaseCreateSchema(BaseSchema):
    """Base schema for create operations."""
    pass


class BaseUpdateSchema(BaseSchema):
    """Base schema for update operations."""
    pass


class BaseResponseSchema(BaseSchema, TimestampMixin):
    """Base schema for response operations."""
    
    id: int


class PaginationParams(BaseModel):
    """Standard pagination parameters."""
    
    skip: int = 0
    limit: int = 100


class PaginatedResponse(BaseModel):
    """Standard paginated response."""
    
    items: list
    total: int
    skip: int
    limit: int
    has_next: bool
    has_prev: bool
