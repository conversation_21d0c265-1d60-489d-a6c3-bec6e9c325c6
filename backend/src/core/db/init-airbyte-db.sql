-- Create Airbyte database and user
CREATE DATABASE airbyte;
CREATE USER airbyte WITH ENCRYPTED PASSWORD 'airbyte';
GRANT ALL PRIVILEGES ON DATABASE airbyte TO airbyte;

-- Give schema privileges
\c airbyte app_user
GRANT USAGE, CREATE ON SCHEMA public TO airbyte;

-- Create Temporal database and user
CREATE DATABASE temporal;
CREATE USER temporal WITH ENCRYPTED PASSWORD 'temporal';
GRANT ALL PRIVILEGES ON DATABASE temporal TO temporal;
\c temporal app_user
GRANT USAGE, CREATE ON SCHEMA public TO temporal;