"""
Templates Module Sc<PERSON>
"""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel

from core.schemas.base_schemas import BaseSchema


class MediaTemplateResponse(BaseModel):
    """Template information."""

    id: str
    name: str
    description: Optional[str] = None
    duration_target: Optional[int] = None
    preview_url: Optional[str] = None
    category: Optional[str] = None


class MediaTemplateListResponse(BaseSchema):
    """Response for listing media templates."""
    templates: List[Dict[str, Any]]
    total: int
    page: int
    per_page: int