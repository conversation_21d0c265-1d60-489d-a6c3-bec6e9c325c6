from sqlalchemy import Column, Integer, String, Float, Text, DateTime, ForeignKey, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from core.db.database import Base


class Order(Base):
    """Order model for storing orders with full JSON data."""

    __tablename__ = "orders"

    id = Column(Integer, primary_key=True, index=True)
    external_id = Column(String, unique=True, nullable=False)
    store_id = Column(Integer, ForeignKey("stores.id"), nullable=False)
    customer_id = Column(Integer, ForeignKey("customers.id"), nullable=True)

    # Order details
    order_number = Column(String, nullable=True)
    name = Column(String, nullable=True)
    email = Column(String, nullable=True)
    total_price = Column(Float, default=0.0)
    subtotal_price = Column(Float, default=0.0)
    total_tax = Column(Float, default=0.0)
    total_discounts = Column(Float, default=0.0)
    currency = Column(String, default="USD")

    # Status fields
    financial_status = Column(String, nullable=True)
    fulfillment_status = Column(String, nullable=True)

    # Full JSON data for more specific items
    full_json = Column(Text, nullable=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    store = relationship("modules.stores.models.Store", back_populates="orders")
    customer = relationship("modules.customers.models.Customer", back_populates="orders")


class OrderLineItem(Base):
    """Order line item model."""

    __tablename__ = "order_line_items"

    id = Column(Integer, primary_key=True, index=True)
    order_id = Column(Integer, ForeignKey("orders.id"), nullable=False)
    external_id = Column(String, nullable=True)

    # Product details
    product_id = Column(String, nullable=True)
    variant_id = Column(String, nullable=True)
    title = Column(String, nullable=True)
    sku = Column(String, nullable=True)
    quantity = Column(Integer, default=0)
    price = Column(Float, default=0.0)
    total_price = Column(Float, default=0.0)

    # Relationships
    order = relationship("Order", back_populates="line_items")

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())


# Add relationships to Order model
Order.line_items = relationship("OrderLineItem", back_populates="order")