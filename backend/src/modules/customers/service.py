from typing import List, Optional
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from core.services.base_service import BaseService
from modules.customers.models import Customer, CustomerAddress
from modules.customers.schemas import CustomerCreate, CustomerUpdate


class CustomerService(BaseService[Customer, CustomerCreate, CustomerUpdate]):
    """Service for customer operations."""

    def __init__(self):
        super().__init__(Customer)

    async def get_by_store(self, db: AsyncSession, store_id: int) -> List[Customer]:
        """Get all customers for a specific store."""
        result = await db.execute(
            select(Customer).filter(Customer.store_id == store_id)
        )
        return result.scalars().all()

    async def get_by_external_id(self, db: AsyncSession, external_id: str) -> Optional[Customer]:
        """Get customer by external ID."""
        result = await db.execute(
            select(Customer).filter(Customer.external_id == external_id)
        )
        return result.scalar_one_or_none()

    async def get_by_email(self, db: AsyncSession, email: str, store_id: int) -> Optional[Customer]:
        """Get customer by email for a specific store."""
        result = await db.execute(
            select(Customer).filter(
                Customer.email == email,
                Customer.store_id == store_id
            )
        )
        return result.scalar_one_or_none()

    async def create_customer_with_addresses(
        self,
        db: AsyncSession,
        customer_create: CustomerCreate
    ) -> Customer:
        """Create a customer with their addresses."""
        # Create the customer
        customer_data = customer_create.model_dump(exclude={'addresses'})
        db_customer = Customer(**customer_data)
        db.add(db_customer)
        await db.flush()  # Get the customer ID

        # Create addresses if provided
        if customer_create.addresses:
            for address_data in customer_create.addresses:
                address_dict = address_data.model_dump()
                address_dict['customer_id'] = db_customer.id
                db_address = CustomerAddress(**address_dict)
                db.add(db_address)

        await db.commit()
        await db.refresh(db_customer)
        return db_customer

    async def get_customer_with_addresses(
        self,
        db: AsyncSession,
        customer_id: int
    ) -> Optional[Customer]:
        """Get customer with their addresses."""
        result = await db.execute(
            select(Customer).filter(Customer.id == customer_id)
        )
        customer = result.scalar_one_or_none()

        if customer:
            # Load addresses
            addresses_result = await db.execute(
                select(CustomerAddress).filter(CustomerAddress.customer_id == customer_id)
            )
            customer.addresses = addresses_result.scalars().all()

        return customer


# Create service instance
customer_service = CustomerService()