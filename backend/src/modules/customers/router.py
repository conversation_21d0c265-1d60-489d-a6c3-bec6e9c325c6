"""
Customers API Router
"""

import logging
from typing import List

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession

from core.db.database import get_db
from modules.auth.router import get_current_user
from modules.auth.models import User
from modules.customers.models import Customer
from modules.customers.schemas import (
    CustomerCreate,
    CustomerResponse,
    CustomerUpdate
)
from modules.customers.service import customer_service

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/", response_model=List[CustomerResponse])
async def get_customers(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get all customers for the current user's stores."""
    try:
        # Get user's stores
        from modules.stores.models import Store
        from sqlalchemy import select

        stores_result = await db.execute(
            select(Store).filter(Store.owner_id == current_user.id)
        )
        user_stores = stores_result.scalars().all()

        if not user_stores:
            return []

        store_ids = [store.id for store in user_stores]

        # Get customers for all user's stores
        all_customers = []
        for store_id in store_ids:
            store_customers = await customer_service.get_by_store(db, store_id)
            all_customers.extend(store_customers)

        return all_customers

    except Exception as e:
        logger.error(f"Error getting customers: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/", response_model=CustomerResponse)
async def create_customer(
    customer: CustomerCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Create a new customer."""
    try:
        # Validate store ownership
        from modules.stores.models import Store
        from sqlalchemy import select

        store_result = await db.execute(
            select(Store).filter(
                Store.id == customer.store_id,
                Store.owner_id == current_user.id
            )
        )
        store = store_result.scalar_one_or_none()

        if not store:
            raise HTTPException(
                status_code=403,
                detail="Access denied: store does not belong to user"
            )

        db_customer = await customer_service.create_customer_with_addresses(db, customer)
        return db_customer

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating customer: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{customer_id}", response_model=CustomerResponse)
async def get_customer(
    customer_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get a specific customer."""
    try:
        customer = await customer_service.get_customer_with_addresses(db, customer_id)

        if not customer:
            raise HTTPException(status_code=404, detail="Customer not found")

        # Check store ownership
        from modules.stores.models import Store
        from sqlalchemy import select

        store_result = await db.execute(
            select(Store).filter(
                Store.id == customer.store_id,
                Store.owner_id == current_user.id
            )
        )
        store = store_result.scalar_one_or_none()

        if not store:
            raise HTTPException(
                status_code=403,
                detail="Access denied: customer does not belong to user's store"
            )

        return customer

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting customer: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/{customer_id}", response_model=CustomerResponse)
async def update_customer(
    customer_id: int,
    customer_update: CustomerUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Update a customer."""
    try:
        # Get existing customer
        customer = await customer_service.get(db, customer_id)
        if not customer:
            raise HTTPException(status_code=404, detail="Customer not found")

        # Check store ownership
        from modules.stores.models import Store
        from sqlalchemy import select

        store_result = await db.execute(
            select(Store).filter(
                Store.id == customer.store_id,
                Store.owner_id == current_user.id
            )
        )
        store = store_result.scalar_one_or_none()

        if not store:
            raise HTTPException(
                status_code=403,
                detail="Access denied: customer does not belong to user's store"
            )

        updated_customer = await customer_service.update(db, db_obj=customer, obj_in=customer_update)
        return updated_customer

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating customer: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{customer_id}")
async def delete_customer(
    customer_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Delete a customer."""
    try:
        # Get existing customer
        customer = await customer_service.get(db, customer_id)
        if not customer:
            raise HTTPException(status_code=404, detail="Customer not found")

        # Check store ownership
        from modules.stores.models import Store
        from sqlalchemy import select

        store_result = await db.execute(
            select(Store).filter(
                Store.id == customer.store_id,
                Store.owner_id == current_user.id
            )
        )
        store = store_result.scalar_one_or_none()

        if not store:
            raise HTTPException(
                status_code=403,
                detail="Access denied: customer does not belong to user's store"
            )

        await customer_service.remove(db, id=customer_id)
        return {"message": "Customer deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting customer: {e}")
        raise HTTPException(status_code=500, detail=str(e))