from sqlalchemy import Column, Integer, String, Float, Text, DateTime, ForeignKey, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from core.db.database import Base


class Customer(Base):
    """Customer model for e-commerce customers."""

    __tablename__ = "customers"

    id = Column(Integer, primary_key=True, index=True)
    external_id = Column(String, unique=True, nullable=False)
    email = Column(String, unique=True, nullable=False)
    first_name = Column(String)
    last_name = Column(String)
    phone = Column(String)
    accepts_marketing = Column(Boolean, default=False)
    tax_exempt = Column(Boolean, default=False)
    verified_email = Column(Boolean, default=False)

    # Address information
    address1 = Column(String)
    address2 = Column(String)
    city = Column(String)
    province = Column(String)
    province_code = Column(String)
    zip = Column(String)
    country = Column(String)
    country_code = Column(String)

    # Store relationship
    store_id = Column(Integer, ForeignKey("stores.id"))
    store = relationship("modules.stores.models.Store", back_populates="customers")

    # Orders relationship
    orders = relationship("modules.orders.models.Order", back_populates="customer")

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Additional fields
    tags = Column(String)  # JSON string of tags
    note = Column(Text)
    total_spent = Column(Float, default=0.0)
    orders_count = Column(Integer, default=0)
    state = Column(String, default="enabled")  # enabled, disabled, invited, declined


class CustomerAddress(Base):
    """Customer address model."""

    __tablename__ = "customer_addresses"

    id = Column(Integer, primary_key=True, index=True)
    customer_id = Column(Integer, ForeignKey("customers.id"))
    external_id = Column(String)

    first_name = Column(String)
    last_name = Column(String)
    company = Column(String)
    address1 = Column(String)
    address2 = Column(String)
    city = Column(String)
    province = Column(String)
    province_code = Column(String)
    zip = Column(String)
    country = Column(String)
    country_code = Column(String)
    phone = Column(String)

    # Relationships
    customer = relationship("Customer", back_populates="addresses")

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())


# Add relationships to Customer model
Customer.addresses = relationship("CustomerAddress", back_populates="customer")