from datetime import datetime
from typing import Optional, List, Any
from pydantic import BaseModel

from core.schemas.base_schemas import BaseCreateSchema, BaseResponseSchema, BaseUpdateSchema


class AssetBase(BaseModel):
    """Base asset schema - matches the actual Asset model."""

    type: str  # image, video, etc.
    product_id: int
    product_external_id: Optional[str] = None
    store_id: int
    src: str
    alt: Optional[str] = None
    external_id: Optional[str] = None
    width: Optional[int] = None
    height: Optional[int] = None
    position: int = 0
    full_json: Optional[str] = None  # Complete raw data from platform
    source_updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class AssetCreate(BaseCreateSchema, AssetBase):
    """Schema for creating an asset."""
    pass


class AssetUpdate(BaseUpdateSchema):
    """Schema for updating an asset."""
    pass


class AssetResponse(BaseResponseSchema, AssetBase):
    """Asset response schema."""
    pass


class MediaItem(BaseModel):
    """Media item schema based on frontend MediaItem interface."""
    type: str  # image, video, featured_image, etc.
    id: str
    src: str
    alt: str
    path: Optional[str] = None
    key: Optional[str] = None
    data: Optional[Any] = None
    source_type: Optional[str] = None  # "product" or "ai_generated"


class PaginatedAssetResponse(BaseModel):
    """Paginated asset list response."""
    items: List[MediaItem]
    total: int
    page: int
    limit: int
    total_pages: int

    class Config:
        from_attributes = True