"""
Sync Module API Router.
Handles data synchronization endpoints using Airbyte connectors.
"""

import logging
from typing import List, TYPE_CHECKING

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from core.db.database import get_db
from modules.auth.models import User
from modules.auth.router import get_current_user
from modules.sync.service import sync_service

if TYPE_CHECKING:
    from modules.stores.models import Store

logger = logging.getLogger(__name__)
router = APIRouter()




@router.get("/airbyte/platforms")
async def get_supported_platforms():
    """
    Get list of platforms supported by Airbyte integration.

    Returns:
        List of supported platforms
    """
    return {
        "platforms": sync_service.get_supported_platforms(),
        "description": "Platforms supported for data synchronization"
    }