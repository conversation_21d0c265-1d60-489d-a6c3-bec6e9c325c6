"""
Platform Integration for Sync Module.
Provides generic data synchronization from e-commerce platforms.
Delegates platform-specific operations to respective plugin services.
"""

import logging
from typing import Dict, List, Optional, Any, TYPE_CHECKING
from datetime import datetime

from plugins import get_platform_service, get_available_platforms

# if TYPE_CHECKING:
#     from modules.stores.models import Store

logger = logging.getLogger(__name__)


class SyncService:
    """
    Generic integration service for syncing e-commerce data.
    Delegates to platform-specific plugins for actual sync operations.
    """

    def __init__(self):
        # Plugin services are managed by the plugin registry
        pass

    def _get_platform_service(self, platform: str):
        """Get the sync service for a specific platform from the plugin registry."""
        return get_platform_service(platform)

    async def sync_store_data(self, store: "Store", sync_types: List[str] = None) -> Dict[str, Any]:
        """
        Sync data from a store using Airbyte connectors.

        Args:
            store: Store object with connection details
            sync_types: List of data types to sync (products, orders, customers)

        Returns:
            Sync results dictionary
        """
        if not sync_types:
            sync_types = ["products", "orders", "customers"]

        results = {
            "store_id": store.id,
            "platform": store.platform,
            "sync_types": sync_types,
            "results": {},
            "timestamp": datetime.utcnow().isoformat()
        }

        platform_service = self._get_platform_service(store.platform)

        for sync_type in sync_types:
            try:
                if sync_type == "products":
                    result = await platform_service.sync_products(store)
                elif sync_type == "orders":
                    result = await platform_service.sync_orders(store)
                elif sync_type == "customers":
                    result = await platform_service.sync_customers(store)
                else:
                    result = {"error": f"Unsupported sync type: {sync_type}"}

                results["results"][sync_type] = result

            except Exception as e:
                logger.error(f"Failed to sync {sync_type} for store {store.id}: {e}")
                results["results"][sync_type] = {"error": str(e)}

        return results

    async def get_sync_status(self, store: "Store", sync_type: str) -> Dict[str, Any]:
        """Get the status of a sync operation."""
        platform_service = self._get_platform_service(store.platform)
        return await platform_service.get_sync_status(store, sync_type)

    async def trigger_incremental_sync(self, store: "Store", sync_type: str) -> Dict[str, Any]:
        """Trigger an incremental sync for specific data type."""
        platform_service = self._get_platform_service(store.platform)
        return await platform_service.trigger_incremental_sync(store, sync_type)

    def get_supported_platforms(self) -> List[str]:
        """Get list of supported platforms for Airbyte integration."""
        return get_available_platforms()

    async def validate_store_connection(self, store: "Store") -> Dict[str, Any]:
        """Validate that the store connection works with Airbyte."""
        platform_service = self._get_platform_service(store.platform)
        return await platform_service.validate_connection(store)


# Create service instance
sync_service = SyncService()