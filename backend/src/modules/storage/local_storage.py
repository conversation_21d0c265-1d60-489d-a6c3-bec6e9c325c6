"""
Local file storage service for development.
Provides S3-compatible interface for local file storage.
"""

import os
import shutil
import mimetypes
from pathlib import Path
from typing import Optional, Dict, Any, List
from urllib.parse import quote
from datetime import datetime, timedelta

from core.config import get_settings
from .base_storage import BaseStorageService

settings = get_settings()


class LocalStorageService(BaseStorageService):
    """Local file storage service with S3-compatible interface."""
    
    def __init__(self, base_path: str = None):
        """
        Initialize local storage service.
        
        Args:
            base_path: Base directory for file storage
        """
        self.base_path = Path(base_path or settings.LOCAL_STORAGE_PATH or "./storage")
        self.base_path.mkdir(parents=True, exist_ok=True)
        
        # Create bucket directories
        self.buckets_path = self.base_path / "buckets"
        self.buckets_path.mkdir(exist_ok=True)
        
        # Default bucket
        self.default_bucket = settings.S3_BUCKET_NAME or "default"
        self.bucket_path = self.buckets_path / self.default_bucket
        self.bucket_path.mkdir(exist_ok=True)
    
    def _get_file_path(self, key: str, bucket: str = None) -> Path:
        """Get full file path for a key."""
        bucket = bucket or self.default_bucket
        bucket_path = self.buckets_path / bucket
        bucket_path.mkdir(exist_ok=True)
        
        # Ensure key doesn't escape bucket directory
        key = key.lstrip('/')
        file_path = bucket_path / key
        
        # Create parent directories
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        return file_path
    
    def _get_public_url(self, key: str, bucket: str = None) -> str:
        """Get public URL for a file."""
        bucket = bucket or self.default_bucket
        base_url = settings.LOCAL_STORAGE_BASE_URL or f"http://localhost:{settings.PORT}/storage"
        return f"{base_url}/{bucket}/{quote(key)}"
    
    async def upload_file(
        self,
        file_path: str,
        key: str,
        bucket: str = None,
        content_type: str = None,
        metadata: Dict[str, str] = None
    ) -> str:
        """
        Upload a file to local storage.
        
        Args:
            file_path: Path to the file to upload
            key: Storage key (path) for the file
            bucket: Bucket name (directory)
            content_type: MIME type of the file
            metadata: Additional metadata
            
        Returns:
            Public URL of the uploaded file
        """
        source_path = Path(file_path)
        if not source_path.exists():
            raise FileNotFoundError(f"Source file not found: {file_path}")
        
        dest_path = self._get_file_path(key, bucket)
        
        # Copy file
        shutil.copy2(source_path, dest_path)
        
        # Store metadata
        if metadata or content_type:
            metadata_path = dest_path.with_suffix(dest_path.suffix + '.meta')
            meta_data = {
                'content_type': content_type or mimetypes.guess_type(str(dest_path))[0],
                'uploaded_at': datetime.utcnow().isoformat(),
                'size': dest_path.stat().st_size,
                **(metadata or {})
            }
            
            with open(metadata_path, 'w') as f:
                import json
                json.dump(meta_data, f, indent=2)
        
        return self._get_public_url(key, bucket)
    
    async def upload_fileobj(
        self,
        fileobj,
        key: str,
        bucket: str = None,
        content_type: str = None,
        metadata: Dict[str, str] = None
    ) -> str:
        """
        Upload a file object to local storage.
        
        Args:
            fileobj: File-like object to upload
            key: Storage key (path) for the file
            bucket: Bucket name (directory)
            content_type: MIME type of the file
            metadata: Additional metadata
            
        Returns:
            Public URL of the uploaded file
        """
        dest_path = self._get_file_path(key, bucket)
        
        # Write file content
        with open(dest_path, 'wb') as f:
            if hasattr(fileobj, 'read'):
                # File-like object
                shutil.copyfileobj(fileobj, f)
            else:
                # Bytes
                f.write(fileobj)
        
        # Store metadata
        if metadata or content_type:
            metadata_path = dest_path.with_suffix(dest_path.suffix + '.meta')
            meta_data = {
                'content_type': content_type or mimetypes.guess_type(str(dest_path))[0],
                'uploaded_at': datetime.utcnow().isoformat(),
                'size': dest_path.stat().st_size,
                **(metadata or {})
            }
            
            with open(metadata_path, 'w') as f:
                import json
                json.dump(meta_data, f, indent=2)
        
        return self._get_public_url(key, bucket)
    
    async def download_file(
        self,
        key: str,
        file_path: str,
        bucket: str = None
    ) -> bool:
        """
        Download a file from local storage.
        
        Args:
            key: Storage key of the file
            file_path: Local path to save the file
            bucket: Bucket name
            
        Returns:
            True if successful, False otherwise
        """
        source_path = self._get_file_path(key, bucket)
        
        if not source_path.exists():
            return False
        
        dest_path = Path(file_path)
        dest_path.parent.mkdir(parents=True, exist_ok=True)
        
        shutil.copy2(source_path, dest_path)
        return True
    
    async def delete_file(self, key: str, bucket: str = None) -> bool:
        """
        Delete a file from local storage.
        
        Args:
            key: Storage key of the file
            bucket: Bucket name
            
        Returns:
            True if successful, False otherwise
        """
        file_path = self._get_file_path(key, bucket)
        metadata_path = file_path.with_suffix(file_path.suffix + '.meta')
        
        success = False
        
        if file_path.exists():
            file_path.unlink()
            success = True
        
        if metadata_path.exists():
            metadata_path.unlink()
        
        return success
    
    async def file_exists(self, key: str, bucket: str = None) -> bool:
        """
        Check if a file exists in local storage.
        
        Args:
            key: Storage key of the file
            bucket: Bucket name
            
        Returns:
            True if file exists, False otherwise
        """
        file_path = self._get_file_path(key, bucket)
        return file_path.exists()
    
    async def get_file_url(
        self,
        key: str,
        bucket: str = None,
        expires_in: int = 3600
    ) -> str:
        """
        Get a public URL for a file.
        
        Args:
            key: Storage key of the file
            bucket: Bucket name
            expires_in: URL expiration time in seconds (ignored for local storage)
            
        Returns:
            Public URL of the file
        """
        return self._get_public_url(key, bucket)
    
    async def get_presigned_url(
        self,
        key: str,
        bucket: str = None,
        expires_in: int = 3600,
        method: str = "GET"
    ) -> str:
        """
        Get a presigned URL for file access.
        
        Args:
            key: Storage key of the file
            bucket: Bucket name
            expires_in: URL expiration time in seconds
            method: HTTP method (GET, PUT, etc.)
            
        Returns:
            Presigned URL
        """
        # For local storage, return regular public URL
        # In production, this would generate time-limited signed URLs
        return self._get_public_url(key, bucket)
    
    async def list_files(
        self,
        prefix: str = "",
        bucket: str = None,
        max_keys: int = 1000
    ) -> List[Dict[str, Any]]:
        """
        List files in local storage.
        
        Args:
            prefix: Key prefix to filter files
            bucket: Bucket name
            max_keys: Maximum number of files to return
            
        Returns:
            List of file information dictionaries
        """
        bucket = bucket or self.default_bucket
        bucket_path = self.buckets_path / bucket
        
        if not bucket_path.exists():
            return []
        
        files = []
        count = 0
        
        for file_path in bucket_path.rglob("*"):
            if count >= max_keys:
                break
            
            if file_path.is_file() and not file_path.name.endswith('.meta'):
                # Get relative path from bucket root
                relative_path = file_path.relative_to(bucket_path)
                key = str(relative_path).replace('\\', '/')  # Normalize path separators
                
                if key.startswith(prefix):
                    stat = file_path.stat()
                    
                    # Load metadata if available
                    metadata_path = file_path.with_suffix(file_path.suffix + '.meta')
                    metadata = {}
                    if metadata_path.exists():
                        try:
                            with open(metadata_path, 'r') as f:
                                import json
                                metadata = json.load(f)
                        except:
                            pass
                    
                    files.append({
                        'key': key,
                        'size': stat.st_size,
                        'last_modified': datetime.fromtimestamp(stat.st_mtime),
                        'content_type': metadata.get('content_type', mimetypes.guess_type(str(file_path))[0]),
                        'url': self._get_public_url(key, bucket),
                        'metadata': metadata
                    })
                    count += 1
        
        return sorted(files, key=lambda x: x['last_modified'], reverse=True)
    
    async def get_file_metadata(self, key: str, bucket: str = None) -> Optional[Dict[str, Any]]:
        """
        Get metadata for a file.
        
        Args:
            key: Storage key of the file
            bucket: Bucket name
            
        Returns:
            File metadata dictionary or None if not found
        """
        file_path = self._get_file_path(key, bucket)
        
        if not file_path.exists():
            return None
        
        stat = file_path.stat()
        
        # Load stored metadata
        metadata_path = file_path.with_suffix(file_path.suffix + '.meta')
        stored_metadata = {}
        if metadata_path.exists():
            try:
                with open(metadata_path, 'r') as f:
                    import json
                    stored_metadata = json.load(f)
            except:
                pass
        
        return {
            'key': key,
            'size': stat.st_size,
            'last_modified': datetime.fromtimestamp(stat.st_mtime),
            'content_type': stored_metadata.get('content_type', mimetypes.guess_type(str(file_path))[0]),
            'url': self._get_public_url(key, bucket),
            **stored_metadata
        }
