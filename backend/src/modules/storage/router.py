import logging
from typing import Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status, UploadFile, File
from pydantic import BaseModel
from sqlalchemy.orm import Session

from core.db.database import get_db
from modules.auth.models import User, Tenant
from modules.media.models import MediaVariant
from modules.auth.router import get_current_user
from .storage_service import MediaStorageService, StorageProvider
from core.config import get_settings

logger = logging.getLogger(__name__)
router = APIRouter(tags=["storage"])

settings = get_settings()

# Initialize storage service
storage_provider = StorageProvider(settings.STORAGE_PROVIDER)
storage_service = MediaStorageService(
    provider=storage_provider,
    bucket_name=settings.S3_BUCKET_NAME,
    aws_access_key=settings.AWS_ACCESS_KEY_ID,
    aws_secret_key=settings.AWS_SECRET_ACCESS_KEY,
    aws_region=settings.AWS_REGION
)


class MediaProcessingRequest(BaseModel):
    """Request model for media processing."""
    variant_id: str
    generate_hls: bool = True
    generate_thumbnails: bool = True
    generate_preview_gif: bool = True
    extract_subtitles: bool = False


class MediaUploadRequest(BaseModel):
    """Request model for media upload."""
    media_id: str
    source_url: str
    filename: Optional[str] = None


@router.get("/stats")
async def get_storage_stats(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get storage statistics for the current user's tenant.
    
    Returns storage usage, file counts, and quota information.
    """
    try:
        # Get user's tenant
        tenant = db.query(Tenant).filter(Tenant.owner_id == current_user.id).first()
        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tenant not found"
            )

        # Get storage stats
        stats = storage_service.get_storage_stats(tenant.id)
        
        # Add quota information
        stats.update({
            'storage_limit_gb': tenant.storage_limit_gb,
            'storage_used_gb': tenant.storage_used_gb,
            'storage_available_gb': max(0, tenant.storage_limit_gb - tenant.storage_used_gb),
            'usage_percentage': round((tenant.storage_used_gb / tenant.storage_limit_gb) * 100, 2) if tenant.storage_limit_gb > 0 else 0
        })
        
        return stats
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Failed to get storage stats: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get storage stats: {str(e)}"
        )


@router.post("/upload")
async def upload_media(
    request: MediaUploadRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Upload a media from URL to storage.

    Downloads media from the provided URL and stores it in tenant-isolated storage.
    """
    try:
        # Get user's tenant
        tenant = db.query(Tenant).filter(Tenant.owner_id == current_user.id).first()
        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tenant not found"
            )

        # Check storage quota
        if tenant.storage_used_gb >= tenant.storage_limit_gb:
            raise HTTPException(
                status_code=status.HTTP_402_PAYMENT_REQUIRED,
                detail="Storage quota exceeded"
            )
        
        # Upload media
        storage_url = await storage_service.upload_media_from_url(
            tenant_id=tenant.id,
            media_id=request.media_id,
            source_url=request.source_url,
            filename=request.filename
        )

        if storage_url:
            return {
                "success": True,
                "storage_url": storage_url,
                "media_id": request.media_id,
                "message": "Media uploaded successfully"
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to upload media"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Failed to upload media: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to upload media: {str(e)}"
        )


@router.post("/upload-file")
async def upload_media_file(
    media_id: str,
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Upload a media file directly to storage.

    Accepts media file upload and stores it in tenant-isolated storage.
    """
    try:
        # Get user's tenant
        tenant = db.query(Tenant).filter(Tenant.owner_id == current_user.id).first()
        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tenant not found"
            )

        # Validate file type
        if not file.content_type or not file.content_type.startswith('video/'):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="File must be a video"
            )
        
        # Check file size (limit to 100MB for now)
        max_size = 100 * 1024 * 1024  # 100MB
        file_content = await file.read()
        
        if len(file_content) > max_size:
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail="File too large. Maximum size is 100MB"
            )
        
        # Check storage quota
        file_size_gb = len(file_content) / (1024**3)
        if tenant.storage_used_gb + file_size_gb > tenant.storage_limit_gb:
            raise HTTPException(
                status_code=status.HTTP_402_PAYMENT_REQUIRED,
                detail="Storage quota would be exceeded"
            )
        
        # Upload media
        storage_url = await storage_service.upload_media_data(
            tenant_id=tenant.id,
            media_id=media_id,
            video_data=file_content,
            filename=file.filename or f"{media_id}.mp4",
            content_type=file.content_type
        )

        if storage_url:
            # Update tenant storage usage
            tenant.storage_used_gb += file_size_gb
            db.commit()

            return {
                "success": True,
                "storage_url": storage_url,
                "media_id": media_id,
                "file_size_bytes": len(file_content),
                "message": "Media file uploaded successfully"
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to upload media file"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Failed to upload media file: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to upload media file: {str(e)}"
        )


@router.post("/process")
async def process_media(
    request: MediaProcessingRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Process a media variant with transcoding and asset generation.

    Generates HLS streams, thumbnails, preview GIFs, and subtitles.
    """
    try:
        # Get user's tenant
        tenant = db.query(Tenant).filter(Tenant.owner_id == current_user.id).first()
        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tenant not found"
            )

        # Find the media variant
        variant = db.query(MediaVariant).filter(
            MediaVariant.external_variant_id == request.variant_id,
            MediaVariant.tenant_id == tenant.id
        ).first()

        if not variant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Media variant not found"
            )

        if not variant.video_url:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Media variant has no source URL"
            )
        
        # Process media
        processing_result = await storage_service.process_media(
            tenant_id=tenant.id,
            media_id=variant.external_variant_id,
            source_url=variant.video_url,
            generate_hls=request.generate_hls,
            generate_thumbnails=request.generate_thumbnails,
            generate_preview_gif=request.generate_preview_gif,
            extract_subtitles=request.extract_subtitles
        )

        if processing_result.success:
            # Update variant with processed assets
            if processing_result.thumbnail_url:
                variant.thumbnail_url = processing_result.thumbnail_url

            if processing_result.preview_gif_url:
                variant.preview_gif_url = processing_result.preview_gif_url

            # Update HLS URL if generated
            if processing_result.processed_files:
                hls_file = next((f for f in processing_result.processed_files if f.format.value == "hls"), None)
                if hls_file:
                    variant.hls_url = hls_file.url

            variant.updated_at = datetime.utcnow()
            db.commit()

            return {
                "success": True,
                "variant_id": request.variant_id,
                "processing_result": processing_result.dict(),
                "message": "Media processing completed successfully"
            }
        else:
            return {
                "success": False,
                "variant_id": request.variant_id,
                "error": processing_result.error_message,
                "message": "Media processing failed"
            }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Failed to process media: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to process media: {str(e)}"
        )


@router.delete("/variants/{variant_id}")
async def delete_media_assets(
    variant_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Delete all storage assets for a media variant.

    Removes media files, thumbnails, previews, and other generated assets.
    """
    try:
        # Get user's tenant
        tenant = db.query(Tenant).filter(Tenant.owner_id == current_user.id).first()
        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tenant not found"
            )

        # Find the media variant
        variant = db.query(MediaVariant).filter(
            MediaVariant.external_variant_id == variant_id,
            MediaVariant.tenant_id == tenant.id
        ).first()

        if not variant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Media variant not found"
            )

        # Delete storage assets
        success = await storage_service.delete_media_assets(tenant.id, variant_id)

        if success:
            # Clear URLs from database
            variant.video_url = None
            variant.hls_url = None
            variant.thumbnail_url = None
            variant.preview_gif_url = None
            variant.updated_at = datetime.utcnow()
            db.commit()

            return {
                "success": True,
                "variant_id": variant_id,
                "message": "Media assets deleted successfully"
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to delete media assets"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Failed to delete media assets: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete media assets: {str(e)}"
        )


@router.get("/provider/info")
async def get_storage_provider_info(
    current_user: User = Depends(get_current_user)
):
    """
    Get information about the current storage provider.
    """
    try:
        return {
            "provider": storage_service.provider.value,
            "bucket_name": storage_service.bucket_name,
            "aws_region": storage_service.aws_region,
            "base_url": storage_service.base_url,
            "supports_hls": True,
            "supports_thumbnails": True,
            "supports_preview_gifs": True,
            "max_file_size_mb": 500,
            "supported_formats": ["mp4", "mov", "avi", "webm"]
        }
        
    except Exception as e:
        logger.exception(f"Failed to get storage provider info: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get provider info: {str(e)}"
        )
