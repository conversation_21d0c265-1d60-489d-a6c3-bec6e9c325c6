from sqlalchemy import Column, Integer, String, Float, Text, DateTime, ForeignKey, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from core.db.database import Base


class Product(Base):
    """Product model for e-commerce products - core product data."""

    __tablename__ = "products"

    # Primary key
    id = Column(Integer, primary_key=True, index=True)

    # Core product identity (exists on ALL products, ALL platforms)
    external_id = Column(String, unique=True, nullable=False)  # Platform product ID
    title = Column(String, nullable=False)
    handle = Column(String)  # URL handle/slug
    vendor = Column(String)
    product_type = Column(String)
    status = Column(String, default="active")  # active, draft, archived, deleted
    published = Column(Boolean, default=True)

    # Store relationship
    store_id = Column(Integer, ForeignKey("stores.id"))
    store = relationship("modules.stores.models.Store", back_populates="products")

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    published_at = Column(DateTime(timezone=True))
    source_updated_at = Column(DateTime(timezone=True))  # Source system update timestamp

    # Complete raw data from platform API
    full_json = Column(Text)  # JSON: Complete raw data from platform API

    # Product metadata
    description = Column(Text)  # Product description (can be HTML)
    tags = Column(Text)  # JSON: Array of product tags
    options = Column(Text)  # JSON: Product options/configuration
    seo = Column(Text)  # JSON: SEO metadata
    metafields = Column(Text)  # JSON: Custom metadata fields from metafield_products table
    collections = Column(Text)  # JSON: Collections this product belongs to
    featured_media = Column(Text)  # JSON: Featured media from Shopify (can be image or video)

    # Relationships
    variants = relationship("ProductVariant", back_populates="product", cascade="all, delete-orphan")
    images = relationship("ProductImage", back_populates="product", cascade="all, delete-orphan")


class ProductVariant(Base):
    """Product variant model - individual SKUs with pricing and inventory."""

    __tablename__ = "product_variants"

    id = Column(Integer, primary_key=True, index=True)
    external_id = Column(String, unique=True, nullable=False)  # Platform variant ID
    product_id = Column(Integer, ForeignKey("products.id"), nullable=False)

    # Core variant data
    title = Column(String)  # Variant title (e.g., "Small / Red")
    sku = Column(String)
    barcode = Column(String)
    price = Column(Float)
    compare_at_price = Column(Float)
    cost = Column(Float)

    # Physical properties
    weight = Column(Float)
    weight_unit = Column(String, default="kg")

    # Inventory
    quantity = Column(Integer, default=0)
    inventory_policy = Column(String)  # continue, deny
    inventory_item_id = Column(String)  # Platform inventory item ID

    # Options (size, color, etc.)
    option1 = Column(String)
    option2 = Column(String)
    option3 = Column(String)

    # Shipping and tax
    taxable = Column(Boolean, default=True)
    requires_shipping = Column(Boolean, default=True)
    fulfillment_service = Column(String)

    # Status
    available_for_sale = Column(Boolean, default=True)

    # Raw data
    full_json = Column(Text)  # JSON: Complete variant data

    # Metafields from metafield_product_variants table
    metafields = Column(Text)  # JSON: Custom metadata fields for this variant

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    source_updated_at = Column(DateTime(timezone=True))  # Source system update timestamp
    # Relationships
    product = relationship("Product", back_populates="variants")


class ProductImage(Base):
    """Product image model - images can be associated with products or specific variants."""

    __tablename__ = "product_images"

    id = Column(Integer, primary_key=True, index=True)
    external_id = Column(String, unique=True, nullable=False)  # Platform image ID
    product_id = Column(Integer, ForeignKey("products.id"), nullable=False)
    variant_id = Column(Integer, ForeignKey("product_variants.id"), nullable=True)  # Can be per variant

    # Image data
    src = Column(String, nullable=False)  # Image URL
    alt = Column(String)  # Alt text
    width = Column(Integer)
    height = Column(Integer)
    position = Column(Integer, default=0)  # Display order

    __table_args__ = (
        {'schema': None},  # Add unique constraint on (product_id, src)
    )

    # Raw data
    full_json = Column(Text)  # JSON: Complete image data

    # Metafields from metafield_product_images table
    metafields = Column(Text)  # JSON: Custom metadata fields for this image

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    source_updated_at = Column(DateTime(timezone=True))  # Source system update timestamp

    # Relationships
    product = relationship("Product", back_populates="images")
    variant = relationship("ProductVariant")


class InventoryLevel(Base):
    """Inventory level model - tracks inventory across locations."""

    __tablename__ = "inventory_levels"

    id = Column(Integer, primary_key=True, index=True)
    external_id = Column(String, unique=True, nullable=False)  # Platform inventory level ID
    inventory_item_id = Column(String, nullable=False)  # Links to variant's inventory_item_id
    location_id = Column(String, nullable=False)  # Platform location ID

    # Inventory data
    available = Column(Integer, default=0)  # Available quantity at this location
    updated_at_location = Column(DateTime(timezone=True))  # When this location was last updated

    # Raw data
    full_json = Column(Text)  # JSON: Complete inventory level data

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    source_updated_at = Column(DateTime(timezone=True))  # Source system update timestamp