from datetime import datetime, date
from typing import Optional, Dict, Any

from pydantic import BaseModel

from core.schemas.base_schemas import BaseCreateSchema, BaseResponseSchema, BaseUpdateSchema


class StoreConnectionTestRequest(BaseModel):
    """Schema for store connection test requests."""

    platform: str = "shopify"
    shop_domain: str
    admin_access_token: str


class StoreConnectionTest(BaseModel):
    """Schema for store connection test results."""

    success: bool
    message: str
    store_info: Optional[Dict[str, Any]] = None


class StoreBase(BaseModel):
    """Base store schema."""

    platform: str = "shopify"
    shop_domain: str
    api_key: Optional[str] = None
    api_secret_key: Optional[str] = None
    admin_access_token: Optional[str] = None
    shop_id: Optional[str] = None
    shop_name: Optional[str] = None
    is_active: bool = True



class StoreCreate(BaseCreateSchema, StoreBase):
    """Schema for creating a store."""

    pass


class StoreUpdate(BaseUpdateSchema):
    """Schema for updating a store."""

    platform: Optional[str] = None
    api_key: Optional[str] = None
    api_secret_key: Optional[str] = None
    admin_access_token: Optional[str] = None
    shop_domain: Optional[str] = None
    shop_id: Optional[str] = None
    shop_name: Optional[str] = None
    is_active: Optional[bool] = None


class StoreResponse(BaseResponseSchema, StoreBase):
    """Schema for store responses."""

    owner_id: int
    last_sync: Optional[datetime] = None

    # Airbyte integration fields
    airbyte_source_id: Optional[str] = None
    airbyte_destination_id: Optional[str] = None
    airbyte_connection_id: Optional[str] = None


# Analytics Schemas
class ForecastBase(BaseModel):
    """Base forecast schema."""
    
    store_id: int
    forecast_date: datetime
    predicted_sales: float
    confidence_lower: Optional[float] = None
    confidence_upper: Optional[float] = None
    forecast_period: Optional[str] = None


class ForecastCreate(BaseCreateSchema, ForecastBase):
    """Schema for creating a forecast."""
    
    raw_forecast_data: Optional[str] = None


class ForecastResponse(BaseResponseSchema, ForecastBase):
    """Schema for forecast responses."""
    
    raw_forecast_data: Optional[str] = None


class StoreSaleBase(BaseModel):
    """Base store sale schema."""
    
    store_id: int
    product_id: Optional[str] = None
    variant_id: Optional[str] = None
    sale_date: date
    quantity_sold: int
    revenue: float


class StoreSaleCreate(BaseCreateSchema, StoreSaleBase):
    """Schema for creating a store sale."""
    
    pass


class StoreSaleResponse(BaseResponseSchema, StoreSaleBase):
    """Schema for store sale responses."""
    
    pass


class ProductPerformanceBase(BaseModel):
    """Base product performance schema."""
    
    product_id: str
    store_id: int
    total_sold: int = 0
    total_revenue: float = 0.0


class ProductPerformanceCreate(BaseCreateSchema, ProductPerformanceBase):
    """Schema for creating product performance."""
    
    pass


class ProductPerformanceResponse(BaseResponseSchema, ProductPerformanceBase):
    """Schema for product performance responses."""
    
    pass


class StoreAnalyticsSnapshotBase(BaseModel):
    """Base store analytics snapshot schema."""
    
    store_id: int
    date: date
    total_sales: float = 0.0
    total_orders: int = 0
    average_order_value: float = 0.0
    new_customers: int = 0
    conversion_rate: float = 0.0


class StoreAnalyticsSnapshotCreate(BaseCreateSchema, StoreAnalyticsSnapshotBase):
    """Schema for creating store analytics snapshot."""
    
    pass


class StoreAnalyticsSnapshotResponse(BaseResponseSchema, StoreAnalyticsSnapshotBase):
    """Schema for store analytics snapshot responses."""
    
    pass


class HolidayBase(BaseModel):
    """Base holiday schema."""
    
    holiday_date: datetime
    name: str


class HolidayCreate(BaseCreateSchema, HolidayBase):
    """Schema for creating a holiday."""
    
    pass


class HolidayResponse(BaseResponseSchema, HolidayBase):
    """Schema for holiday responses."""

    pass




class SyncJobResponse(BaseModel):
    """Schema for sync job responses."""

    job_id: str
    status: str
    message: str


class SyncRequest(BaseModel):
    """Schema for sync requests."""

    mode: str = "incremental"  # 'full' or 'incremental'
