"""
Generic sync service for database operations.
Platform-independent database sync logic with platform-specific implementations.
"""

import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from modules.products.models import Product
from modules.orders.models import Order, OrderLineItem
from modules.stores.models import Store
from modules.customers.models import Customer
from plugins.interfaces import StoreServiceInterface

logger = logging.getLogger(__name__)


class DBService:
    """Database service that handles database operations for any platform."""

    def __init__(self, store_domain: str):
        self.store_domain = store_domain

    def _convert_tags_to_string(self, tags) -> Optional[str]:
        """Convert tags from list to comma-separated string."""
        if isinstance(tags, list):
            return ", ".join(tags)
        elif isinstance(tags, str):
            return tags
        else:
            return None

    async def sync_product_to_db(
        self,
        db: AsyncSession,
        product_data: Dict[str, Any],
        platform_specific_data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Sync a single product to database."""
        product_id = str(product_data["id"])

        # Check if product exists
        result = await db.execute(
            select(Product).filter(Product.external_id == product_id)
        )
        existing_product = result.scalar_one_or_none()

        # Get store
        store_result = await db.execute(
            select(Store).filter(Store.shop_domain == self.store_domain)
        )
        store = store_result.scalar_one_or_none()
        if not store:
            raise ValueError(f"Store with domain {self.store_domain} not found")

        # Extract common fields
        title = product_data.get("title")
        description = product_data.get("description") or product_data.get("descriptionHtml")
        handle = product_data.get("handle")
        product_type = product_data.get("productType")
        vendor = product_data.get("vendor")
        status = product_data.get("status")
        tags = self._convert_tags_to_string(product_data.get("tags"))

        # Extract price from first variant
        price = 0.0
        compare_at_price = None
        if product_data.get("variants") and product_data["variants"].get("edges"):
            first_variant = product_data["variants"]["edges"][0]["node"]
            if first_variant.get("price"):
                try:
                    price = float(first_variant["price"])
                except (ValueError, TypeError):
                    price = 0.0
            if first_variant.get("compareAtPrice"):
                try:
                    compare_at_price = float(first_variant["compareAtPrice"])
                except (ValueError, TypeError):
                    compare_at_price = None

        # Extract images
        images = []
        if product_data.get("images") and product_data["images"].get("edges"):
            for edge in product_data["images"]["edges"]:
                image_node = edge["node"]
                images.append({
                    "id": image_node.get("id"),
                    "url": image_node.get("url"),
                    "alt_text": image_node.get("altText"),
                    "width": image_node.get("width"),
                    "height": image_node.get("height"),
                    "original_src": image_node.get("originalSrc"),
                    "transformed_src": image_node.get("transformedSrc")
                })

        # Extract featured image
        featured_image = None
        if product_data.get("featuredImage"):
            featured_image = {
                "id": product_data["featuredImage"].get("id"),
                "url": product_data["featuredImage"].get("url"),
                "alt_text": product_data["featuredImage"].get("altText"),
                "width": product_data["featuredImage"].get("width"),
                "height": product_data["featuredImage"].get("height")
            }

        # Extract options
        options = product_data.get("options", [])

        # Extract SEO
        seo = product_data.get("seo")

        # Extract metafields
        metafields = []
        if product_data.get("metafields") and product_data["metafields"].get("edges"):
            for edge in product_data["metafields"]["edges"]:
                metafields.append(edge["node"])

        # Extract collections
        collections = []
        if product_data.get("collections") and product_data["collections"].get("edges"):
            for edge in product_data["collections"]["edges"]:
                collections.append(edge["node"])

        if existing_product:
            # Update existing product
            updated = False

            # Update basic fields
            if existing_product.title != title:
                existing_product.title = title
                updated = True
            if existing_product.description != description:
                existing_product.description = description
                updated = True
            if existing_product.handle != handle:
                existing_product.handle = handle
                updated = True
            if existing_product.product_type != product_type:
                existing_product.product_type = product_type
                updated = True
            if existing_product.vendor != vendor:
                existing_product.vendor = vendor
                updated = True
            if existing_product.status != status:
                existing_product.status = status
                updated = True
            if existing_product.tags != tags:
                existing_product.tags = tags
                updated = True

            # Update prices
            if existing_product.price != price:
                existing_product.price = price
                updated = True
            if existing_product.compare_at_price != compare_at_price:
                existing_product.compare_at_price = compare_at_price
                updated = True

            # Update images
            if existing_product.images != json.dumps(images) if images else None:
                existing_product.images = json.dumps(images) if images else None
                updated = True

            # Update featured image
            if existing_product.featured_image != json.dumps(featured_image) if featured_image else None:
                existing_product.featured_image = json.dumps(featured_image) if featured_image else None
                updated = True

            # Update options
            if existing_product.options != json.dumps(options):
                existing_product.options = json.dumps(options)
                updated = True

            # Update SEO
            if existing_product.seo != json.dumps(seo) if seo else None:
                existing_product.seo = json.dumps(seo) if seo else None
                updated = True

            # Update metafields
            if existing_product.metafields != json.dumps(metafields):
                existing_product.metafields = json.dumps(metafields)
                updated = True

            # Update collections
            if existing_product.collections != json.dumps(collections):
                existing_product.collections = json.dumps(collections)
                updated = True

            # Update platform-specific data
            if platform_specific_data:
                if existing_product.platform_data != json.dumps(platform_specific_data):
                    existing_product.platform_data = json.dumps(platform_specific_data)
                    updated = True

            # Update published_at
            published_at = None
            if product_data.get("publishedAt"):
                try:
                    published_at = datetime.fromisoformat(product_data["publishedAt"].replace('Z', '+00:00'))
                except (ValueError, TypeError):
                    pass
            if existing_product.published_at != published_at:
                existing_product.published_at = published_at
                updated = True

            existing_product.updated_at = datetime.now()

            return {"action": "updated" if updated else "unchanged", "product": existing_product}

        else:
            # Create new product
            new_product = Product(
                external_id=product_id,
                title=title,
                description=description,
                price=price,
                compare_at_price=compare_at_price,
                handle=handle,
                product_type=product_type,
                vendor=vendor,
                status=status,
                tags=tags,
                images=json.dumps(images) if images else None,
                featured_image=json.dumps(featured_image) if featured_image else None,
                options=json.dumps(options),
                seo=json.dumps(seo) if seo else None,
                metafields=json.dumps(metafields),
                collections=json.dumps(collections),
                platform_data=json.dumps(platform_specific_data) if platform_specific_data else None,
                store_id=store.id,
                published_at=datetime.fromisoformat(product_data["publishedAt"].replace('Z', '+00:00')) if product_data.get("publishedAt") else None
            )

            db.add(new_product)
            return {"action": "created", "product": new_product}

    async def sync_order_to_db(
        self,
        db: AsyncSession,
        order_data: Dict[str, Any],
        platform_specific_data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Sync a single order to database."""
        order_id = str(order_data["id"])

        # Get store
        store_result = await db.execute(
            select(Store).filter(Store.shop_domain == self.store_domain)
        )
        store = store_result.scalar_one_or_none()
        if not store:
            raise ValueError(f"Store with domain {self.store_domain} not found")

        # Get customer if exists
        customer_id = None
        if order_data.get("customer"):
            customer_external_id = str(order_data["customer"]["id"])
            customer_result = await db.execute(
                select(Customer).filter(Customer.external_id == customer_external_id)
            )
            customer = customer_result.scalar_one_or_none()
            if customer:
                customer_id = customer.id

        # Check if order exists
        order_result = await db.execute(
            select(Order).filter(Order.external_id == order_id)
        )
        existing_order = order_result.scalar_one_or_none()

        if existing_order:
            return {"action": "exists", "order": existing_order}
        else:
            # Create new order
            new_order = Order(
                external_id=order_id,
                store_id=store.id,
                customer_id=customer_id,
                name=order_data.get("name"),
                email=order_data.get("email"),
                total_price=float(order_data["totalPriceSet"]["shopMoney"]["amount"]) if order_data.get("totalPriceSet") else None,
                subtotal_price=float(order_data["subtotalPriceSet"]["shopMoney"]["amount"]) if order_data.get("subtotalPriceSet") else None,
                total_tax=float(order_data["totalTaxSet"]["shopMoney"]["amount"]) if order_data.get("totalTaxSet") else None,
                total_discounts=float(order_data["totalDiscountsSet"]["shopMoney"]["amount"]) if order_data.get("totalDiscountsSet") else None,
                currency=order_data.get("totalPriceSet", {}).get("shopMoney", {}).get("currencyCode"),
                financial_status=order_data.get("displayFinancialStatus"),
                fulfillment_status=order_data.get("displayFulfillmentStatus"),
                full_json=json.dumps(order_data)
            )

            db.add(new_order)
            await db.flush()  # Get order ID

            # Sync line items
            if order_data.get("lineItems"):
                for edge in order_data["lineItems"]["edges"]:
                    line_item = edge["node"]
                    await self._sync_order_line_item(db, line_item, new_order.id)

            return {"action": "created", "order": new_order}

    async def _sync_order_line_item(
        self,
        db: AsyncSession,
        line_item_data: Dict[str, Any],
        order_id: int
    ):
        """Sync an order line item."""
        line_item_id = str(line_item_data["id"])

        existing_item = await db.execute(
            select(OrderLineItem).filter(OrderLineItem.external_id == line_item_id)
        )
        existing_item = existing_item.scalar_one_or_none()

        if not existing_item:
            new_item = OrderLineItem(
                external_id=line_item_id,
                order_id=order_id,
                product_id=str(line_item_data.get("product", {}).get("id")) if line_item_data.get("product") else None,
                variant_id=str(line_item_data.get("variant", {}).get("id")) if line_item_data.get("variant") else None,
                title=line_item_data.get("title"),
                sku=line_item_data.get("sku"),
                quantity=line_item_data.get("quantity", 0),
                price=float(line_item_data.get("originalUnitPriceSet", {}).get("shopMoney", {}).get("amount")) if line_item_data.get("originalUnitPriceSet") else None,
                total_price=float(line_item_data.get("originalTotalSet", {}).get("shopMoney", {}).get("amount")) if line_item_data.get("originalTotalSet") else None
            )
            db.add(new_item)


class StoreSyncService:
    """
    Generic store sync service that coordinates between platform API service and generic sync service.
    """

    def __init__(self, store_service: StoreServiceInterface, store_domain: str):
        self.store_service = store_service
        self.store_domain = store_domain
        self.db_service = DBService(store_domain)

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        # Services don't need explicit cleanup
        pass

    async def sync_products(
        self,
        db: AsyncSession,
        limit: int = 50,
        cursor: Optional[str] = None
    ) -> Dict[str, int]:
        """Sync products using store service and generic sync service."""
        logger.info("Starting store product sync")
        return await self._sync_products_full(db)

    async def sync_orders(
        self,
        db: AsyncSession,
        limit: int = 50,
        cursor: Optional[str] = None
    ) -> Dict[str, int]:
        """Sync orders using store service and generic sync service."""
        logger.info("Starting store order sync")
        return await self._sync_orders_full(db)

    async def _sync_products_full(self, db: AsyncSession) -> Dict[str, int]:
        """Sync all products from store with per-page processing."""
        stats = {"added": 0, "updated": 0, "unchanged": 0}

        # Define callback to process each page immediately
        async def process_page(edges):
            nonlocal stats
            for edge in edges:
                product_data = edge["node"]
                try:
                    result = await self.db_service.sync_product_to_db(
                        db,
                        product_data,
                        platform_specific_data={
                            "shopify_product_id": product_data.get("legacyResourceId"),
                            "online_store_url": product_data.get("onlineStoreUrl"),
                            "has_only_default_variant": product_data.get("hasOnlyDefaultVariant"),
                            "total_inventory": product_data.get("totalInventory"),
                            "total_variants": product_data.get("totalVariants"),
                            "tracks_inventory": product_data.get("tracksInventory"),
                            "product_category": product_data.get("productCategory")
                        }
                    )

                    if result["action"] == "created":
                        stats["added"] += 1
                    elif result["action"] == "updated":
                        stats["updated"] += 1
                    else:
                        stats["unchanged"] += 1

                except Exception as e:
                    logger.error(f"Failed to sync product {product_data.get('id', 'unknown')}: {e}")
                    continue

        # Paginate with per-page processing
        response = await self._paginate_all(
            self.store_service.get_products,
            "products",
            process_callback=process_page
        )

        if "errors" in response:
            logger.error(f"Store API errors during product sync: {response['errors']}")
            return {"added": 0, "updated": 0, "unchanged": 0, "errors": response["errors"]}

        # Enhanced logging
        total_processed = stats["added"] + stats["updated"] + stats["unchanged"]
        logger.info("=== PRODUCT SYNC COMPLETED ===")
        logger.info(f"Total products processed: {total_processed}")
        logger.info(f"New products added: {stats['added']}")
        logger.info(f"Existing products updated: {stats['updated']}")
        logger.info(f"Products unchanged: {stats['unchanged']}")
        logger.info("============================")

        return stats

    async def _sync_orders_full(self, db: AsyncSession) -> Dict[str, int]:
        """Sync all orders from store with per-page processing."""
        stats = {"added": 0, "updated": 0, "unchanged": 0}

        # Define callback to process each page immediately
        async def process_page(edges):
            nonlocal stats
            for edge in edges:
                order_data = edge["node"]
                try:
                    result = await self.db_service.sync_order_to_db(
                        db,
                        order_data
                    )

                    if result["action"] == "created":
                        stats["added"] += 1
                    elif result["action"] == "exists":
                        stats["unchanged"] += 1

                except Exception as e:
                    logger.error(f"Failed to sync order {order_data.get('id', 'unknown')}: {e}")
                    continue

        # Paginate with per-page processing
        response = await self._paginate_all(
            self.store_service.get_orders,
            "orders",
            process_callback=process_page
        )

        if "errors" in response:
            logger.error(f"Store API errors during order sync: {response['errors']}")
            return {"added": 0, "updated": 0, "unchanged": 0, "errors": response["errors"]}

        # Enhanced logging
        total_processed = stats["added"] + stats["updated"] + stats["unchanged"]
        logger.info("=== ORDER SYNC COMPLETED ===")
        logger.info(f"Total orders processed: {total_processed}")
        logger.info(f"New orders added: {stats['added']}")
        logger.info(f"Existing orders updated: {stats['updated']}")
        logger.info(f"Orders unchanged: {stats['unchanged']}")
        logger.info("==========================")

        return stats

    async def get_shop_info(self) -> Dict[str, Any]:
        """Get comprehensive shop information."""
        return await self.store_service.get_shop_info()

    async def get_products(self, limit: int = 50, offset: int = 0) -> Dict[str, Any]:
        """Get products with pagination."""
        return await self.store_service.get_products(limit=limit, offset=offset)

    async def get_orders(self, limit: int = 50, offset: int = 0) -> Dict[str, Any]:
        """Get orders with pagination."""
        return await self.store_service.get_orders(limit=limit, offset=offset)

    async def test_connection(self) -> Dict[str, Any]:
        """Test store API connections."""
        return await self.store_service.test_connection()

    async def _paginate_all(self, get_func, item_type: str, process_callback=None) -> Dict[str, Any]:
        """Generic pagination helper for fetching all items with optional per-page processing"""
        all_edges = []
        offset = 0
        limit = 50
        total_processed = 0

        logger.info(f"Starting full {item_type} fetch with pagination")

        while True:
            result = await get_func(limit=limit, offset=offset)

            if "errors" in result:
                logger.error(f"Error fetching {item_type} page: {result['errors']}")
                return {"errors": result["errors"], "edges": all_edges}

            edges = result.get("edges", [])
            page_info = result.get("pageInfo", {})

            if not edges:
                break

            # Process this page immediately if callback provided
            if process_callback:
                await process_callback(edges)
                total_processed += len(edges)
                logger.info(f"Processed {len(edges)} {item_type} (running total: {total_processed})")
            else:
                # Add edges to our collection for traditional behavior
                all_edges.extend(edges)

            offset += len(edges)

            # Check if there are more pages
            if not page_info.get("hasNextPage", False):
                break

        logger.info(f"Completed full {item_type} fetch: {total_processed if process_callback else len(all_edges)} {item_type} total")

        # Enhanced logging for sync summary
        if item_type == "products":
            logger.info("=== STORE PRODUCTS FETCH SUMMARY ===")
            logger.info(f"Total products fetched from store: {total_processed if process_callback else len(all_edges)}")
            logger.info("===================================")
        elif item_type == "orders":
            logger.info("=== STORE ORDERS FETCH SUMMARY ===")
            logger.info(f"Total orders fetched from store: {total_processed if process_callback else len(all_edges)}")
            logger.info("=================================")

        return {
            "edges": all_edges,
            "pageInfo": {
                "hasNextPage": False,
                "endCursor": None
            }
        }