"""
Web scraper module for e-commerce data extraction.
"""

from .models import (
    ScrapedDocument,
    ScrapingJob,
    ScrapedProduct,
    ScrapedCollection,
    ScrapingPlatform,
    ScrapingStatus,
    JobStatus
)

from .schemas import (
    StartScrapingRequest,
    StartScrapingResponse,
    ValidateUrlRequest,
    ValidateUrlResponse,
    ScrapedDocumentInfo,
    ScrapingJobInfo,
    ScrapedProductInfo,
    ScrapedCollectionInfo,
    ScraperStatsResponse,
    DocumentsListResponse,
    JobsListResponse,
    ProductsListResponse,
    CollectionsListResponse,
    ProductCountResponse,
    ImportDataRequest,
    ImportDataResponse,
    PlatformInfo,
    PlatformsListResponse
)

from .service import scraper_service
from .router import router

__all__ = [
    # Models
    "ScrapedDocument",
    "ScrapingJob", 
    "ScrapedProduct",
    "ScrapedCollection",
    "ScrapingPlatform",
    "ScrapingStatus",
    "JobStatus",
    
    # Schemas
    "StartScrapingRequest",
    "StartScrapingResponse",
    "ValidateUrlRequest", 
    "ValidateUrlResponse",
    "ScrapedDocumentInfo",
    "ScrapingJobInfo",
    "ScrapedProductInfo",
    "ScrapedCollectionInfo",
    "ScraperStatsResponse",
    "DocumentsListResponse",
    "JobsListResponse",
    "ProductsListResponse",
    "CollectionsListResponse",
    "ProductCountResponse",
    "ImportDataRequest",
    "ImportDataResponse",
    "PlatformInfo",
    "PlatformsListResponse",
    
    # Service
    "scraper_service",
    
    # Router
    "router"
]
