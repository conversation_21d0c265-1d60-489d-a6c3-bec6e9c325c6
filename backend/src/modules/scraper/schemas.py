"""
Web scraper API schemas.
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, <PERSON>, validator
from enum import Enum


class ScrapingStatusEnum(str, Enum):
    """Scraping status enumeration."""
    PENDING = "pending"
    SCRAPING = "scraping"
    COMPLETED = "completed"
    FAILED = "failed"


class JobStatusEnum(str, Enum):
    """Job status enumeration."""
    QUEUED = "queued"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class ScrapingOptions(BaseModel):
    """Options for scraping configuration."""
    deep_scrape: bool = False
    max_pages: int = 10
    include_variants: bool = True
    include_images: bool = True


class StartScrapingRequest(BaseModel):
    """Request to start scraping a URL."""
    url: str
    deep_scrape: Optional[bool] = False
    max_pages: Optional[int] = 10
    include_variants: Optional[bool] = True
    include_images: Optional[bool] = True


class StartScrapingResponse(BaseModel):
    """Response from starting scraping."""
    job_id: str
    message: str


class ValidateUrlRequest(BaseModel):
    """Request to validate a URL."""
    url: str


class ValidateUrlResponse(BaseModel):
    """Response from URL validation."""
    valid: bool
    domain: str
    platform: Optional[str] = None
    estimated_products: Optional[int] = None
    warnings: Optional[List[str]] = None
    errors: Optional[List[str]] = None


class ScrapedDocumentInfo(BaseModel):
    """Scraped document information."""
    id: str
    url: str
    domain: str
    title: Optional[str]
    status: ScrapingStatusEnum
    progress: float
    product_count: int
    collection_count: int
    created_at: str
    updated_at: str
    error: Optional[str] = None


class ScrapingJobInfo(BaseModel):
    """Scraping job information."""
    id: str
    url: str
    status: JobStatusEnum
    progress: float
    started_at: Optional[str] = None
    completed_at: Optional[str] = None
    error: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class ScrapedProductImage(BaseModel):
    """Scraped product image."""
    id: str
    src_url: str
    alt: Optional[str] = None
    position: Optional[int] = None


class ScrapedProductVariant(BaseModel):
    """Scraped product variant."""
    id: str
    title: str
    price: str
    compare_at_price: Optional[str] = None
    sku: Optional[str] = None
    inventory_quantity: Optional[int] = None


class ScrapedProductCollection(BaseModel):
    """Scraped product collection."""
    id: str
    name: str
    slug: str


class ScrapedProductInfo(BaseModel):
    """Scraped product information."""
    id: str
    product_id: str
    title: str
    handle: Optional[str]
    description: Optional[str] = None
    vendor: Optional[str] = None
    product_type: Optional[str] = None
    tags: Optional[List[str]] = None
    images: List[ScrapedProductImage]
    variants: List[ScrapedProductVariant]
    collections: List[ScrapedProductCollection]
    url: str
    domain: str
    scraped_at: str


class ScrapedCollectionInfo(BaseModel):
    """Scraped collection information."""
    id: str
    slug: str
    title: str
    description: Optional[str] = None
    image_url: Optional[str] = None
    product_count: int
    domain: str
    scraped_at: str


class ScraperStatsResponse(BaseModel):
    """Scraper statistics response."""
    total_documents: int
    total_products: int
    total_domains: int
    active_jobs: int
    domains: List[Dict[str, Any]]


class DocumentsListResponse(BaseModel):
    """Response for documents listing."""
    items: List[ScrapedDocumentInfo]
    total: int
    has_more: bool


class JobsListResponse(BaseModel):
    """Response for jobs listing."""
    items: List[ScrapingJobInfo]


class ProductsListResponse(BaseModel):
    """Response for products listing."""
    items: List[ScrapedProductInfo]
    next_cursor: Optional[str] = None
    total_count: Optional[int] = None


class CollectionsListResponse(BaseModel):
    """Response for collections listing."""
    items: List[ScrapedCollectionInfo]
    total: int


class ProductCountResponse(BaseModel):
    """Response for product count."""
    count: int
    meta: Optional[Dict[str, Any]] = None


class ImportDataRequest(BaseModel):
    """Request to import scraped data."""
    document_id: str
    shop_id: Optional[str] = None
    collection_mapping: Optional[Dict[str, str]] = None


class ImportDataResponse(BaseModel):
    """Response from data import."""
    imported_products: int
    imported_collections: int
    skipped: int
    errors: List[str]


class PlatformInfo(BaseModel):
    """Platform information."""
    name: str
    domains: List[str]
    features: List[str]
    limitations: Optional[List[str]] = None


class PlatformsListResponse(BaseModel):
    """Response for platforms listing."""
    items: List[PlatformInfo]
