"""
Web scraper API router.
"""

import logging
from typing import Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from core.db.database import get_db
from modules.auth.models import User, Tenant
from modules.auth.router import get_current_user
from modules.scraper.service import scraper_service
from modules.scraper.schemas import (
    StartScrapingRequest, StartScrapingResponse, ValidateUrlRequest, ValidateUrlResponse,
    ScraperStatsResponse, DocumentsListResponse, JobsListResponse,
    ProductsListResponse, CollectionsListResponse, ProductCountResponse,
    ImportDataRequest, ImportDataResponse, PlatformsListResponse
)

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/stats", response_model=ScraperStatsResponse)
async def get_scraper_stats(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get scraper statistics."""
    try:
        # Get user's tenant
        tenant_result = await db.execute(select(Tenant).filter(Tenant.owner_id == current_user.id))
        tenant = tenant_result.scalar_one_or_none()
        if not tenant:
            raise HTTPException(
                status_code=404,
                detail="Tenant not found"
            )

        stats = await scraper_service.get_stats(db, tenant.id)
        return stats
        
    except Exception as e:
        logger.error(f"Error getting scraper stats: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/documents", response_model=DocumentsListResponse)
async def get_documents(
    limit: int = Query(20, ge=1, le=100),
    offset: int = Query(0, ge=0),
    domain: Optional[str] = Query(None),
    status: Optional[str] = Query(None),
    search: Optional[str] = Query(None),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get scraped documents."""
    try:
        # Get user's tenant
        tenant_result = await db.execute(select(Tenant).filter(Tenant.owner_id == current_user.id))
        tenant = tenant_result.scalar_one_or_none()
        if not tenant:
            raise HTTPException(
                status_code=404,
                detail="Tenant not found"
            )

        result = await scraper_service.get_documents(
            db=db,
            workspace_id=tenant.id,
            limit=limit,
            offset=offset,
            domain=domain,
            status=status,
            search=search
        )
        
        return DocumentsListResponse(**result)
        
    except Exception as e:
        logger.error(f"Error getting documents: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/jobs", response_model=JobsListResponse)
async def get_active_jobs(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get active scraping jobs."""
    try:
        # Get user's tenant
        tenant_result = await db.execute(select(Tenant).filter(Tenant.owner_id == current_user.id))
        tenant = tenant_result.scalar_one_or_none()
        if not tenant:
            raise HTTPException(
                status_code=404,
                detail="Tenant not found"
            )

        result = await scraper_service.get_active_jobs(db, tenant.id)
        return JobsListResponse(**result)
        
    except Exception as e:
        logger.error(f"Error getting active jobs: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/validate", response_model=ValidateUrlResponse)
async def validate_url(
    request: ValidateUrlRequest,
    current_user: User = Depends(get_current_user),
):
    """Validate a URL for scraping."""
    try:
        result = await scraper_service.validate_url(request.url)
        return result
        
    except Exception as e:
        logger.error(f"Error validating URL: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/scrape", response_model=StartScrapingResponse)
async def start_scraping(
    request: StartScrapingRequest,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Start scraping a URL."""
    try:
        # Get user's tenant
        tenant_result = await db.execute(select(Tenant).filter(Tenant.owner_id == current_user.id))
        tenant = tenant_result.scalar_one_or_none()
        if not tenant:
            raise HTTPException(
                status_code=404,
                detail="Tenant not found"
            )

        result = await scraper_service.start_scraping(
            db=db,
            workspace_id=tenant.id,
            request=request
        )
        
        # TODO: Queue background scraping task
        # background_tasks.add_task(scraper_service.process_scraping_job, db, result["job_id"])
        
        return StartScrapingResponse(**result)
        
    except Exception as e:
        logger.error(f"Error starting scraping: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/jobs/{job_id}")
async def get_job_status(
    job_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get job status."""
    try:
        # Get user's tenant
        tenant_result = await db.execute(select(Tenant).filter(Tenant.owner_id == current_user.id))
        tenant = tenant_result.scalar_one_or_none()
        if not tenant:
            raise HTTPException(
                status_code=404,
                detail="Tenant not found"
            )

        # TODO: Implement get_job_status in service
        return {
            "id": job_id,
            "status": "running",
            "progress": 50.0,
            "message": "Job status endpoint not fully implemented yet"
        }
        
    except Exception as e:
        logger.error(f"Error getting job status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/jobs/{job_id}/cancel")
async def cancel_job(
    job_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Cancel a scraping job."""
    try:
        # Get user's tenant
        tenant_result = await db.execute(select(Tenant).filter(Tenant.owner_id == current_user.id))
        tenant = tenant_result.scalar_one_or_none()
        if not tenant:
            raise HTTPException(
                status_code=404,
                detail="Tenant not found"
            )

        # TODO: Implement cancel_job in service
        return {"message": "Job cancellation not fully implemented yet"}
        
    except Exception as e:
        logger.error(f"Error cancelling job: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/documents/{document_id}")
async def delete_document(
    document_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Delete a scraped document."""
    try:
        # Get user's tenant
        tenant_result = await db.execute(select(Tenant).filter(Tenant.owner_id == current_user.id))
        tenant = tenant_result.scalar_one_or_none()
        if not tenant:
            raise HTTPException(
                status_code=404,
                detail="Tenant not found"
            )

        # TODO: Implement delete_document in service
        return {"message": "Document deletion not fully implemented yet"}
        
    except Exception as e:
        logger.error(f"Error deleting document: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/products")
async def get_scraped_products(
    limit: int = Query(20, ge=1, le=100),
    cursor: Optional[str] = Query(None),
    domain: Optional[str] = Query(None),
    collections: Optional[str] = Query(None),
    search: Optional[str] = Query(None),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get scraped products."""
    try:
        # Get user's tenant
        tenant_result = await db.execute(select(Tenant).filter(Tenant.owner_id == current_user.id))
        tenant = tenant_result.scalar_one_or_none()
        if not tenant:
            raise HTTPException(
                status_code=404,
                detail="Tenant not found"
            )

        # TODO: Implement get_scraped_products in service
        return {
            "items": [],
            "next_cursor": None,
            "total_count": 0
        }
        
    except Exception as e:
        logger.error(f"Error getting scraped products: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/collections")
async def get_scraped_collections(
    limit: int = Query(20, ge=1, le=100),
    offset: int = Query(0, ge=0),
    domain: Optional[str] = Query(None),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get scraped collections."""
    try:
        # Get user's tenant
        tenant_result = await db.execute(select(Tenant).filter(Tenant.owner_id == current_user.id))
        tenant = tenant_result.scalar_one_or_none()
        if not tenant:
            raise HTTPException(
                status_code=404,
                detail="Tenant not found"
            )

        # TODO: Implement get_scraped_collections in service
        return {
            "items": [],
            "total": 0
        }
        
    except Exception as e:
        logger.error(f"Error getting scraped collections: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/products/count")
async def get_scraped_product_count(
    domain: Optional[str] = Query(None),
    collections: Optional[str] = Query(None),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get scraped product count."""
    try:
        # Get user's tenant
        tenant_result = await db.execute(select(Tenant).filter(Tenant.owner_id == current_user.id))
        tenant = tenant_result.scalar_one_or_none()
        if not tenant:
            raise HTTPException(
                status_code=404,
                detail="Tenant not found"
            )

        # TODO: Implement get_scraped_product_count in service
        return {
            "count": 0,
            "meta": {
                "total_products": 0,
                "domains": []
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting scraped product count: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/import")
async def import_scraped_data(
    request: ImportDataRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Import scraped data to main database."""
    try:
        # Get user's tenant
        tenant_result = await db.execute(select(Tenant).filter(Tenant.owner_id == current_user.id))
        tenant = tenant_result.scalar_one_or_none()
        if not tenant:
            raise HTTPException(
                status_code=404,
                detail="Tenant not found"
            )

        # TODO: Implement import_scraped_data in service
        return ImportDataResponse(
            imported_products=0,
            imported_collections=0,
            skipped=0,
            errors=["Import functionality not fully implemented yet"]
        )
        
    except Exception as e:
        logger.error(f"Error importing scraped data: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/platforms", response_model=PlatformsListResponse)
async def get_supported_platforms(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get supported scraping platforms."""
    try:
        platforms = await scraper_service.get_supported_platforms(db)
        return PlatformsListResponse(items=platforms)
        
    except Exception as e:
        logger.error(f"Error getting supported platforms: {e}")
        raise HTTPException(status_code=500, detail=str(e))
