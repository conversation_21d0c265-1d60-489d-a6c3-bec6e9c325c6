"""
Authentication router for ProductVideo platform.
Handles login, registration, password reset, and OAuth.
"""

import logging
from fastapi import APIRouter, Depends, HTTPException, status, Request, Response
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession

from core.db.database import get_db
from core.config import get_settings
from .schemas import (
    LoginRequest,
    TokenResponse,
    ForgotPasswordRequest,
    ResetPasswordRequest,
    RegisterRequest,
    UserResponse,
    VerifyEmailRequest,
    ResendVerificationRequest
)
from .service import auth_service
from .oauth_router import router as oauth_router
from modules.billing.service import billing_service
from modules.billing.schemas import TenantCreate
from modules.billing.models import BillingPlan

settings = get_settings()
logger = logging.getLogger(__name__)
router = APIRouter()
security = HTTPBearer()

# Include OAuth routes
router.include_router(oauth_router)


@router.post("/register", response_model=UserResponse)
async def register(
    request: RegisterRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    Register a new user.
    
    Args:
        request: Registration request data
        db: Database session
        
    Returns:
        Created user information
    """
    try:
        user = await auth_service.create_user(db, request)

        # Create tenant for the new user
        try:
            tenant_name = f"{request.first_name or 'User'}'s Workspace" if request.first_name else f"User {user.id}'s Workspace"
            tenant_slug = f"user-{user.id}"

            tenant_data = TenantCreate(
                name=tenant_name,
                slug=tenant_slug,
                billing_email=request.email,
                billing_plan=BillingPlan.STARTER,  # Default to starter plan
                is_active=True
            )

            tenant = await billing_service.create_tenant(db, tenant_data, create_stripe_customer=False)
            logger.info(f"Created tenant {tenant.id} for user {user.id}")

        except Exception as tenant_error:
            logger.error(f"Failed to create tenant for user {user.id}: {tenant_error}")
            # Don't fail registration if tenant creation fails
            # User can still use basic features and create tenant later

        return UserResponse.from_orm(user)

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create user"
        )


@router.post("/login", response_model=TokenResponse)
async def login(
    request: LoginRequest,
    response: Response,
    db: AsyncSession = Depends(get_db)
):
    """
    Authenticate user and return JWT tokens.
    
    Args:
        request: Login credentials
        response: HTTP response for setting cookies
        db: Database session
        
    Returns:
        JWT access and refresh tokens
    """
    user = await auth_service.authenticate_user(db, request.email, request.password)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password"
        )
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Account is disabled"
        )
    
    # Generate tokens
    access_token = auth_service.create_access_token(
        data={"sub": str(user.id), "email": user.email}
    )
    refresh_token = auth_service.create_refresh_token(
        data={"sub": str(user.id)}
    )
    
    # Set secure HTTP-only cookies
    response.set_cookie(
        key="access_token",
        value=access_token,
        httponly=True,
        secure=settings.ENVIRONMENT == "production",
        samesite="lax",
        max_age=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
    )
    
    response.set_cookie(
        key="refresh_token",
        value=refresh_token,
        httponly=True,
        secure=settings.ENVIRONMENT == "production",
        samesite="lax",
        max_age=settings.REFRESH_TOKEN_EXPIRE_DAYS * 24 * 60 * 60
    )
    
    # Update last login
    await auth_service.update_last_login(db, user.id)
    
    return TokenResponse(
        access_token=access_token,
        token_type="bearer"
    )


@router.post("/logout")
async def logout(
    response: Response,
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_db)
):
    """
    Logout user and revoke tokens.
    
    Args:
        response: HTTP response for clearing cookies
        credentials: JWT token from Authorization header
        db: Database session
        
    Returns:
        Success message
    """
    # Clear cookies
    response.delete_cookie(key="access_token")
    response.delete_cookie(key="refresh_token")
    
    return {"message": "Successfully logged out"}


@router.post("/refresh", response_model=TokenResponse)
async def refresh_token(
    request: Request,
    response: Response,
    db: AsyncSession = Depends(get_db)
):
    """
    Refresh access token using refresh token.
    
    Args:
        request: HTTP request containing refresh token cookie
        response: HTTP response for setting new cookies
        db: Database session
        
    Returns:
        New JWT tokens
    """
    refresh_token = request.cookies.get("refresh_token")
    
    if not refresh_token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Refresh token not found"
        )
    
    # Verify refresh token
    payload = auth_service.verify_token(refresh_token, token_type="refresh")
    if not payload:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid refresh token"
        )
    
    user_id = payload.get("sub")
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token payload"
        )
    
    # Get user
    user = await auth_service.get_user_by_id(db, int(user_id))
    if not user or not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found or inactive"
        )
    
    # Generate new tokens
    access_token = auth_service.create_access_token(
        data={"sub": str(user.id), "email": user.email}
    )
    new_refresh_token = auth_service.create_refresh_token(
        data={"sub": str(user.id)}
    )
    
    # Set new cookies
    response.set_cookie(
        key="access_token",
        value=access_token,
        httponly=True,
        secure=settings.ENVIRONMENT == "production",
        samesite="lax",
        max_age=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
    )
    
    response.set_cookie(
        key="refresh_token",
        value=new_refresh_token,
        httponly=True,
        secure=settings.ENVIRONMENT == "production",
        samesite="lax",
        max_age=settings.REFRESH_TOKEN_EXPIRE_DAYS * 24 * 60 * 60
    )
    
    return TokenResponse(
        access_token=access_token,
        token_type="bearer"
    )


@router.get("/me", response_model=UserResponse)
async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_db)
):
    """
    Get current user information.
    
    Args:
        credentials: JWT token from Authorization header
        db: Database session
        
    Returns:
        Current user information
    """
    user = await auth_service.get_current_user(db, credentials.credentials)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials"
        )
    
    return UserResponse.from_orm(user)


@router.post("/forgot-password")
async def forgot_password(
    request: ForgotPasswordRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    Send password reset email.

    Args:
        request: Email for password reset
        db: Database session

    Returns:
        Success message
    """
    try:
        await auth_service.initiate_password_reset(db, request.email)
        # Always return success to prevent email enumeration
        return {"message": "If the email exists, a password reset link has been sent"}
    except Exception as e:
        # Log the error but still return success to prevent email enumeration
        print(f"Error initiating password reset: {e}")
        return {"message": "If the email exists, a password reset link has been sent"}


@router.post("/reset-password")
async def reset_password(
    request: ResetPasswordRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    Reset password using reset token.

    Args:
        request: Reset token and new password
        db: Database session

    Returns:
        Success message
    """
    try:
        success = await auth_service.reset_password(db, request.token, request.new_password)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid or expired reset token"
            )

        return {"message": "Password has been reset successfully"}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to reset password"
        )


@router.post("/verify-email")
async def verify_email(
    request: VerifyEmailRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    Verify email address using verification token.
    
    Args:
        request: Email verification token
        db: Database session
        
    Returns:
        Success message
    """
    # Implementation would verify token and mark email as verified
    return {"message": "Email has been verified successfully"}


@router.post("/resend-verification")
async def resend_verification(
    request: ResendVerificationRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    Resend email verification.
    
    Args:
        request: Email to resend verification to
        db: Database session
        
    Returns:
        Success message
    """
    # Always return success to prevent email enumeration
    return {"message": "If the email exists, a verification email has been sent"}
