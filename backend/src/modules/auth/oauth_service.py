"""
OAuth service for social login integration.
Supports Google, GitHub, and Shopify OAuth flows.
"""

import json
import secrets
from typing import Optional, Dict, Any, Tu<PERSON>
from datetime import datetime, timedelta
from urllib.parse import urlencode

import httpx
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from core.config import get_settings
from core.db.database import get_db
from .models import User, OAuthAccount, OAuthProvider
from .schemas import UserCreate, OAuthUserInfo
from .service import AuthService

settings = get_settings()


class OAuthService:
    """Service for handling OAuth authentication flows."""
    
    def __init__(self):
        self.auth_service = AuthService()
        
        # OAuth provider configurations
        self.providers = {
            OAuthProvider.GOOGLE: {
                "client_id": settings.GOOGLE_CLIENT_ID,
                "client_secret": settings.GOOGLE_CLIENT_SECRET,
                "auth_url": "https://accounts.google.com/o/oauth2/v2/auth",
                "token_url": "https://oauth2.googleapis.com/token",
                "user_info_url": "https://www.googleapis.com/oauth2/v2/userinfo",
                "scope": "openid email profile",
            },
            OAuthProvider.GITHUB: {
                "client_id": settings.GITHUB_CLIENT_ID,
                "client_secret": settings.GITHUB_CLIENT_SECRET,
                "auth_url": "https://github.com/login/oauth/authorize",
                "token_url": "https://github.com/login/oauth/access_token",
                "user_info_url": "https://api.github.com/user",
                "scope": "user:email",
            },
            OAuthProvider.SHOPIFY: {
                "client_id": settings.SHOPIFY_API_KEY,
                "client_secret": settings.SHOPIFY_API_SECRET,
                "auth_url": "https://{shop}.myshopify.com/admin/oauth/authorize",
                "token_url": "https://{shop}.myshopify.com/admin/oauth/access_token",
                "scope": "read_products,write_products,read_product_listings,write_files,read_orders",
            }
        }
    
    def get_authorization_url(
        self, 
        provider: OAuthProvider, 
        redirect_uri: str,
        shop_domain: Optional[str] = None
    ) -> Tuple[str, str]:
        """
        Generate OAuth authorization URL and state.
        
        Args:
            provider: OAuth provider
            redirect_uri: Callback URL
            shop_domain: Shopify shop domain (required for Shopify)
            
        Returns:
            Tuple of (authorization_url, state)
        """
        if provider not in self.providers:
            raise ValueError(f"Unsupported OAuth provider: {provider}")
        
        config = self.providers[provider]
        state = secrets.token_urlsafe(32)
        
        params = {
            "client_id": config["client_id"],
            "redirect_uri": redirect_uri,
            "scope": config["scope"],
            "state": state,
            "response_type": "code",
        }
        
        # Provider-specific parameters
        if provider == OAuthProvider.GOOGLE:
            params["access_type"] = "offline"
            params["prompt"] = "consent"
        
        auth_url = config["auth_url"]
        
        # Handle Shopify shop-specific URLs
        if provider == OAuthProvider.SHOPIFY and shop_domain:
            auth_url = auth_url.format(shop=shop_domain)
        
        authorization_url = f"{auth_url}?{urlencode(params)}"
        
        return authorization_url, state
    
    async def exchange_code_for_token(
        self,
        provider: OAuthProvider,
        code: str,
        redirect_uri: str,
        shop_domain: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Exchange authorization code for access token.
        
        Args:
            provider: OAuth provider
            code: Authorization code
            redirect_uri: Callback URL
            shop_domain: Shopify shop domain
            
        Returns:
            Token response data
        """
        if provider not in self.providers:
            raise ValueError(f"Unsupported OAuth provider: {provider}")
        
        config = self.providers[provider]
        
        token_data = {
            "client_id": config["client_id"],
            "client_secret": config["client_secret"],
            "code": code,
            "redirect_uri": redirect_uri,
        }
        
        # Provider-specific parameters
        if provider in [OAuthProvider.GOOGLE, OAuthProvider.GITHUB]:
            token_data["grant_type"] = "authorization_code"
        
        token_url = config["token_url"]
        
        # Handle Shopify shop-specific URLs
        if provider == OAuthProvider.SHOPIFY and shop_domain:
            token_url = token_url.format(shop=shop_domain)
        
        async with httpx.AsyncClient() as client:
            headers = {"Accept": "application/json"}
            
            if provider == OAuthProvider.GITHUB:
                headers["Accept"] = "application/vnd.github.v3+json"
            
            response = await client.post(
                token_url,
                data=token_data,
                headers=headers
            )
            response.raise_for_status()
            
            return response.json()
    
    async def get_user_info(
        self,
        provider: OAuthProvider,
        access_token: str,
        shop_domain: Optional[str] = None
    ) -> OAuthUserInfo:
        """
        Get user information from OAuth provider.
        
        Args:
            provider: OAuth provider
            access_token: Access token
            shop_domain: Shopify shop domain
            
        Returns:
            User information
        """
        if provider not in self.providers:
            raise ValueError(f"Unsupported OAuth provider: {provider}")
        
        config = self.providers[provider]
        
        async with httpx.AsyncClient() as client:
            headers = {"Authorization": f"Bearer {access_token}"}
            
            if provider == OAuthProvider.SHOPIFY:
                # For Shopify, we get shop info instead of user info
                user_info_url = f"https://{shop_domain}.myshopify.com/admin/api/2023-10/shop.json"
            else:
                user_info_url = config["user_info_url"]
            
            response = await client.get(user_info_url, headers=headers)
            response.raise_for_status()
            
            data = response.json()
            
            # Parse user info based on provider
            if provider == OAuthProvider.GOOGLE:
                return OAuthUserInfo(
                    provider_user_id=data["id"],
                    email=data.get("email"),
                    first_name=data.get("given_name"),
                    last_name=data.get("family_name"),
                    avatar_url=data.get("picture"),
                    username=None,
                    provider_data=data
                )
            
            elif provider == OAuthProvider.GITHUB:
                # Get email separately for GitHub
                email = data.get("email")
                if not email:
                    email_response = await client.get(
                        "https://api.github.com/user/emails",
                        headers=headers
                    )
                    if email_response.status_code == 200:
                        emails = email_response.json()
                        primary_email = next(
                            (e for e in emails if e.get("primary")), 
                            emails[0] if emails else None
                        )
                        if primary_email:
                            email = primary_email["email"]
                
                return OAuthUserInfo(
                    provider_user_id=str(data["id"]),
                    email=email,
                    first_name=data.get("name", "").split(" ")[0] if data.get("name") else None,
                    last_name=" ".join(data.get("name", "").split(" ")[1:]) if data.get("name") and " " in data.get("name", "") else None,
                    avatar_url=data.get("avatar_url"),
                    username=data.get("login"),
                    provider_data=data
                )
            
            elif provider == OAuthProvider.SHOPIFY:
                shop_data = data.get("shop", {})
                return OAuthUserInfo(
                    provider_user_id=str(shop_data.get("id")),
                    email=shop_data.get("email"),
                    first_name=shop_data.get("shop_owner"),
                    last_name=None,
                    avatar_url=None,
                    username=shop_data.get("name"),
                    provider_data=shop_data
                )
    
    async def authenticate_or_create_user(
        self,
        db: AsyncSession,
        provider: OAuthProvider,
        user_info: OAuthUserInfo,
        token_data: Dict[str, Any]
    ) -> User:
        """
        Authenticate existing user or create new user from OAuth.
        
        Args:
            db: Database session
            provider: OAuth provider
            user_info: User information from provider
            token_data: Token data from OAuth exchange
            
        Returns:
            User instance
        """
        # Check if OAuth account already exists
        stmt = select(OAuthAccount).where(
            OAuthAccount.provider == provider.value,
            OAuthAccount.provider_user_id == user_info.provider_user_id
        )
        result = await db.execute(stmt)
        oauth_account = result.scalar_one_or_none()
        
        if oauth_account:
            # Update existing OAuth account
            oauth_account.access_token = token_data.get("access_token")
            oauth_account.refresh_token = token_data.get("refresh_token")
            oauth_account.provider_data = json.dumps(user_info.provider_data)
            oauth_account.last_used_at = datetime.utcnow()
            
            if token_data.get("expires_in"):
                oauth_account.token_expires_at = datetime.utcnow() + timedelta(
                    seconds=int(token_data["expires_in"])
                )
            
            await db.commit()
            return oauth_account.user
        
        # Check if user exists by email
        user = None
        if user_info.email:
            stmt = select(User).where(User.email == user_info.email)
            result = await db.execute(stmt)
            user = result.scalar_one_or_none()
        
        if not user:
            # Create new user
            user_create = UserCreate(
                email=user_info.email or f"{user_info.provider_user_id}@{provider.value}.local",
                first_name=user_info.first_name,
                last_name=user_info.last_name,
                username=user_info.username,
                avatar_url=user_info.avatar_url,
                is_verified=True  # OAuth users are pre-verified
            )
            
            user = await self.auth_service.create_user(db, user_create)
        
        # Create OAuth account
        oauth_account = OAuthAccount(
            user_id=user.id,
            provider=provider.value,
            provider_user_id=user_info.provider_user_id,
            provider_username=user_info.username,
            provider_email=user_info.email,
            access_token=token_data.get("access_token"),
            refresh_token=token_data.get("refresh_token"),
            provider_data=json.dumps(user_info.provider_data)
        )
        
        if token_data.get("expires_in"):
            oauth_account.token_expires_at = datetime.utcnow() + timedelta(
                seconds=int(token_data["expires_in"])
            )
        
        db.add(oauth_account)
        await db.commit()
        
        return user
    
    async def refresh_token(
        self,
        db: AsyncSession,
        oauth_account: OAuthAccount
    ) -> Optional[str]:
        """
        Refresh OAuth access token.
        
        Args:
            db: Database session
            oauth_account: OAuth account to refresh
            
        Returns:
            New access token or None if refresh failed
        """
        if not oauth_account.refresh_token:
            return None
        
        provider = OAuthProvider(oauth_account.provider)
        
        if provider not in self.providers:
            return None
        
        config = self.providers[provider]
        
        refresh_data = {
            "client_id": config["client_id"],
            "client_secret": config["client_secret"],
            "refresh_token": oauth_account.refresh_token,
            "grant_type": "refresh_token",
        }
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    config["token_url"],
                    data=refresh_data,
                    headers={"Accept": "application/json"}
                )
                response.raise_for_status()
                
                token_data = response.json()
                
                # Update OAuth account
                oauth_account.access_token = token_data.get("access_token")
                if token_data.get("refresh_token"):
                    oauth_account.refresh_token = token_data["refresh_token"]
                
                if token_data.get("expires_in"):
                    oauth_account.token_expires_at = datetime.utcnow() + timedelta(
                        seconds=int(token_data["expires_in"])
                    )
                
                await db.commit()
                
                return token_data.get("access_token")
        
        except Exception as e:
            print(f"Failed to refresh token: {e}")
            return None


oauth_service = OAuthService()
