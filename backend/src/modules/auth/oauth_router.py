"""
OAuth authentication router for social login.
Handles Google, GitHub, and Shopify OAuth flows.
"""

import logging
from fastapi import <PERSON><PERSON>out<PERSON>, Depends, HTTPException, Request, Response
from fastapi.responses import RedirectResponse
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession

from core.db.database import get_db
from core.config import get_settings
from .oauth_service import oauth_service, OAuthProvider
from .schemas import (
    OAuthAuthorizationRequest,
    OAuthCallbackRequest,
    TokenResponse,
    UserResponse
)
from .service import auth_service
from .models import User

# Add logging
logger = logging.getLogger(__name__)

settings = get_settings()
router = APIRouter()
security = HTTPBearer()


async def get_current_user_dependency(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_db)
) -> User:
    """
    Dependency to get current authenticated user.
    Extracts token from Authorization header and validates it.
    """
    logger.info("Resolving current user dependency")
    try:
        user = await auth_service.get_current_user(db, credentials.credentials)
        if not user:
            logger.warning("Invalid authentication credentials")
            raise HTTPException(
                status_code=401,
                detail="Invalid authentication credentials"
            )
        logger.info(f"Authenticated user: {user.email}")
        return user
    except Exception as e:
        logger.error(f"Error in get_current_user_dependency: {e}")
        raise


@router.post("/authorize")
async def get_oauth_authorization_url(
    request: OAuthAuthorizationRequest
) -> dict:
    """
    Get OAuth authorization URL for a provider.
    
    Args:
        request: OAuth authorization request
        
    Returns:
        Authorization URL and state
    """
    try:
        provider = OAuthProvider(request.provider)
    except ValueError:
        raise HTTPException(
            status_code=400,
            detail=f"Unsupported OAuth provider: {request.provider}"
        )
    
    try:
        auth_url, state = oauth_service.get_authorization_url(
            provider=provider,
            redirect_uri=request.redirect_uri,
            shop_domain=request.shop_domain
        )
        
        return {
            "authorization_url": auth_url,
            "state": state,
            "provider": request.provider
        }
    
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to generate authorization URL: {str(e)}"
        )


@router.post("/callback")
async def oauth_callback(
    request: OAuthCallbackRequest,
    response: Response,
    db = Depends(get_db)
) -> TokenResponse:
    """
    Handle OAuth callback and authenticate user.
    
    Args:
        request: OAuth callback request
        response: HTTP response for setting cookies
        db: Database session
        
    Returns:
        JWT tokens
    """
    try:
        provider = OAuthProvider(request.provider)
    except ValueError:
        raise HTTPException(
            status_code=400,
            detail=f"Unsupported OAuth provider: {request.provider}"
        )
    
    try:
        # Exchange code for token
        token_data = await oauth_service.exchange_code_for_token(
            provider=provider,
            code=request.code,
            redirect_uri=request.redirect_uri,
            shop_domain=request.shop_domain
        )
        
        # Get user info from provider
        user_info = await oauth_service.get_user_info(
            provider=provider,
            access_token=token_data["access_token"],
            shop_domain=request.shop_domain
        )
        
        # Authenticate or create user
        user = await oauth_service.authenticate_or_create_user(
            db=db,
            provider=provider,
            user_info=user_info,
            token_data=token_data
        )
        
        # Generate JWT tokens
        access_token = auth_service.create_access_token(
            data={"sub": str(user.id), "email": user.email}
        )
        refresh_token = auth_service.create_refresh_token(
            data={"sub": str(user.id)}
        )
        
        # Set secure HTTP-only cookies
        response.set_cookie(
            key="access_token",
            value=access_token,
            httponly=True,
            secure=settings.ENVIRONMENT == "production",
            samesite="lax",
            max_age=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
        )
        
        response.set_cookie(
            key="refresh_token",
            value=refresh_token,
            httponly=True,
            secure=settings.ENVIRONMENT == "production",
            samesite="lax",
            max_age=settings.REFRESH_TOKEN_EXPIRE_DAYS * 24 * 60 * 60
        )
        
        # Update last login
        await auth_service.update_last_login(db, user.id)
        
        return TokenResponse(
            access_token=access_token,
            token_type="bearer"
        )
    
    except Exception as e:
        raise HTTPException(
            status_code=400,
            detail=f"OAuth authentication failed: {str(e)}"
        )


@router.get("/google")
async def google_oauth_redirect(
    redirect_uri: str = f"{settings.FRONTEND_URL}/auth/callback/google"
):
    """Redirect to Google OAuth."""
    auth_url, state = oauth_service.get_authorization_url(
        provider=OAuthProvider.GOOGLE,
        redirect_uri=redirect_uri
    )
    return RedirectResponse(url=auth_url)


@router.get("/github")
async def github_oauth_redirect(
    redirect_uri: str = f"{settings.FRONTEND_URL}/auth/callback/github"
):
    """Redirect to GitHub OAuth."""
    auth_url, state = oauth_service.get_authorization_url(
        provider=OAuthProvider.GITHUB,
        redirect_uri=redirect_uri
    )
    return RedirectResponse(url=auth_url)


@router.get("/shopify")
async def shopify_oauth_redirect(
    shop: str,
    redirect_uri: str = f"{settings.FRONTEND_URL}/auth/callback/shopify"
):
    """Redirect to Shopify OAuth."""
    auth_url, state = oauth_service.get_authorization_url(
        provider=OAuthProvider.SHOPIFY,
        redirect_uri=redirect_uri,
        shop_domain=shop
    )
    return RedirectResponse(url=auth_url)


@router.get("/callback/google")
async def google_oauth_callback(
    code: str,
    state: str,
    response: Response,
    db = Depends(get_db)
):
    """Handle Google OAuth callback."""
    callback_request = OAuthCallbackRequest(
        provider="google",
        code=code,
        state=state,
        redirect_uri=f"{settings.FRONTEND_URL}/auth/callback/google"
    )
    
    token_response = await oauth_callback(callback_request, response, db)
    
    # Redirect to frontend with success
    return RedirectResponse(
        url=f"{settings.FRONTEND_URL}/auth/success?token={token_response.access_token}"
    )


@router.get("/callback/github")
async def github_oauth_callback(
    code: str,
    state: str,
    response: Response,
    db = Depends(get_db)
):
    """Handle GitHub OAuth callback."""
    callback_request = OAuthCallbackRequest(
        provider="github",
        code=code,
        state=state,
        redirect_uri=f"{settings.FRONTEND_URL}/auth/callback/github"
    )
    
    token_response = await oauth_callback(callback_request, response, db)
    
    # Redirect to frontend with success
    return RedirectResponse(
        url=f"{settings.FRONTEND_URL}/auth/success?token={token_response.access_token}"
    )


@router.get("/callback/shopify")
async def shopify_oauth_callback(
    code: str,
    shop: str,
    state: str,
    response: Response,
    db = Depends(get_db)
):
    """Handle Shopify OAuth callback."""
    callback_request = OAuthCallbackRequest(
        provider="shopify",
        code=code,
        state=state,
        redirect_uri=f"{settings.FRONTEND_URL}/auth/callback/shopify",
        shop_domain=shop
    )
    
    token_response = await oauth_callback(callback_request, response, db)
    
    # Redirect to frontend with success
    return RedirectResponse(
        url=f"{settings.FRONTEND_URL}/auth/success?token={token_response.access_token}"
    )


@router.post("/disconnect/{provider}")
async def disconnect_oauth_provider(
    provider: str,
    db = Depends(get_db),
    current_user: User = Depends(get_current_user_dependency)
):
    """
    Disconnect OAuth provider from user account.
    
    Args:
        provider: OAuth provider to disconnect
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Success message
    """
    try:
        oauth_provider = OAuthProvider(provider)
    except ValueError:
        raise HTTPException(
            status_code=400,
            detail=f"Unsupported OAuth provider: {provider}"
        )
    
    success = await auth_service.disconnect_oauth_provider(
        db, current_user.id, oauth_provider
    )
    
    if not success:
        raise HTTPException(
            status_code=404,
            detail=f"OAuth provider {provider} not connected"
        )
    
    return {"message": f"Successfully disconnected {provider}"}


@router.get("/providers")
async def get_connected_providers(
    db = Depends(get_db),
    current_user: User = Depends(get_current_user_dependency)
):
    """
    Get list of connected OAuth providers for current user.
    
    Args:
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        List of connected providers
    """
    providers = await auth_service.get_user_oauth_providers(db, current_user.id)
    
    return {
        "connected_providers": [
            {
                "provider": account.provider,
                "provider_username": account.provider_username,
                "provider_email": account.provider_email,
                "connected_at": account.created_at,
                "last_used_at": account.last_used_at
            }
            for account in providers
        ]
    }