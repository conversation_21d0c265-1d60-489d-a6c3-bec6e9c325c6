"""
Analytics event schemas for ProductMedia platform.
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, validator
import uuid

from core.schemas.base_schemas import BaseSchema
from .event_models import EventType, GroupBy


class EventIngestionRequest(BaseSchema):
    """Request schema for ingesting analytics events."""
    
    event_type: EventType
    session_id: Optional[str] = None
    user_id: Optional[str] = None
    media_variant_id: Optional[int] = None
    product_id: Optional[str] = None
    
    # Event data
    timestamp: Optional[datetime] = None
    duration: Optional[float] = Field(None, ge=0)
    position: Optional[float] = Field(None, ge=0)
    
    # Device and context
    user_agent: Optional[str] = None
    referrer: Optional[str] = None
    
    # UTM parameters
    utm_source: Optional[str] = None
    utm_medium: Optional[str] = None
    utm_campaign: Optional[str] = None
    utm_content: Optional[str] = None
    utm_term: Optional[str] = None
    
    # E-commerce data
    order_id: Optional[str] = None
    order_value: Optional[float] = Field(None, ge=0)
    currency: Optional[str] = Field(None, max_length=3)
    
    # Custom properties
    properties: Optional[Dict[str, Any]] = None
    
    # Deduplication token
    dedup_token: Optional[str] = None
    
    @validator('timestamp', pre=True, always=True)
    def set_timestamp(cls, v):
        return v or datetime.utcnow()
    
    @validator('dedup_token', pre=True, always=True)
    def set_dedup_token(cls, v):
        return v or str(uuid.uuid4())


class BatchEventIngestionRequest(BaseSchema):
    """Request schema for batch event ingestion."""
    
    events: List[EventIngestionRequest] = Field(..., min_items=1, max_items=100)
    tenant_id: int


class EventIngestionResponse(BaseSchema):
    """Response schema for event ingestion."""
    
    event_id: str
    status: str  # "success", "duplicate", "error"
    message: Optional[str] = None


class BatchEventIngestionResponse(BaseSchema):
    """Response schema for batch event ingestion."""
    
    total_events: int
    successful: int
    duplicates: int
    errors: int
    results: List[EventIngestionResponse]


class MediaAnalyticsRequest(BaseSchema):
    """Request schema for media analytics."""

    product_id: Optional[str] = None
    media_variant_id: Optional[int] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    group_by: Optional[GroupBy] = None


class MediaAnalyticsMetrics(BaseSchema):
    """Media analytics metrics."""
    
    views: int = 0
    unique_views: int = 0
    plays: int = 0
    completions: int = 0
    total_watch_time: float = 0.0
    average_watch_time: float = 0.0
    completion_rate: float = 0.0
    cta_clicks: int = 0
    cta_click_rate: float = 0.0
    add_to_carts: int = 0
    purchases: int = 0
    conversion_rate: float = 0.0
    total_revenue: float = 0.0


class MediaAnalyticsResponse(BaseSchema):
    """Response schema for media analytics."""

    product_id: Optional[str] = None
    media_variant_id: Optional[int] = None
    period_start: datetime
    period_end: datetime
    metrics: MediaAnalyticsMetrics
    daily_breakdown: Optional[List[Dict[str, Any]]] = None


class ConversionFunnelRequest(BaseSchema):
    """Request schema for conversion funnel analysis."""
    
    product_id: Optional[str] = None
    video_variant_id: Optional[int] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None


class ConversionFunnelStage(BaseSchema):
    """Conversion funnel stage data."""
    
    stage: str
    count: int
    conversion_rate: float
    drop_off_rate: float


class ConversionFunnelResponse(BaseSchema):
    """Response schema for conversion funnel analysis."""
    
    product_id: Optional[str] = None
    video_variant_id: Optional[int] = None
    period_start: datetime
    period_end: datetime
    total_sessions: int
    stages: List[ConversionFunnelStage]
    overall_conversion_rate: float
    average_time_to_conversion: Optional[float] = None


class ABTestRequest(BaseSchema):
    """Request schema for A/B test creation."""
    
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    product_ids: List[str] = Field(..., min_items=1)
    control_variant_id: int
    test_variant_ids: List[int] = Field(..., min_items=1)
    traffic_allocation: float = Field(1.0, ge=0.0, le=1.0)
    control_split: float = Field(0.5, ge=0.0, le=1.0)
    confidence_level: float = Field(0.95, ge=0.8, le=0.99)


class ABTestResponse(BaseSchema):
    """Response schema for A/B test."""
    
    id: int
    name: str
    description: Optional[str] = None
    product_ids: List[str]
    control_variant_id: int
    test_variant_ids: List[int]
    traffic_allocation: float
    control_split: float
    is_active: bool
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    statistical_significance: Optional[float] = None
    confidence_level: float
    winner_variant_id: Optional[int] = None
    created_at: datetime
    updated_at: Optional[datetime] = None


class ABTestResultsRequest(BaseSchema):
    """Request schema for A/B test results."""

    experiment_id: int
    metric: str = "conversion_rate"


class ABTestVariantResult(BaseSchema):
    """A/B test variant result."""
    
    variant_id: int
    variant_name: str
    sessions: int
    metric_value: float
    confidence_interval_lower: float
    confidence_interval_upper: float
    is_control: bool
    is_winner: bool


class ABTestResultsResponse(BaseSchema):
    """Response schema for A/B test results."""
    
    experiment_id: int
    experiment_name: str
    metric: str
    is_significant: bool
    p_value: Optional[float] = None
    confidence_level: float
    winner_variant_id: Optional[int] = None
    lift_percentage: Optional[float] = None
    variants: List[ABTestVariantResult]


class AnalyticsDashboardRequest(BaseSchema):
    """Request schema for analytics dashboard."""
    
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    product_ids: Optional[List[str]] = None
    media_variant_ids: Optional[List[int]] = None


class AnalyticsDashboardResponse(BaseSchema):
    """Response schema for analytics dashboard."""

    period_start: datetime
    period_end: datetime
    overview_metrics: MediaAnalyticsMetrics
    top_performing_media: List[Dict[str, Any]]
    conversion_funnel: ConversionFunnelResponse
    device_breakdown: Dict[str, int]
    traffic_sources: Dict[str, int]
    daily_trends: List[Dict[str, Any]]


# Event validation schemas
class MediaPlayEvent(BaseSchema):
    """Media play event validation."""

    media_variant_id: int
    product_id: str
    position: float = Field(0.0, ge=0)


class MediaCompleteEvent(BaseSchema):
    """Media complete event validation."""

    media_variant_id: int
    product_id: str
    duration: float = Field(..., gt=0)


class CTAClickEvent(BaseSchema):
    """CTA click event validation."""

    media_variant_id: int
    product_id: str
    cta_type: Optional[str] = None
    cta_text: Optional[str] = None


class PurchaseEvent(BaseSchema):
    """Purchase event validation."""

    order_id: str
    product_id: str
    order_value: float = Field(..., gt=0)
    currency: str = Field(..., max_length=3)
    media_variant_id: Optional[int] = None
