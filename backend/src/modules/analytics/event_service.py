"""
Analytics event service for ProductMedia platform.
Handles event ingestion, deduplication, and conversion tracking.
"""

import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, func, distinct
from sqlalchemy.dialects.postgresql import insert

from core.services.base_service import BaseService
from .event_models import AnalyticsEvent, ConversionFunnel, MediaAnalytics, ABTestExperiment, EventType, GroupBy
from .event_schemas import (
    EventIngestionRequest, BatchEventIngestionRequest,
    EventIngestionResponse, BatchEventIngestionResponse,
    MediaAnalyticsRequest, MediaAnalyticsResponse, MediaAnalyticsMetrics,
    ConversionFunnelRequest, ConversionFunnelResponse, ConversionFunnelStage
)

logger = logging.getLogger(__name__)


class AnalyticsEventService(BaseService[AnalyticsEvent, dict, dict]):
    """Service for analytics event operations."""

    def __init__(self):
        super().__init__(AnalyticsEvent)

    async def ingest_event(
        self,
        db: AsyncSession,
        tenant_id: int,
        event_request: EventIngestionRequest,
        ip_address: Optional[str] = None
    ) -> EventIngestionResponse:
        """
        Ingest a single analytics event with deduplication.
        
        Args:
            db: Database session
            tenant_id: Tenant ID
            event_request: Event data
            ip_address: Client IP address
            
        Returns:
            Event ingestion response
        """
        try:
            # Generate event ID for deduplication
            event_id = f"{tenant_id}_{event_request.dedup_token}"
            
            # Check for duplicate
            existing_event = await db.execute(
                select(AnalyticsEvent).where(AnalyticsEvent.event_id == event_id)
            )
            if existing_event.scalar_one_or_none():
                return EventIngestionResponse(
                    event_id=event_id,
                    status="duplicate",
                    message="Event already processed"
                )
            
            # Detect device type from user agent
            device_type = self._detect_device_type(event_request.user_agent)
            
            # Create event
            event = AnalyticsEvent(
                event_id=event_id,
                event_type=event_request.event_type.value,
                tenant_id=tenant_id,
                session_id=event_request.session_id,
                user_id=event_request.user_id,
                media_variant_id=event_request.media_variant_id,
                product_id=event_request.product_id,
                timestamp=event_request.timestamp,
                duration=event_request.duration,
                position=event_request.position,
                user_agent=event_request.user_agent,
                ip_address=ip_address,
                device_type=device_type,
                referrer=event_request.referrer,
                utm_source=event_request.utm_source,
                utm_medium=event_request.utm_medium,
                utm_campaign=event_request.utm_campaign,
                utm_content=event_request.utm_content,
                utm_term=event_request.utm_term,
                order_id=event_request.order_id,
                order_value=event_request.order_value,
                currency=event_request.currency,
                properties=event_request.properties or {},
                is_conversion=event_request.event_type in [EventType.ADD_TO_CART, EventType.PURCHASE]
            )
            
            db.add(event)
            await db.commit()
            await db.refresh(event)
            
            # Process conversion funnel if this is a conversion event
            if event.is_conversion:
                await self._update_conversion_funnel(db, event)
            
            logger.info(f"Ingested event {event_id} for tenant {tenant_id}")
            
            return EventIngestionResponse(
                event_id=event_id,
                status="success",
                message="Event ingested successfully"
            )
            
        except Exception as e:
            logger.error(f"Error ingesting event: {e}")
            return EventIngestionResponse(
                event_id=event_id,
                status="error",
                message=str(e)
            )

    async def ingest_batch_events(
        self,
        db: AsyncSession,
        request: BatchEventIngestionRequest,
        ip_address: Optional[str] = None
    ) -> BatchEventIngestionResponse:
        """
        Ingest multiple events in batch.
        
        Args:
            db: Database session
            request: Batch event request
            ip_address: Client IP address
            
        Returns:
            Batch ingestion response
        """
        results = []
        successful = 0
        duplicates = 0
        errors = 0
        
        for event_request in request.events:
            result = await self.ingest_event(db, request.tenant_id, event_request, ip_address)
            results.append(result)
            
            if result.status == "success":
                successful += 1
            elif result.status == "duplicate":
                duplicates += 1
            else:
                errors += 1
        
        return BatchEventIngestionResponse(
            total_events=len(request.events),
            successful=successful,
            duplicates=duplicates,
            errors=errors,
            results=results
        )

    async def get_media_analytics(
        self,
        db: AsyncSession,
        tenant_id: int,
        request: MediaAnalyticsRequest
    ) -> MediaAnalyticsResponse:
        """
        Get media analytics for a product or variant.

        Args:
            db: Database session
            tenant_id: Tenant ID
            request: Analytics request

        Returns:
            Media analytics response
        """
        # Set default date range (last 30 days)
        end_date = request.end_date or datetime.utcnow()
        start_date = request.start_date or (end_date - timedelta(days=30))
        
        # Build base query
        query = select(AnalyticsEvent).where(
            and_(
                AnalyticsEvent.tenant_id == tenant_id,
                AnalyticsEvent.timestamp >= start_date,
                AnalyticsEvent.timestamp <= end_date
            )
        )
        
        if request.product_id:
            query = query.where(AnalyticsEvent.product_id == request.product_id)
        
        if request.media_variant_id:
            query = query.where(AnalyticsEvent.media_variant_id == request.media_variant_id)
        
        # Execute query
        result = await db.execute(query)
        events = result.scalars().all()
        
        # Calculate metrics
        metrics = self._calculate_media_metrics(events)
        
        # Get daily breakdown if requested
        daily_breakdown = None
        if request.group_by == GroupBy.DAY:
            daily_breakdown = await self._get_daily_breakdown(
                db, tenant_id, start_date, end_date, request.product_id, request.media_variant_id
            )
        
        return MediaAnalyticsResponse(
            product_id=request.product_id,
            media_variant_id=request.media_variant_id,
            period_start=start_date,
            period_end=end_date,
            metrics=metrics,
            daily_breakdown=daily_breakdown
        )

    async def get_conversion_funnel(
        self,
        db: AsyncSession,
        tenant_id: int,
        request: ConversionFunnelRequest
    ) -> ConversionFunnelResponse:
        """
        Get conversion funnel analysis.
        
        Args:
            db: Database session
            tenant_id: Tenant ID
            request: Funnel request
            
        Returns:
            Conversion funnel response
        """
        # Set default date range
        end_date = request.end_date or datetime.utcnow()
        start_date = request.start_date or (end_date - timedelta(days=30))
        
        # Get funnel data
        funnel_query = select(ConversionFunnel).where(
            and_(
                ConversionFunnel.tenant_id == tenant_id,
                ConversionFunnel.funnel_start >= start_date,
                ConversionFunnel.funnel_start <= end_date
            )
        )
        
        if request.product_id:
            funnel_query = funnel_query.where(ConversionFunnel.product_id == request.product_id)
        
        if request.media_variant_id:
            funnel_query = funnel_query.where(
                ConversionFunnel.first_media_variant_id == request.media_variant_id
            )
        
        result = await db.execute(funnel_query)
        funnels = result.scalars().all()
        
        # Calculate funnel stages
        stages = self._calculate_funnel_stages(funnels)
        
        # Calculate overall metrics
        total_sessions = len(funnels)
        conversions = len([f for f in funnels if f.purchase_event_id])
        overall_conversion_rate = (conversions / total_sessions * 100) if total_sessions > 0 else 0
        
        # Calculate average time to conversion
        conversion_times = [f.time_to_conversion for f in funnels if f.time_to_conversion]
        avg_time_to_conversion = sum(conversion_times) / len(conversion_times) if conversion_times else None
        
        return ConversionFunnelResponse(
            product_id=request.product_id,
            media_variant_id=request.media_variant_id,
            period_start=start_date,
            period_end=end_date,
            total_sessions=total_sessions,
            stages=stages,
            overall_conversion_rate=overall_conversion_rate,
            average_time_to_conversion=avg_time_to_conversion
        )

    def _detect_device_type(self, user_agent: Optional[str]) -> Optional[str]:
        """Detect device type from user agent."""
        if not user_agent:
            return None
        
        user_agent_lower = user_agent.lower()
        
        if any(mobile in user_agent_lower for mobile in ['mobile', 'android', 'iphone']):
            return 'mobile'
        elif 'tablet' in user_agent_lower or 'ipad' in user_agent_lower:
            return 'tablet'
        else:
            return 'desktop'

    def _calculate_media_metrics(self, events: List[AnalyticsEvent]) -> MediaAnalyticsMetrics:
        """Calculate media metrics from events."""
        metrics = MediaAnalyticsMetrics()
        
        # Group events by type
        events_by_type = {}
        for event in events:
            event_type = event.event_type
            if event_type not in events_by_type:
                events_by_type[event_type] = []
            events_by_type[event_type].append(event)
        
        # Calculate metrics
        view_events = events_by_type.get(EventType.MEDIA_VIEW.value, [])
        play_events = events_by_type.get(EventType.MEDIA_PLAY.value, [])
        complete_events = events_by_type.get(EventType.MEDIA_COMPLETE.value, [])
        cta_events = events_by_type.get(EventType.CTA_CLICK.value, [])
        cart_events = events_by_type.get(EventType.ADD_TO_CART.value, [])
        purchase_events = events_by_type.get(EventType.PURCHASE.value, [])
        
        metrics.views = len(view_events)
        metrics.unique_views = len(set(e.session_id for e in view_events if e.session_id))
        metrics.plays = len(play_events)
        metrics.completions = len(complete_events)
        metrics.cta_clicks = len(cta_events)
        metrics.add_to_carts = len(cart_events)
        metrics.purchases = len(purchase_events)
        
        # Calculate rates
        if metrics.views > 0:
            metrics.completion_rate = (metrics.completions / metrics.views) * 100
            metrics.cta_click_rate = (metrics.cta_clicks / metrics.views) * 100
            metrics.conversion_rate = (metrics.purchases / metrics.views) * 100
        
        # Calculate watch time
        watch_times = [e.duration for e in complete_events if e.duration]
        if watch_times:
            metrics.total_watch_time = sum(watch_times)
            metrics.average_watch_time = metrics.total_watch_time / len(watch_times)
        
        # Calculate revenue
        revenues = [e.order_value for e in purchase_events if e.order_value]
        metrics.total_revenue = sum(revenues)
        
        return metrics

    def _calculate_funnel_stages(self, funnels: List[ConversionFunnel]) -> List[ConversionFunnelStage]:
        """Calculate funnel stage metrics."""
        total = len(funnels)
        if total == 0:
            return []
        
        stages = [
            ("Media View", len([f for f in funnels if f.media_view_event_id])),
            ("Media Play", len([f for f in funnels if f.media_play_event_id])),
            ("Media Complete", len([f for f in funnels if f.media_complete_event_id])),
            ("CTA Click", len([f for f in funnels if f.cta_click_event_id])),
            ("Add to Cart", len([f for f in funnels if f.add_to_cart_event_id])),
            ("Purchase", len([f for f in funnels if f.purchase_event_id])),
        ]
        
        result = []
        for i, (stage_name, count) in enumerate(stages):
            conversion_rate = (count / total) * 100
            drop_off_rate = 0
            if i > 0:
                prev_count = stages[i-1][1]
                drop_off_rate = ((prev_count - count) / prev_count * 100) if prev_count > 0 else 0
            
            result.append(ConversionFunnelStage(
                stage=stage_name,
                count=count,
                conversion_rate=conversion_rate,
                drop_off_rate=drop_off_rate
            ))
        
        return result

    async def _update_conversion_funnel(self, db: AsyncSession, event: AnalyticsEvent):
        """Update conversion funnel for a conversion event."""
        if not event.session_id:
            return
        
        # Get or create funnel for this session
        funnel_result = await db.execute(
            select(ConversionFunnel).where(
                and_(
                    ConversionFunnel.tenant_id == event.tenant_id,
                    ConversionFunnel.session_id == event.session_id
                )
            )
        )
        funnel = funnel_result.scalar_one_or_none()
        
        if not funnel:
            # Create new funnel
            funnel = ConversionFunnel(
                tenant_id=event.tenant_id,
                session_id=event.session_id,
                user_id=event.user_id,
                product_id=event.product_id,
                funnel_start=event.timestamp
            )
            db.add(funnel)
        
        # Update funnel based on event type
        if event.event_type == EventType.ADD_TO_CART.value:
            funnel.add_to_cart_event_id = event.event_id
        elif event.event_type == EventType.PURCHASE.value:
            funnel.purchase_event_id = event.event_id
            funnel.funnel_end = event.timestamp
            funnel.conversion_value = event.order_value
            funnel.currency = event.currency
            
            # Calculate time to conversion
            if funnel.funnel_start:
                time_diff = event.timestamp - funnel.funnel_start
                funnel.time_to_conversion = int(time_diff.total_seconds())
        
        await db.commit()

    async def _get_daily_breakdown(
        self,
        db: AsyncSession,
        tenant_id: int,
        start_date: datetime,
        end_date: datetime,
        product_id: Optional[str] = None,
        media_variant_id: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """Get daily breakdown of analytics."""
        # This would implement daily aggregation logic
        # For now, return empty list
        return []


# Create service instance
analytics_event_service = AnalyticsEventService()
