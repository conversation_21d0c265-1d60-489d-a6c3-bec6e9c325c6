"""
Media Generation Service - Core business logic for ProductVideo
Generic service that uses provider plugins for different AI services.
"""

import logging
from enum import Enum
from typing import List, Optional, Dict, Any

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel

from core.services.base_service import BaseService
from core.config import get_settings
from .models import (
    MediaJob, MediaVariant, Template, MediaJobStatus, MediaVariantStatus
)
from .schemas import MediaGenerateRequest, MediaGenerationRequest, MediaGenerationResult

from modules.queue.queue_service import celery_service as queue_service, TaskPriority
from modules.storage.storage_service import media_storage_service
from .provider_interface import (
    provider_registry,
    ProviderConfig
)
from .provider_manager import (
    provider_manager,
    initialize_providers
)
from .providers_config import (
    PROVIDER_CONFIGS,
    get_provider_config,
    get_default_provider,
    get_fallback_chain
)

logger = logging.getLogger(__name__)
settings = get_settings()




class MediaGenerationService(BaseService[MediaJob, dict, dict]):
    """Service for media generation operations using provider plugins."""

    def __init__(self):
        super().__init__(MediaJob)
        self._providers_initialized = False

    async def initialize_providers(self) -> Dict[str, bool]:
        """Initialize media provider plugins from configuration."""
        if self._providers_initialized:
            return {"message": "Providers already initialized"}

        # Load provider configurations
        provider_configs = {}
        for provider_name in PROVIDER_CONFIGS.keys():
            try:
                config = get_provider_config(provider_name)
                provider_configs[provider_name] = config
            except ValueError as e:
                logger.warning(f"Skipping provider {provider_name}: {e}")
                continue

        results = await initialize_providers(provider_configs)
        self._providers_initialized = True

        logger.info(f"Initialized media providers: {results}")
        return results

    async def generate_media_with_provider(
        self,
        provider_name: str,
        request: MediaGenerationRequest,
        use_fallback: bool = True
    ) -> MediaGenerationResult:
        """Generate media using a specific provider plugin with fallback support."""
        if not self._providers_initialized:
            await self.initialize_providers()

        # Try the specified provider first
        result = await self._try_provider(provider_name, request)

        if result.success or not use_fallback:
            return result

        # Try fallback providers
        fallback_chain = get_fallback_chain(request.media_type)
        for fallback_provider in fallback_chain:
            if fallback_provider == provider_name:
                continue  # Skip the original provider

            logger.info(f"Trying fallback provider: {fallback_provider}")
            fallback_result = await self._try_provider(fallback_provider, request)
            if fallback_result.success:
                logger.info(f"Successfully used fallback provider: {fallback_provider}")
                return fallback_result

        # All providers failed
        return MediaGenerationResult(
            success=False,
            error_message=f"All providers failed for {request.media_type} generation"
        )

    async def _try_provider(self, provider_name: str, request: MediaGenerationRequest) -> MediaGenerationResult:
        """Try to generate media with a specific provider."""
        try:
            provider = provider_registry.get_provider(provider_name)
            if not provider:
                return MediaGenerationResult(
                    success=False,
                    error_message=f"Provider {provider_name} not available"
                )

            # Check if provider supports the requested media type
            if request.media_type not in provider.supported_media_types:
                return MediaGenerationResult(
                    success=False,
                    error_message=f"Provider {provider_name} does not support {request.media_type}"
                )

            result = await provider.generate_media(request)

            if not result.success:
                logger.warning(f"Provider {provider_name} failed: {result.error_message}")

            return result

        except Exception as e:
            logger.error(f"Error with provider {provider_name}: {e}")
            return MediaGenerationResult(
                success=False,
                error_message=f"Provider {provider_name} error: {str(e)}"
            )

    async def get_available_providers(self, media_type: Optional[str] = None) -> List[str]:
        """Get list of available providers, optionally filtered by media type."""
        if not self._providers_initialized:
            await self.initialize_providers()

        return provider_registry.get_available_providers(media_type)

    def _convert_to_internal_request(self, api_request: MediaGenerateRequest, product_id: str = None) -> MediaGenerationRequest:
        """Convert API request to internal provider request format."""
        # Extract product title from request
        product_title = product_id or "unknown_product"

        # If items are provided, find the matching product
        if api_request.items:
            for item in api_request.items:
                if str(item.product_id) == str(product_id):
                    product_title = item.prompt.split()[0] if item.prompt else str(item.product_id)
                    break

        return MediaGenerationRequest(
            product_title=product_title,
            media_type=api_request.media_type,
            template_id=api_request.template_id,
            voice_id=api_request.voice_id,
            text_input=api_request.text_input,
            custom_config={
                "aspect_ratio": api_request.aspect_ratio,
                "locale": api_request.locale,
                "mode": api_request.mode,
                "model": api_request.model,
                "settings": api_request.settings,
                "items": api_request.items
            },
            num_images=4,  # Default variants count
            variants_count=4,
            aspect_ratio=api_request.aspect_ratio or "1:1",
            style="professional",
            model=api_request.model,
            settings=api_request.settings
        )

    async def create_generation_jobs(
        self,
        db: AsyncSession,
        user_id: int,
        request: MediaGenerateRequest
    ) -> List[MediaJob]:
        """Create media generation jobs for multiple products."""
        jobs = []

        # Store the full request payload
        full_payload = {
            "media_type": request.media_type,
            "shop_id": request.shop_id,
            "product_ids": request.product_ids,
            "template_id": request.template_id,
            "voice_id": request.voice_id,
            "aspect_ratio": request.aspect_ratio,
            "locale": request.locale,
            "text_input": request.text_input,
            "mode": request.mode,
            "model": request.model,
            "settings": request.settings,
            "items": request.items
        }

        for product_id in request.product_ids:
            # Create main job
            job = MediaJob(
                user_id=user_id,
                product_id=str(product_id),
                status=MediaJobStatus.PENDING,
                media_type=request.media_type,
                provider=request.model or settings.VIDEO_PROVIDER,  # Use model if provided
                template_id=request.template_id,
                voice_id=request.voice_id,
                script=request.text_input, # Use text_input for voice generation
                custom_config={
                    "aspect_ratio": request.aspect_ratio,
                    "locale": request.locale,
                    "mode": request.mode,
                    "model": request.model,
                    "settings": request.settings
                },
                full_payload=full_payload  # Store complete payload
            )

            db.add(job)
            await db.flush()  # Get the job ID

            # Create 4 variants per job (as per requirements)
            variants = [
                MediaVariant(
                    job_id=job.id,
                    user_id=user_id,
                    variant_name="square",
                    status=MediaVariantStatus.GENERATING
                ),
                MediaVariant(
                    job_id=job.id,
                    user_id=user_id,
                    variant_name="vertical",
                    status=MediaVariantStatus.GENERATING
                ),
                MediaVariant(
                    job_id=job.id,
                    user_id=user_id,
                    variant_name="horizontal",
                    status=MediaVariantStatus.GENERATING
                ),
                MediaVariant(
                    job_id=job.id,
                    user_id=user_id,
                    variant_name="story",
                    status=MediaVariantStatus.GENERATING
                )
            ]

            for variant in variants:
                db.add(variant)

            jobs.append(job)

        await db.commit()
        return jobs

    async def get_job_with_variants(self, db: AsyncSession, job_id: int) -> Optional[MediaJob]:
        """Get job with its variants."""
        result = await db.execute(
            select(MediaJob).where(MediaJob.id == job_id)
        )
        return result.scalar_one_or_none()

    async def process_generation_job(self, db: AsyncSession, job_id: int):
        """Process a media generation job using provider plugins (background task)."""
        logger.info(f"Processing media generation job {job_id}")

        # Get the job to extract details
        job = await self.get(db, job_id)
        if not job:
            raise ValueError(f"Job {job_id} not found")

        # Determine provider based on media type and settings
        provider_name = self._get_provider_for_media_type(job.media_type)

        # Convert stored payload back to API request format, then to internal format
        api_request = MediaGenerateRequest(**job.full_payload)
        request = self._convert_to_internal_request(api_request, job.product_id)

        # Generate media using provider
        result = await self.generate_media_with_provider(provider_name, request)

        if result.success:
            # Update job status and create variants
            await self._create_variants_from_result(db, job, result)
            job.status = MediaJobStatus.COMPLETED
            logger.info(f"Media generation job {job_id} completed successfully")
        else:
            job.status = MediaJobStatus.FAILED
            job.error_message = result.error_message
            logger.error(f"Media generation job {job_id} failed: {result.error_message}")

        await db.commit()

    def _get_provider_for_media_type(self, media_type: str) -> str:
        """Get the appropriate provider for a media type from configuration."""
        # Check settings first, then fall back to defaults
        if media_type == "image" and hasattr(settings, 'IMAGE_PROVIDER') and settings.IMAGE_PROVIDER:
            return settings.IMAGE_PROVIDER
        elif media_type == "video" and hasattr(settings, 'VIDEO_PROVIDER') and settings.VIDEO_PROVIDER:
            return settings.VIDEO_PROVIDER
        elif media_type == "voice" and hasattr(settings, 'VOICE_PROVIDER') and settings.VOICE_PROVIDER:
            return settings.VOICE_PROVIDER

        # Use default from configuration
        return get_default_provider(media_type)

    async def _create_variants_from_result(
        self,
        db: AsyncSession,
        job: MediaJob,
        result: MediaGenerationResult
    ):
        """Create media variants from generation result."""
        # Update existing variants with results
        variants = (await db.execute(
            select(MediaVariant).filter(MediaVariant.job_id == job.id)
        )).scalars().all()

        if result.images:
            for i, image_data in enumerate(result.images):
                if i < len(variants):
                    variant = variants[i]
                    variant.image_url = image_data.get("image_url")
                    variant.thumbnail_url = image_data.get("thumbnail_url")
                    variant.status = MediaVariantStatus.READY

        elif result.variants:
            for i, variant_data in enumerate(result.variants):
                if i < len(variants):
                    variant = variants[i]
                    variant.video_url = variant_data.get("video_url")
                    variant.thumbnail_url = variant_data.get("thumbnail_url")
                    variant.status = MediaVariantStatus.READY

    async def regenerate_variant(
        self, 
        job_id: int, 
        variant_id: int, 
        override_params: Optional[Dict[str, Any]] = None
    ):
        """Regenerate a specific media variant."""
        # TODO: Implement regeneration logic
        logger.info(f"Regenerating variant {variant_id} for job {job_id}")

    async def push_to_platform(
        self,
        shop_id: int,
        product_id: str,
        variant_id: int,
        publish_options: Optional[Dict[str, Any]] = None
    ):
        """Push media variant to platform product media."""
        # TODO: Implement platform push logic
        logger.info(f"Pushing variant {variant_id} to platform product {product_id}")







# Create service instance
media_service = MediaGenerationService()
