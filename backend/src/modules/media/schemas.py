"""
Media Generation Schemas
Production-ready schemas for e-commerce media generation pipeline.
"""

from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, validator, Field
from enum import Enum

from core.schemas.base_schemas import BaseSchema


class ShopBrandingTone(str, Enum):
    """Shop branding tone options."""
    LUXURY = "luxury"
    MINIMAL = "minimal"
    FRIENDLY = "friendly"
    TECHNICAL = "technical"
    PLAYFUL = "playful"
    PROFESSIONAL = "professional"
    VINTAGE = "vintage"
    MODERN = "modern"


class MediaFormat(str, Enum):
    """Supported media formats."""
    WEBP = "webp"
    AVIF = "avif"
    JPG = "jpg"
    PNG = "png"
    MP4 = "mp4"
    WEBM = "webm"
    GIF = "gif"


class ShopBranding(BaseModel):
    """Shop branding configuration."""
    tone: ShopBrandingTone = ShopBrandingTone.PROFESSIONAL
    primary_colors: List[str] = Field(default_factory=list, description="Hex colors or color names")
    logo_url: Optional[str] = None
    target_audience: Optional[str] = None  # e.g., "urban professionals, 25-45"


class ImageRequirements(BaseModel):
    """Image generation requirements."""
    aspect_ratios: List[str] = Field(default=["1:1", "16:9", "4:3"], description="Desired aspect ratios")
    min_resolution: str = "1024x1024"
    background: str = "white"  # transparent, white, scene
    formats: List[MediaFormat] = Field(default=[MediaFormat.WEBP, MediaFormat.JPG])
    generate_thumbnails: bool = True
    watermark: bool = False


class VideoRequirements(BaseModel):
    """Video generation requirements."""
    length_seconds: int = Field(default=15, ge=5, le=60)
    aspect_ratios: List[str] = Field(default=["1:1", "9:16", "16:9"])
    voiceover: bool = False
    music: bool = True
    subtitles: bool = True
    formats: List[MediaFormat] = Field(default=[MediaFormat.MP4, MediaFormat.WEBM])


class SEOData(BaseModel):
    """SEO-related data."""
    meta_title: Optional[str] = None
    meta_description: Optional[str] = None
    primary_keywords: List[str] = Field(default_factory=list)
    og_title: Optional[str] = None
    og_description: Optional[str] = None


class PriceTiers(BaseModel):
    """Price tier information for context."""
    tier: str = "mid"  # budget, mid, premium, luxury
    price_range: Optional[str] = None  # e.g., "$50-100"


class ProductVariant(BaseModel):
    """Product variant information."""
    id: str
    color: Optional[str] = None
    size: Optional[str] = None
    sku: Optional[str] = None
    price: Optional[float] = None


class SafetyFlags(BaseModel):
    """Safety and legal flags."""
    contains_manufacturer_logo: bool = False
    requires_age_verification: bool = False
    restricted_categories: List[str] = Field(default_factory=list)


class ProductSchema(BaseModel):
    """
    Complete product schema for e-commerce media generation.

    This schema contains all the information needed to generate high-quality,
    context-aware media assets for e-commerce products.
    """

    # Core product identification
    product_id: str = Field(..., description="Unique product identifier/SKU")
    title: str = Field(..., description="Product title")
    short_description: str = Field(..., description="1-2 sentence product blurb")
    detailed_description: str = Field(..., description="Long description with features")

    # Brand and categorization
    brand: str = Field(..., description="Product brand name")
    category: str = Field(..., description="Normalized category (e.g., apparel.coats, electronics.headphones)")
    tags: List[str] = Field(default_factory=list, description="Marketing/Shopify tags")

    # Shop context
    shop_name: str = Field(..., description="Store/shop name")
    shop_branding: ShopBranding = Field(default_factory=ShopBranding)

    # Product attributes
    materials: List[str] = Field(default_factory=list, description="Materials used (e.g., wool, cotton)")
    colors: List[str] = Field(default_factory=list, description="Available colors")
    sizes: List[str] = Field(default_factory=list, description="Available sizes")
    usage_contexts: List[str] = Field(default_factory=list, description="Usage contexts (e.g., office, travel)")

    # Media requirements
    primary_image_url: Optional[str] = None
    image_requirements: ImageRequirements = Field(default_factory=ImageRequirements)
    video_requirements: VideoRequirements = Field(default_factory=VideoRequirements)

    # SEO and marketing
    seo: SEOData = Field(default_factory=SEOData)
    price_tiers: PriceTiers = Field(default_factory=PriceTiers)

    # Variants and inventory
    available_variants: List[ProductVariant] = Field(default_factory=list)
    inventory_status: Optional[str] = "in_stock"

    # Localization
    language: str = "en-US"
    locale: str = "en-US"

    # Optional enhancement data
    customer_reviews_sample: List[str] = Field(default_factory=list, description="Sample customer reviews")
    competitor_examples: List[str] = Field(default_factory=list, description="Competitor reference URLs or descriptions")
    preferred_styles: List[str] = Field(default_factory=list, description="Preferred visual styles")

    # Safety and compliance
    safety_flags: SafetyFlags = Field(default_factory=SafetyFlags)

    # Versioning for regeneration triggers
    product_version: str = "1.0"

    class Config:
        json_encoders = {
            Enum: lambda v: v.value
        }


# Example product instances for testing and development
EXAMPLE_PRODUCTS = {
    "midtown_wool_overcoat": {
        "product_id": "MWO-2024-001",
        "title": "Midtown Wool Overcoat",
        "short_description": "Premium wool overcoat designed for urban professionals who demand style and warmth.",
        "detailed_description": "Crafted from 100% merino wool with a tailored fit, this overcoat features a classic double-breasted design with horn buttons, interior pockets, and a removable belt. Perfect for business meetings, evening events, and daily commuting in cold weather.",
        "brand": "UrbanClassic",
        "category": "apparel.coats",
        "tags": ["wool", "overcoat", "professional", "winter", "tailored", "premium"],
        "shop_name": "Metropolitan Menswear",
        "shop_branding": {
            "tone": "luxury",
            "primary_colors": ["#1a1a1a", "#8b4513", "#f5f5dc"],
            "target_audience": "urban professionals, 25-45"
        },
        "materials": ["merino wool", "polyester lining", "horn buttons"],
        "colors": ["charcoal", "navy", "camel"],
        "sizes": ["S", "M", "L", "XL", "XXL"],
        "usage_contexts": ["office", "business meetings", "evening events", "commuting"],
        "image_requirements": {
            "aspect_ratios": ["1:1", "3:4", "16:9"],
            "background": "white",
            "generate_thumbnails": True
        },
        "video_requirements": {
            "length_seconds": 20,
            "voiceover": False,
            "music": True,
            "subtitles": True
        },
        "seo": {
            "primary_keywords": ["wool overcoat", "men's winter coat", "professional outerwear"],
            "meta_title": "Premium Wool Overcoat for Men | UrbanClassic",
            "meta_description": "Discover our premium merino wool overcoat. Tailored fit, classic design, perfect for professionals. Available in charcoal, navy, and camel."
        },
        "price_tiers": {
            "tier": "premium",
            "price_range": "$300-500"
        },
        "available_variants": [
            {"id": "MWO-2024-001-M-CHAR", "color": "charcoal", "size": "M", "sku": "MWO-M-CHAR"},
            {"id": "MWO-2024-001-L-NAVY", "color": "navy", "size": "L", "sku": "MWO-L-NAVY"}
        ],
        "customer_reviews_sample": [
            "Perfect fit and excellent quality wool. Keeps me warm during NYC winters.",
            "Stylish and professional. Gets compliments every time I wear it.",
            "Worth the investment. The tailoring is impeccable."
        ],
        "preferred_styles": ["lifestyle", "studio", "model_wearing"],
        "language": "en-US",
        "product_version": "1.0"
    },

    "wireless_noise_cancelling_headphones": {
        "product_id": "WNC-HP-2024-PRO",
        "title": "ProSound Wireless Noise-Cancelling Headphones",
        "short_description": "Premium wireless headphones with active noise cancellation and 30-hour battery life.",
        "detailed_description": "Experience superior audio quality with our flagship wireless headphones featuring advanced active noise cancellation, premium drivers, and all-day comfort. Includes quick charge, multi-device connectivity, and premium carrying case.",
        "brand": "ProSound",
        "category": "electronics.headphones",
        "tags": ["wireless", "noise-cancelling", "premium", "bluetooth", "long-battery"],
        "shop_name": "AudioTech Pro",
        "shop_branding": {
            "tone": "technical",
            "primary_colors": ["#000000", "#ff6b35", "#f7f7f7"],
            "target_audience": "audio enthusiasts, professionals, 20-50"
        },
        "materials": ["aluminum", "memory foam", "premium leather"],
        "colors": ["matte black", "silver", "midnight blue"],
        "sizes": ["one size"],
        "usage_contexts": ["work", "travel", "music production", "gaming", "calls"],
        "image_requirements": {
            "aspect_ratios": ["1:1", "16:9"],
            "background": "white",
            "generate_thumbnails": True
        },
        "video_requirements": {
            "length_seconds": 30,
            "voiceover": True,
            "music": True,
            "subtitles": True
        },
        "seo": {
            "primary_keywords": ["wireless headphones", "noise cancelling", "premium audio"],
            "meta_title": "ProSound Wireless Noise-Cancelling Headphones | AudioTech Pro",
            "meta_description": "Premium wireless headphones with active noise cancellation, 30-hour battery, and superior sound quality. Perfect for professionals and audiophiles."
        },
        "price_tiers": {
            "tier": "premium",
            "price_range": "$200-350"
        },
        "available_variants": [
            {"id": "WNC-HP-2024-PRO-BLACK", "color": "matte black", "sku": "WNC-PRO-BLK"},
            {"id": "WNC-HP-2024-PRO-SILVER", "color": "silver", "sku": "WNC-PRO-SLV"}
        ],
        "customer_reviews_sample": [
            "Best headphones I've ever owned. The noise cancellation is incredible.",
            "Perfect for long flights and work calls. Battery lasts forever.",
            "Sound quality is amazing. Worth every penny."
        ],
        "preferred_styles": ["studio", "lifestyle", "technical"],
        "language": "en-US",
        "product_version": "1.0"
    }
}


class GenerationSettings(BaseModel):
    """Detailed generation settings for media creation."""

    # Image/Video settings
    size: Optional[str] = None  # e.g., "1024x1024"
    guidance: Optional[float] = None  # e.g., 7.5
    steps: Optional[int] = None  # e.g., 25
    strength: Optional[float] = None  # e.g., 0.8
    seed: Optional[int] = None  # e.g., 91678

    # Quality and processing
    upscale: Optional[bool] = None  # e.g., True
    safety: Optional[bool] = None  # e.g., True
    quality: Optional[str] = None  # e.g., "Standard"

    # Layout and format
    aspect_ratio: Optional[str] = None  # e.g., "1:1", "16:9"

    # Additional settings
    locale: Optional[str] = "en"  # Language/locale setting


class ProductItem(BaseModel):
    """Individual product item for media generation."""

    product_id: Union[int, str]  # Product identifier (support both int and str)
    prompt: Optional[str] = None  # Generation prompt for this product
    reference_image_urls: Optional[List[str]] = None  # Reference images
    store_id: Optional[int] = None  # Store identifier

    # Enhanced product data (can be full ProductSchema or minimal data)
    product_data: Optional[ProductSchema] = None


class MediaGenerateRequest(BaseSchema):
    """Request to generate media for products - matches user payload structure exactly."""

    # Core generation parameters (matches user payload order)
    mode: str  # 'image', 'video', 'text'
    model: Optional[str] = None  # 'banana', 'veo3', 'openai', etc.

    # Detailed generation settings
    settings: Optional[GenerationSettings] = None

    # Product items with individual prompts and references
    items: Optional[List[ProductItem]] = None

    # Optional enhancement fields
    template_id: Optional[str] = None

    # Batch processing options
    batch_size: Optional[int] = Field(default=1, ge=1, le=10)
    priority: Optional[str] = "normal"  # low, normal, high


class MediaJobInfo(BaseModel):
    """Job information in generate response."""

    product_id: int
    job_id: str  # Changed to str to support external UUID
    status: str
    celery_task_id: Optional[str] = None  # Add Celery task ID


class MediaGenerateResponse(BaseSchema):
    """Response from generate endpoint."""
    
    jobs: List[MediaJobInfo]


class MediaVariantInfo(BaseModel):
    """Media variant information."""
    
    variant_id: int
    variant_name: str
    status: str
    video_url: Optional[str] = None
    image_url: Optional[str] = None
    thumbnail_url: Optional[str] = None
    duration: Optional[float] = None


class MediaJobStatusResponse(BaseSchema):
    """Job status response."""
    
    job_id: int
    status: str
    progress: float
    variants: List[MediaVariantInfo]


class PublishOptions(BaseModel):
    """Options for publishing media."""
    alt_text: Optional[str] = None
    position: Optional[int] = None
    replace_existing: bool = False


class MediaPushRequest(BaseSchema):
    """Request to push media to Store."""

    shop_id: int
    product_id: int
    variant_id: int
    publish_targets: List[str] = ["shopify"]  # e.g. shopify, tiktok, youtube
    publish_options: Optional[PublishOptions] = None


class MediaPushResponse(BaseSchema):
    """Response from push endpoint."""
    
    push_id: str
    status: str
    message: str


class MediaGenerationRequest(BaseModel):
    """Internal request schema for media generation providers."""

    # Product context (enhanced)
    product_data: ProductSchema
    media_type: str  # text, image, video

    # Generation configuration
    template_id: Optional[str] = None
    custom_config: Optional[Dict[str, Any]] = None

    # Provider-specific fields
    num_variants: Optional[int] = 4
    aspect_ratio: Optional[str] = "1:1"
    style: Optional[str] = "professional"
    model: Optional[str] = None
    settings: Optional[Dict[str, Any]] = None

    # Quality and safety
    quality_threshold: Optional[float] = 0.8
    safety_checks: bool = True

    # Idempotency and versioning
    idempotency_key: Optional[str] = None
    generation_version: str = "1.0"


class GeneratedAsset(BaseModel):
    """Individual generated asset."""
    asset_id: str
    asset_type: str  # image, video, text
    url: Optional[str] = None
    content: Optional[str] = None  # For text assets
    metadata: Dict[str, Any] = Field(default_factory=dict)
    quality_score: Optional[float] = None
    alt_text: Optional[str] = None
    file_size: Optional[int] = None
    dimensions: Optional[str] = None  # e.g., "1024x1024"
    format: Optional[str] = None


class MediaGenerationResult(BaseModel):
    """Result from media generation."""
    success: bool
    provider_job_id: Optional[str] = None

    # Generated assets
    assets: List[GeneratedAsset] = Field(default_factory=list)

    # Legacy fields for backward compatibility
    images: Optional[List[Dict[str, Any]]] = None
    variants: Optional[List[Dict[str, Any]]] = None

    # Processing information
    estimated_completion_time: Optional[int] = None
    actual_processing_time: Optional[float] = None

    # Quality and validation
    overall_quality_score: Optional[float] = None
    validation_results: Dict[str, Any] = Field(default_factory=dict)
    needs_manual_review: bool = False

    # Error handling
    error_message: Optional[str] = None
    warnings: List[str] = Field(default_factory=list)

    # Cost and usage tracking
    cost_estimate: Optional[float] = None
    tokens_used: Optional[int] = None

    # Idempotency
    idempotency_key: Optional[str] = None


class MediaJobListResponse(BaseSchema):
    """Response for listing media jobs."""
    jobs: List[Dict[str, Any]]  # Simplified, could use MediaJobInfo
    total: int
    page: int
    per_page: int


