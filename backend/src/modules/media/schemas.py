"""
Media Generation Schemas
"""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, validator

from core.schemas.base_schemas import BaseSchema


class GenerationSettings(BaseModel):
    """Detailed generation settings for media creation."""

    # Image/Video settings
    size: Optional[str] = None  # e.g., "1024x1024"
    guidance: Optional[float] = None  # e.g., 7.5
    steps: Optional[int] = None  # e.g., 25
    strength: Optional[float] = None  # e.g., 0.8
    seed: Optional[int] = None  # e.g., 91678

    # Quality and processing
    upscale: Optional[bool] = None  # e.g., True
    safety: Optional[bool] = None  # e.g., True
    quality: Optional[str] = None  # e.g., "Standard"

    # Layout and format
    aspect_ratio: Optional[str] = None  # e.g., "1:1", "16:9"

    # Additional settings
    locale: Optional[str] = "en"  # Language/locale setting

class ProductItem(BaseModel):
    """Individual product item for media generation."""

    product_id: int  # Product identifier
    prompt: str  # Generation prompt for this product
    reference_image_urls: Optional[List[str]] = None  # Reference images
    store_id: Optional[int] = None  # Store identifier (avoid duplication with shop_id)


class MediaGenerateRequest(BaseSchema):
    """Request to generate media for products - matches user payload structure exactly."""

    # Core generation parameters (matches user payload order)
    mode: str  # 'image', 'video'
    model: Optional[str] = None  # 'banana', 'veo3', etc.

    # Detailed generation settings
    settings: Optional[GenerationSettings] = None

    # Product items with individual prompts and references
    items: Optional[List[ProductItem]] = None

    # Optional enhancement fields
    template_id: Optional[str] = None


class MediaJobInfo(BaseModel):
    """Job information in generate response."""

    product_id: int
    job_id: str  # Changed to str to support external UUID
    status: str
    celery_task_id: Optional[str] = None  # Add Celery task ID


class MediaGenerateResponse(BaseSchema):
    """Response from generate endpoint."""
    
    jobs: List[MediaJobInfo]


class MediaVariantInfo(BaseModel):
    """Media variant information."""
    
    variant_id: int
    variant_name: str
    status: str
    video_url: Optional[str] = None
    image_url: Optional[str] = None
    thumbnail_url: Optional[str] = None
    duration: Optional[float] = None


class MediaJobStatusResponse(BaseSchema):
    """Job status response."""
    
    job_id: int
    status: str
    progress: float
    variants: List[MediaVariantInfo]


class PublishOptions(BaseModel):
    """Options for publishing media."""
    alt_text: Optional[str] = None
    position: Optional[int] = None
    replace_existing: bool = False


class MediaPushRequest(BaseSchema):
    """Request to push media to Store."""

    shop_id: int
    product_id: int
    variant_id: int
    publish_targets: List[str] = ["shopify"]  # e.g. shopify, tiktok, youtube
    publish_options: Optional[PublishOptions] = None


class MediaPushResponse(BaseSchema):
    """Response from push endpoint."""
    
    push_id: str
    status: str
    message: str


class MediaGenerationRequest(BaseModel):
    """Internal request schema for media generation providers."""
    product_title: str
    media_type: str
    template_id: Optional[str] = None
    custom_config: Optional[Dict[str, Any]] = None
    # Provider-specific fields
    num_images: Optional[int] = 4
    variants_count: Optional[int] = 4
    aspect_ratio: Optional[str] = "1:1"
    style: Optional[str] = "professional"
    model: Optional[str] = None
    settings: Optional[Dict[str, Any]] = None


class MediaGenerationResult(BaseModel):
    """Result from media generation."""
    success: bool
    provider_job_id: Optional[str] = None
    images: Optional[List[Dict[str, Any]]] = None
    variants: Optional[List[Dict[str, Any]]] = None
    estimated_completion_time: Optional[int] = None
    error_message: Optional[str] = None


class MediaJobListResponse(BaseSchema):
    """Response for listing media jobs."""
    jobs: List[Dict[str, Any]]  # Simplified, could use MediaJobInfo
    total: int
    page: int
    per_page: int


