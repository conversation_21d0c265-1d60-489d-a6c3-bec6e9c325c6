"""
Media Generation API Router
"""

import logging
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func

from core.db.database import get_db

from modules.auth.models import User, Tenant
from modules.auth.router import get_current_user
from modules.queue.queue_service import celery_service as job_queue_service, TaskPriority
from .models import (
    MediaJob, MediaVariant, MediaJobStatus, MediaVariantStatus
)
from .schemas import (
    MediaGenerateRequest, MediaGenerateResponse, MediaJobStatusResponse,
    MediaPushRequest, MediaPushResponse, MediaJobListResponse,
    MediaVariantInfo, MediaJobInfo
)
from .service import media_service
from .unified_media_service import unified_media_service, MediaGenerationRequest as UnifiedRequest
from modules.stores.models import Store

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/generate", response_model=MediaGenerateResponse)
async def generate_media(
    request: MediaGenerateRequest,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Generate AI media (text, images, videos) for selected products.

    Body: {shopId, productIds[], mediaTypes[], contentTypes?, platforms?, styles?}
    Returns: job IDs per product and media type
    """
    try:
        # Get user's tenant
        tenant_result = await db.execute(select(Tenant).filter(Tenant.owner_id == current_user.id))
        tenant = tenant_result.scalar_one_or_none()
        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tenant not found"
            )

        # Validate shop ownership
        # Get user's stores
        user_stores_result = await db.execute(select(Store).filter(Store.owner_id == current_user.id))
        user_stores = user_stores_result.scalars().all()
        if not user_stores:
            raise HTTPException(
                status_code=404,
                detail="No stores found for user"
            )

        store_ids = [store.id for store in user_stores]

        # Check if the requested shop_id belongs to user's stores
        if hasattr(request, 'shop_id') and request.shop_id:
            if request.shop_id not in store_ids:
                raise HTTPException(
                    status_code=403,
                    detail="Access denied: shop does not belong to user"
                )

        # Create video generation jobs with full payload
        jobs = await media_service.create_generation_jobs(
            db=db,
            user_id=current_user.id,  # Use user ID directly
            request=request
        )

        # Queue generation tasks using Celery
        from modules.queue.queue_service import celery_service
        import uuid

        job_responses = []
        for job in jobs:
            # Generate external job ID
            external_job_id = str(uuid.uuid4())

            # Update job with external ID
            job.job_id = external_job_id
            await db.commit()

            # Enqueue the task using Celery
            celery_task_id = celery_service.enqueue_media_generation(
                user_id=current_user.id,
                job_id=job.id,
                product_ids=[job.product_id],
                media_type=job.media_type,
                template_id=job.template_id,
                voice_id=job.voice_id,
                text_input=job.script,
                full_payload=job.full_payload  # Pass the complete payload
            )

            job_responses.append({
                "product_id": job.product_id,
                "job_id": external_job_id,
                "celery_task_id": celery_task_id,
                "status": job.status.value
            })

        return MediaGenerateResponse(jobs=job_responses)
        
    except Exception as e:
        logger.exception(f"Error generating videos: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/jobs/{job_id}", response_model=MediaJobStatusResponse)
async def get_job_status(
    job_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Get job status, progress, and variant IDs.
    """
    try:
        job = await media_service.get_job_with_variants(db, job_id)
        if not job:
            raise HTTPException(status_code=404, detail="Job not found")
        
        # Add ownership validation
        # Check if job belongs to current user
        if hasattr(job, 'user_id') and job.user_id != current_user.id:
            raise HTTPException(
                status_code=403,
                detail="Access denied: job does not belong to user"
            )

        return MediaJobStatusResponse(
            job_id=job.id,
            status=job.status.value,
            progress=job.progress_percentage,
            variants=[{
                "variant_id": variant.id,
                "variant_name": variant.variant_name,
                "status": variant.status.value,
                "video_url": variant.video_url,
                "image_url": variant.image_url,
                "voice_url": variant.voice_url,
                "thumbnail_url": variant.thumbnail_url,
                "duration": variant.duration_seconds
            } for variant in job.variants] if hasattr(job, 'variants') else []
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting job status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/push", response_model=MediaPushResponse)
async def push_to_platform(
    request: MediaPushRequest,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Push selected video variant to connected store's product media.

    Body: {shopId, productId, variantId, publishTargets, publishOptions}
    """
    try:
        # Validate store ownership and permissions
        # Get user's stores
        user_stores_result = await db.execute(select(Store).filter(Store.owner_id == current_user.id))
        user_stores = user_stores_result.scalars().all()
        if not user_stores:
            raise HTTPException(
                status_code=404,
                detail="No stores found for user"
            )

        store_ids = [store.id for store in user_stores]

        # Check if the requested shop_id belongs to user's stores
        if request.shop_id not in store_ids:
            raise HTTPException(
                status_code=403,
                detail="Access denied: store does not belong to user"
            )

        # Find the store to determine platform
        store_result = await db.execute(select(Store).filter(
            Store.id == request.shop_id,
            Store.owner_id == current_user.id
        ))
        store = store_result.scalar_one_or_none()

        if not store:
            raise HTTPException(
                status_code=404,
                detail="Store not found"
            )

        # Queue push task
        background_tasks.add_task(
            media_service.push_to_platform,
            request.shop_id,
            request.product_id,
            request.variant_id,
            request.publish_options
        )

        platform_name = store.platform.title()
        return MediaPushResponse(
            push_id=f"push_{request.variant_id}_{request.product_id}",
            status="queued",
            message=f"Push to {platform_name} queued"
        )

    except Exception as e:
        logger.error(f"Error pushing to platform: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/jobs", response_model=MediaJobListResponse)
async def list_jobs(
    page: int = Query(1, ge=1),
    per_page: int = Query(20, ge=1, le=100),
    status_filter: Optional[str] = Query(None),
    product_id: Optional[str] = Query(None),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    List media generation jobs for the current user.

    Supports filtering by status and product ID, with pagination.
    """
    try:
        # Use current user directly
        user_id = current_user.id

        # Build query
        query = select(MediaJob).filter(MediaJob.user_id == user_id)

        # Apply filters
        if status_filter:
            try:
                status_enum = MediaJobStatus(status_filter)
                query = query.filter(MediaJob.status == status_enum)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid status filter: {status_filter}"
                )

        if product_id:
            query = query.filter(MediaJob.product_id == product_id)

        # Get total count
        total = (await db.execute(select(func.count()).select_from(query.subquery()))).scalar_one()

        # Apply pagination
        offset = (page - 1) * per_page
        jobs = (await db.execute(query.order_by(MediaJob.created_at.desc()).offset(offset).limit(per_page))).scalars().all()

        # Convert to response models
        job_responses = []
        for job in jobs:
            variants = (await db.execute(select(MediaVariant).filter(MediaVariant.job_id == job.id))).scalars().all()
            variant_responses = [MediaVariantInfo.from_orm(v) for v in variants]

            job_response = MediaJobInfo.from_orm(job)
            job_response.variants = variant_responses
            job_responses.append(job_response)

        return MediaJobListResponse(
            jobs=job_responses,
            total=total,
            page=page,
            per_page=per_page
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Failed to list jobs: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list jobs: {str(e)}"
        )


@router.post("/ai-generate")
async def generate_ai_media(
    product_ids: List[str],
    media_types: List[str],
    content_types: Optional[List[str]] = None,
    platforms: Optional[List[str]] = None,
    styles: Optional[List[str]] = None,
    custom_config: Optional[Dict[str, Any]] = None,
    current_user: User = Depends(get_current_user),
):
    """
    Generate AI media content for products using the unified pipeline.

    This is the main endpoint for AI media generation.

    Args:
        product_ids: List of product IDs to generate media for
        media_types: Types of media to generate ["text", "image", "video"]
        content_types: Specific content types ["seo_title", "description", "hero_image"]
        platforms: Target platforms ["instagram", "website", "email"]
        styles: Content styles ["hero", "lifestyle", "social"]
        custom_config: Additional configuration options

    Returns:
        Complete media generation result with assets per product

    Example request:
    ```
    POST /media/ai-generate
    {
        "product_ids": ["sneaker_001", "dress_001"],
        "media_types": ["text", "image"],
        "content_types": ["seo_title", "description", "hero_image"],
        "platforms": ["website", "instagram"],
        "styles": ["hero", "lifestyle"]
    }
    ```
    """
    try:
        # Get user's tenant
        tenant_id = 1  # Simplified - would get from user context

        # Create unified request
        unified_request = UnifiedRequest(
            product_ids=product_ids,
            media_types=media_types,
            content_types=content_types,
            platforms=platforms,
            styles=styles,
            custom_config=custom_config or {},
            tenant_id=tenant_id
        )

        # Generate media using unified service
        result = await unified_media_service.generate_media(unified_request)

        # Format response
        response = {
            "request_id": result.request_id,
            "success": result.success,
            "total_processing_time": result.total_processing_time,
            "total_cost": result.total_cost,
            "summary": result.summary,
            "products": []
        }

        # Add product results
        for product_result in result.products:
            product_response = {
                "product_id": product_result.product_id,
                "success": product_result.success,
                "processing_time": product_result.processing_time,
                "total_cost": product_result.total_cost,
                "quality_summary": product_result.quality_summary,
                "error_message": product_result.error_message,
                "assets": []
            }

            # Add assets
            for asset in product_result.assets:
                asset_response = {
                    "asset_id": asset.asset_id,
                    "media_type": asset.media_type,
                    "content_type": asset.content_type,
                    "url": asset.url,
                    "content": asset.content,
                    "quality_score": asset.quality_score,
                    "needs_review": asset.needs_review,
                    "metadata": asset.metadata,
                    "file_size": asset.file_size,
                    "dimensions": asset.dimensions,
                    "format": asset.format
                }
                product_response["assets"].append(asset_response)

            response["products"].append(product_response)

        return response

    except Exception as e:
        logger.exception(f"AI media generation failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"AI media generation failed: {str(e)}"
        )

