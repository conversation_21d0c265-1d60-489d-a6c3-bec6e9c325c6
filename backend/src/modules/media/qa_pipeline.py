"""
Quality Assurance and Validation Pipeline for Generated Media Content
Handles safety checks, visual quality assessment, content policy validation, and human review flagging.
"""

import logging
import re
import hashlib
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from enum import Enum
from dataclasses import dataclass

from .schemas import (
    ProductSchema, 
    GeneratedAsset,
    QualityCheck,
    ValidationResult,
    SafetyFlags
)

logger = logging.getLogger(__name__)


class QualityLevel(Enum):
    """Quality assessment levels."""
    EXCELLENT = "excellent"
    GOOD = "good"
    ACCEPTABLE = "acceptable"
    POOR = "poor"
    UNACCEPTABLE = "unacceptable"


class ReviewPriority(Enum):
    """Human review priority levels."""
    IMMEDIATE = "immediate"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    NONE = "none"


@dataclass
class QualityAssessment:
    """Comprehensive quality assessment result."""
    overall_score: float  # 0.0 to 1.0
    quality_level: QualityLevel
    safety_score: float
    content_score: float
    technical_score: float
    brand_alignment_score: float
    issues: List[str]
    recommendations: List[str]
    needs_human_review: bool
    review_priority: ReviewPriority
    auto_approve: bool


class MediaQualityAssurance:
    """
    Comprehensive quality assurance pipeline for generated media content.
    """
    
    def __init__(self):
        self.safety_keywords = self._load_safety_keywords()
        self.brand_guidelines = self._load_brand_guidelines()
        self.quality_thresholds = {
            "auto_approve": 0.85,
            "human_review": 0.60,
            "auto_reject": 0.30
        }
    
    async def assess_content_quality(
        self,
        asset: GeneratedAsset,
        product_data: ProductSchema,
        context: Optional[Dict[str, Any]] = None
    ) -> QualityAssessment:
        """
        Perform comprehensive quality assessment on generated content.
        
        Args:
            asset: Generated asset to assess
            product_data: Product context
            context: Additional context for assessment
            
        Returns:
            QualityAssessment with detailed results
        """
        try:
            assessment_start = datetime.now()
            
            # Initialize assessment
            assessment = QualityAssessment(
                overall_score=0.0,
                quality_level=QualityLevel.POOR,
                safety_score=0.0,
                content_score=0.0,
                technical_score=0.0,
                brand_alignment_score=0.0,
                issues=[],
                recommendations=[],
                needs_human_review=False,
                review_priority=ReviewPriority.NONE,
                auto_approve=False
            )
            
            # Perform different assessments based on asset type
            if asset.asset_type == "text":
                assessment = await self._assess_text_quality(asset, product_data, assessment)
            elif asset.asset_type == "image":
                assessment = await self._assess_image_quality(asset, product_data, assessment)
            elif asset.asset_type == "video":
                assessment = await self._assess_video_quality(asset, product_data, assessment)
            
            # Calculate overall score
            assessment.overall_score = self._calculate_overall_score(assessment)
            
            # Determine quality level
            assessment.quality_level = self._determine_quality_level(assessment.overall_score)
            
            # Determine review requirements
            assessment.needs_human_review, assessment.review_priority = self._determine_review_needs(assessment)
            
            # Determine auto-approval
            assessment.auto_approve = self._can_auto_approve(assessment)
            
            # Log assessment
            processing_time = (datetime.now() - assessment_start).total_seconds()
            logger.info(
                f"Quality assessment completed for {asset.asset_id} - "
                f"Score: {assessment.overall_score:.2f}, "
                f"Level: {assessment.quality_level.value}, "
                f"Review: {assessment.needs_human_review}, "
                f"Time: {processing_time:.2f}s"
            )
            
            return assessment
            
        except Exception as e:
            logger.error(f"Quality assessment failed for {asset.asset_id}: {e}")
            
            # Return safe default assessment
            return QualityAssessment(
                overall_score=0.0,
                quality_level=QualityLevel.UNACCEPTABLE,
                safety_score=0.0,
                content_score=0.0,
                technical_score=0.0,
                brand_alignment_score=0.0,
                issues=[f"Assessment failed: {str(e)}"],
                recommendations=["Manual review required"],
                needs_human_review=True,
                review_priority=ReviewPriority.HIGH,
                auto_approve=False
            )
    
    async def _assess_text_quality(
        self,
        asset: GeneratedAsset,
        product_data: ProductSchema,
        assessment: QualityAssessment
    ) -> QualityAssessment:
        """Assess text content quality."""
        content = asset.content or ""
        
        # Safety assessment
        safety_issues = self._check_text_safety(content)
        assessment.safety_score = 1.0 - (len(safety_issues) * 0.2)
        assessment.issues.extend(safety_issues)
        
        # Content quality assessment
        content_issues = self._check_text_content_quality(content, product_data)
        assessment.content_score = 1.0 - (len(content_issues) * 0.15)
        assessment.issues.extend(content_issues)
        
        # Technical assessment (grammar, spelling, formatting)
        technical_issues = self._check_text_technical_quality(content)
        assessment.technical_score = 1.0 - (len(technical_issues) * 0.1)
        assessment.issues.extend(technical_issues)
        
        # Brand alignment assessment
        brand_issues = self._check_brand_alignment(content, product_data)
        assessment.brand_alignment_score = 1.0 - (len(brand_issues) * 0.2)
        assessment.issues.extend(brand_issues)
        
        # Add recommendations
        if assessment.content_score < 0.7:
            assessment.recommendations.append("Consider regenerating with more specific prompts")
        if assessment.technical_score < 0.8:
            assessment.recommendations.append("Review grammar and formatting")
        if assessment.brand_alignment_score < 0.7:
            assessment.recommendations.append("Adjust tone to match brand guidelines")
        
        return assessment
    
    async def _assess_image_quality(
        self,
        asset: GeneratedAsset,
        product_data: ProductSchema,
        assessment: QualityAssessment
    ) -> QualityAssessment:
        """Assess image content quality."""
        
        # Safety assessment (would integrate with image moderation API)
        assessment.safety_score = await self._check_image_safety(asset)
        
        # Technical quality (resolution, format, file size)
        technical_score, technical_issues = self._check_image_technical_quality(asset)
        assessment.technical_score = technical_score
        assessment.issues.extend(technical_issues)
        
        # Content relevance (would use computer vision)
        content_score, content_issues = await self._check_image_content_relevance(asset, product_data)
        assessment.content_score = content_score
        assessment.issues.extend(content_issues)
        
        # Brand alignment (colors, style, composition)
        brand_score, brand_issues = self._check_image_brand_alignment(asset, product_data)
        assessment.brand_alignment_score = brand_score
        assessment.issues.extend(brand_issues)
        
        # Add recommendations
        if assessment.technical_score < 0.7:
            assessment.recommendations.append("Consider higher resolution or better compression")
        if assessment.content_score < 0.7:
            assessment.recommendations.append("Regenerate with more specific product context")
        
        return assessment
    
    async def _assess_video_quality(
        self,
        asset: GeneratedAsset,
        product_data: ProductSchema,
        assessment: QualityAssessment
    ) -> QualityAssessment:
        """Assess video content quality."""
        
        # Safety assessment
        assessment.safety_score = await self._check_video_safety(asset)
        
        # Technical quality (resolution, format, duration)
        technical_score, technical_issues = self._check_video_technical_quality(asset)
        assessment.technical_score = technical_score
        assessment.issues.extend(technical_issues)
        
        # Content quality (relevance, engagement)
        content_score, content_issues = await self._check_video_content_quality(asset, product_data)
        assessment.content_score = content_score
        assessment.issues.extend(content_issues)
        
        # Brand alignment
        brand_score, brand_issues = self._check_video_brand_alignment(asset, product_data)
        assessment.brand_alignment_score = brand_score
        assessment.issues.extend(brand_issues)
        
        return assessment
    
    def _check_text_safety(self, content: str) -> List[str]:
        """Check text content for safety issues."""
        issues = []
        
        # Check for inappropriate content
        for keyword in self.safety_keywords.get("inappropriate", []):
            if keyword.lower() in content.lower():
                issues.append(f"Contains inappropriate content: {keyword}")
        
        # Check for misleading claims
        misleading_patterns = [
            r"100% guaranteed",
            r"miracle cure",
            r"instant results",
            r"no side effects"
        ]
        
        for pattern in misleading_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                issues.append(f"Contains potentially misleading claim: {pattern}")
        
        # Check for spam indicators
        if len(re.findall(r'[!]{2,}', content)) > 2:
            issues.append("Excessive exclamation marks (spam indicator)")
        
        if len(re.findall(r'[A-Z]{3,}', content)) > 3:
            issues.append("Excessive capitalization (spam indicator)")
        
        return issues
    
    def _check_text_content_quality(self, content: str, product_data: ProductSchema) -> List[str]:
        """Check text content quality and relevance."""
        issues = []
        
        # Check length appropriateness
        if len(content) < 10:
            issues.append("Content too short")
        elif len(content) > 2000:
            issues.append("Content too long")
        
        # Check product relevance
        product_terms = [
            product_data.title.lower(),
            product_data.brand.lower(),
            product_data.category.lower()
        ]
        
        content_lower = content.lower()
        relevant_terms_found = sum(1 for term in product_terms if term in content_lower)
        
        if relevant_terms_found == 0:
            issues.append("Content not relevant to product")
        
        # Check for generic content
        generic_phrases = [
            "this product",
            "our item",
            "this thing",
            "great quality"
        ]
        
        generic_count = sum(1 for phrase in generic_phrases if phrase in content_lower)
        if generic_count > 2:
            issues.append("Content too generic")
        
        return issues
    
    def _check_text_technical_quality(self, content: str) -> List[str]:
        """Check text technical quality (grammar, formatting)."""
        issues = []
        
        # Basic grammar checks
        if not content.strip():
            issues.append("Empty content")
            return issues
        
        # Check sentence structure
        sentences = content.split('.')
        if len(sentences) > 1:
            for sentence in sentences:
                sentence = sentence.strip()
                if sentence and not sentence[0].isupper():
                    issues.append("Inconsistent capitalization")
                    break
        
        # Check for repeated words
        words = content.lower().split()
        for i in range(len(words) - 1):
            if words[i] == words[i + 1] and len(words[i]) > 3:
                issues.append("Repeated words detected")
                break
        
        return issues
    
    def _check_brand_alignment(self, content: str, product_data: ProductSchema) -> List[str]:
        """Check content alignment with brand guidelines."""
        issues = []
        
        brand_tone = product_data.shop_branding.tone.value
        content_lower = content.lower()
        
        # Check tone alignment
        tone_indicators = {
            "luxury": ["premium", "exclusive", "sophisticated", "elegant"],
            "friendly": ["love", "enjoy", "perfect", "amazing"],
            "technical": ["features", "specifications", "performance", "quality"],
            "minimal": ["simple", "clean", "essential", "pure"],
            "playful": ["fun", "exciting", "vibrant", "colorful"]
        }
        
        expected_indicators = tone_indicators.get(brand_tone, [])
        found_indicators = sum(1 for indicator in expected_indicators if indicator in content_lower)
        
        if found_indicators == 0 and expected_indicators:
            issues.append(f"Content doesn't match {brand_tone} brand tone")
        
        return issues
    
    async def _check_image_safety(self, asset: GeneratedAsset) -> float:
        """Check image safety using moderation APIs."""
        # Placeholder for image moderation API integration
        # In production, this would call services like AWS Rekognition, Google Vision, etc.
        logger.info(f"Image safety check for {asset.asset_id} - would integrate with moderation API")
        return 0.95  # Default safe score
    
    def _check_image_technical_quality(self, asset: GeneratedAsset) -> Tuple[float, List[str]]:
        """Check image technical quality."""
        issues = []
        score = 1.0
        
        # Check dimensions
        if asset.dimensions:
            width, height = map(int, asset.dimensions.split('x'))
            if width < 300 or height < 300:
                issues.append("Resolution too low")
                score -= 0.3
        
        # Check file size
        if asset.file_size:
            if asset.file_size > 5 * 1024 * 1024:  # 5MB
                issues.append("File size too large")
                score -= 0.2
            elif asset.file_size < 10 * 1024:  # 10KB
                issues.append("File size suspiciously small")
                score -= 0.2
        
        # Check format
        if asset.format not in ["webp", "jpg", "png"]:
            issues.append("Unsupported image format")
            score -= 0.1
        
        return max(score, 0.0), issues
    
    async def _check_image_content_relevance(self, asset: GeneratedAsset, product_data: ProductSchema) -> Tuple[float, List[str]]:
        """Check image content relevance using computer vision."""
        # Placeholder for computer vision analysis
        logger.info(f"Image content analysis for {asset.asset_id} - would use computer vision")
        return 0.85, []  # Default good score
    
    def _check_image_brand_alignment(self, asset: GeneratedAsset, product_data: ProductSchema) -> Tuple[float, List[str]]:
        """Check image brand alignment."""
        issues = []
        score = 0.8  # Default score
        
        # Check if brand colors are represented (placeholder)
        # In production, this would analyze image colors
        
        return score, issues
    
    async def _check_video_safety(self, asset: GeneratedAsset) -> float:
        """Check video safety."""
        logger.info(f"Video safety check for {asset.asset_id} - would integrate with video moderation")
        return 0.90  # Default safe score
    
    def _check_video_technical_quality(self, asset: GeneratedAsset) -> Tuple[float, List[str]]:
        """Check video technical quality."""
        issues = []
        score = 1.0
        
        # Check file size
        if asset.file_size and asset.file_size > 50 * 1024 * 1024:  # 50MB
            issues.append("Video file too large")
            score -= 0.2
        
        return max(score, 0.0), issues
    
    async def _check_video_content_quality(self, asset: GeneratedAsset, product_data: ProductSchema) -> Tuple[float, List[str]]:
        """Check video content quality."""
        logger.info(f"Video content analysis for {asset.asset_id} - would analyze video content")
        return 0.80, []  # Default score
    
    def _check_video_brand_alignment(self, asset: GeneratedAsset, product_data: ProductSchema) -> Tuple[float, List[str]]:
        """Check video brand alignment."""
        return 0.75, []  # Default score
    
    def _calculate_overall_score(self, assessment: QualityAssessment) -> float:
        """Calculate weighted overall quality score."""
        weights = {
            "safety": 0.4,      # Safety is most important
            "content": 0.3,     # Content relevance
            "technical": 0.2,   # Technical quality
            "brand": 0.1        # Brand alignment
        }
        
        return (
            assessment.safety_score * weights["safety"] +
            assessment.content_score * weights["content"] +
            assessment.technical_score * weights["technical"] +
            assessment.brand_alignment_score * weights["brand"]
        )
    
    def _determine_quality_level(self, score: float) -> QualityLevel:
        """Determine quality level from score."""
        if score >= 0.9:
            return QualityLevel.EXCELLENT
        elif score >= 0.8:
            return QualityLevel.GOOD
        elif score >= 0.6:
            return QualityLevel.ACCEPTABLE
        elif score >= 0.3:
            return QualityLevel.POOR
        else:
            return QualityLevel.UNACCEPTABLE
    
    def _determine_review_needs(self, assessment: QualityAssessment) -> Tuple[bool, ReviewPriority]:
        """Determine if human review is needed and priority."""
        score = assessment.overall_score
        
        # Always review if safety score is low
        if assessment.safety_score < 0.7:
            return True, ReviewPriority.IMMEDIATE
        
        # Review based on overall score
        if score < self.quality_thresholds["auto_reject"]:
            return True, ReviewPriority.HIGH
        elif score < self.quality_thresholds["human_review"]:
            return True, ReviewPriority.MEDIUM
        elif score < self.quality_thresholds["auto_approve"]:
            return True, ReviewPriority.LOW
        
        return False, ReviewPriority.NONE
    
    def _can_auto_approve(self, assessment: QualityAssessment) -> bool:
        """Determine if content can be auto-approved."""
        return (
            assessment.overall_score >= self.quality_thresholds["auto_approve"] and
            assessment.safety_score >= 0.9 and
            not assessment.needs_human_review
        )
    
    def _load_safety_keywords(self) -> Dict[str, List[str]]:
        """Load safety keywords for content filtering."""
        return {
            "inappropriate": [
                "hate", "violence", "explicit", "illegal", "harmful"
            ],
            "spam": [
                "click here", "limited time", "act now", "guaranteed"
            ]
        }
    
    def _load_brand_guidelines(self) -> Dict[str, Any]:
        """Load brand guidelines for alignment checking."""
        return {
            "tone_requirements": {
                "luxury": ["sophisticated", "premium", "exclusive"],
                "friendly": ["warm", "approachable", "helpful"],
                "technical": ["precise", "detailed", "informative"]
            }
        }


# Global QA pipeline instance
qa_pipeline = MediaQualityAssurance()
