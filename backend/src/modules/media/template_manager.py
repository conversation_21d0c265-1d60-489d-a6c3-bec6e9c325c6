"""
Template Manager for Media Generation
Handles loading, rendering, and managing prompt templates for text, image, and video generation.
"""

import json
import logging
import re
from pathlib import Path
from typing import Dict, Any, List, Optional, Union
from jinja2 import Template, Environment, BaseLoader, TemplateNotFound

from .schemas import ProductSchema, ShopBrandingTone

logger = logging.getLogger(__name__)


class TemplateLoader(BaseLoader):
    """Custom template loader for media generation templates."""
    
    def __init__(self, templates_data: Dict[str, Any]):
        self.templates_data = templates_data
    
    def get_source(self, environment: Environment, template: str) -> tuple:
        """Get template source."""
        # Parse template path like "text/seo/meta_title"
        parts = template.split('/')
        if len(parts) < 3:
            raise TemplateNotFound(template)
        
        media_type, category, template_name = parts[0], parts[1], parts[2]
        
        try:
            template_data = self.templates_data[f"{media_type}_templates"][category][template_name]
            source = template_data.get("template", "")
            return source, None, lambda: True
        except KeyError:
            raise TemplateNotFound(template)


class MediaTemplateManager:
    """
    Manages prompt templates for media generation.
    
    Provides functionality to:
    - Load templates from JSON configuration
    - Render templates with product data
    - Validate template constraints
    - Support multiple media types (text, image, video)
    """
    
    def __init__(self, templates_file: Optional[str] = None):
        """
        Initialize template manager.
        
        Args:
            templates_file: Path to templates JSON file
        """
        self.templates_file = templates_file or str(Path(__file__).parent / "templates.json")
        self.templates_data: Dict[str, Any] = {}
        self.jinja_env: Optional[Environment] = None
        self._load_templates()
    
    def _load_templates(self) -> None:
        """Load templates from JSON file."""
        try:
            with open(self.templates_file, 'r', encoding='utf-8') as f:
                self.templates_data = json.load(f)
            
            # Initialize Jinja2 environment with custom loader
            loader = TemplateLoader(self.templates_data)
            self.jinja_env = Environment(loader=loader, autoescape=False)
            
            logger.info(f"Loaded templates from {self.templates_file}")
            
        except FileNotFoundError:
            logger.error(f"Templates file not found: {self.templates_file}")
            self.templates_data = {}
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in templates file: {e}")
            self.templates_data = {}
        except Exception as e:
            logger.error(f"Error loading templates: {e}")
            self.templates_data = {}
    
    def reload_templates(self) -> bool:
        """Reload templates from file."""
        try:
            self._load_templates()
            return True
        except Exception as e:
            logger.error(f"Failed to reload templates: {e}")
            return False
    
    def get_available_templates(self, media_type: str, category: Optional[str] = None) -> List[str]:
        """
        Get list of available templates.
        
        Args:
            media_type: Type of media (text, image, video)
            category: Optional category filter
            
        Returns:
            List of template names
        """
        templates_key = f"{media_type}_templates"
        if templates_key not in self.templates_data:
            return []
        
        templates = []
        for cat, cat_templates in self.templates_data[templates_key].items():
            if category and cat != category:
                continue
            
            for template_name in cat_templates.keys():
                templates.append(f"{media_type}/{cat}/{template_name}")
        
        return templates
    
    def get_template_info(self, template_path: str) -> Optional[Dict[str, Any]]:
        """
        Get template information and metadata.
        
        Args:
            template_path: Template path like "text/seo/meta_title"
            
        Returns:
            Template information or None if not found
        """
        parts = template_path.split('/')
        if len(parts) < 3:
            return None
        
        media_type, category, template_name = parts[0], parts[1], parts[2]
        templates_key = f"{media_type}_templates"
        
        try:
            return self.templates_data[templates_key][category][template_name]
        except KeyError:
            return None
    
    def _prepare_product_context(self, product_data: ProductSchema) -> Dict[str, Any]:
        """
        Prepare product data for template rendering.
        
        Args:
            product_data: Product schema data
            
        Returns:
            Context dictionary for template rendering
        """
        # Convert ProductSchema to dict and handle nested objects
        context = product_data.dict()
        
        # Flatten shop_branding for easier access
        if 'shop_branding' in context:
            branding = context['shop_branding']
            context['shop_branding.tone'] = branding.get('tone', 'professional')
            context['shop_branding.primary_colors'] = branding.get('primary_colors', [])
            context['shop_branding.target_audience'] = branding.get('target_audience', '')
        
        # Ensure lists have safe access
        for list_field in ['colors', 'materials', 'usage_contexts', 'tags', 'sizes']:
            if list_field in context and context[list_field]:
                # Add indexed access for templates
                for i, item in enumerate(context[list_field]):
                    context[f"{list_field}[{i}]"] = item
            else:
                context[list_field] = []
        
        # Add derived fields
        context['primary_keyword'] = context.get('seo', {}).get('primary_keywords', [''])[0] if context.get('seo', {}).get('primary_keywords') else ''
        context['primary_keywords'] = ', '.join(context.get('seo', {}).get('primary_keywords', []))
        context['target_audience'] = context.get('shop_branding', {}).get('target_audience', 'customers')
        
        return context
    
    def render_template(
        self, 
        template_path: str, 
        product_data: ProductSchema,
        custom_context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Render a template with product data.
        
        Args:
            template_path: Template path like "text/seo/meta_title"
            product_data: Product data for rendering
            custom_context: Additional context variables
            
        Returns:
            Dictionary with rendered content and metadata
        """
        if not self.jinja_env:
            raise RuntimeError("Template environment not initialized")
        
        # Get template info
        template_info = self.get_template_info(template_path)
        if not template_info:
            raise ValueError(f"Template not found: {template_path}")
        
        # Prepare context
        context = self._prepare_product_context(product_data)
        if custom_context:
            context.update(custom_context)
        
        try:
            # Render template
            template = self.jinja_env.get_template(template_path)
            rendered_content = template.render(**context)
            
            # Validate constraints
            validation_result = self._validate_constraints(rendered_content, template_info)
            
            return {
                "content": rendered_content,
                "template_path": template_path,
                "system_prompt": template_info.get("system_prompt", ""),
                "constraints": template_info.get("constraints", {}),
                "parameters": template_info.get("parameters", {}),
                "validation": validation_result,
                "metadata": {
                    "template_info": template_info,
                    "context_used": list(context.keys())
                }
            }
            
        except Exception as e:
            logger.error(f"Error rendering template {template_path}: {e}")
            raise
    
    def _validate_constraints(self, content: str, template_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate rendered content against template constraints.
        
        Args:
            content: Rendered content
            template_info: Template information with constraints
            
        Returns:
            Validation result
        """
        constraints = template_info.get("constraints", {})
        validation = {
            "passed": True,
            "violations": [],
            "warnings": []
        }
        
        # Check length constraints
        if "max_length" in constraints:
            max_len = constraints["max_length"]
            if len(content) > max_len:
                validation["passed"] = False
                validation["violations"].append(f"Content length {len(content)} exceeds maximum {max_len}")
        
        # Check forbidden phrases
        if "forbidden_phrases" in constraints:
            content_lower = content.lower()
            for phrase in constraints["forbidden_phrases"]:
                if phrase.lower() in content_lower:
                    validation["passed"] = False
                    validation["violations"].append(f"Contains forbidden phrase: '{phrase}'")
        
        # Check required elements (basic check)
        if "required_elements" in constraints:
            content_lower = content.lower()
            for element in constraints["required_elements"]:
                # Simple check - could be enhanced with more sophisticated matching
                if element.lower() not in content_lower:
                    validation["warnings"].append(f"May be missing required element: '{element}'")
        
        return validation
    
    def get_category_templates(self, media_type: str, category: str) -> Dict[str, Any]:
        """
        Get all templates for a specific category.
        
        Args:
            media_type: Type of media (text, image, video)
            category: Category name
            
        Returns:
            Dictionary of templates in the category
        """
        templates_key = f"{media_type}_templates"
        try:
            return self.templates_data[templates_key][category]
        except KeyError:
            return {}
    
    def get_template_by_use_case(self, media_type: str, use_case: str, category: Optional[str] = None) -> Optional[str]:
        """
        Find template by use case (e.g., "hero", "lifestyle", "seo").
        
        Args:
            media_type: Type of media
            use_case: Use case identifier
            category: Optional category filter
            
        Returns:
            Template path or None if not found
        """
        templates = self.get_available_templates(media_type, category)
        
        for template_path in templates:
            if use_case in template_path.lower():
                return template_path
        
        return None


# Global template manager instance
template_manager = MediaTemplateManager()


def get_template_manager() -> MediaTemplateManager:
    """Get the global template manager instance."""
    return template_manager


def render_product_template(
    template_path: str,
    product_data: ProductSchema,
    custom_context: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Convenience function to render a template with product data.
    
    Args:
        template_path: Template path like "text/seo/meta_title"
        product_data: Product data
        custom_context: Additional context
        
    Returns:
        Rendered template result
    """
    return template_manager.render_template(template_path, product_data, custom_context)
