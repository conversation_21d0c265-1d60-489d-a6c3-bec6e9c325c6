"""
Unified Media Generation Service - Production-Ready E-commerce Media Pipeline
Orchestrates all components: context, prompts, generation, quality, and storage.
"""

import logging
import asyncio
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from dataclasses import dataclass

from .schemas import ProductSchema, EXAMPLE_PRODUCTS
from .context_engine import ProductContext, BrandContext, MarketContext
from .prompt_engine import PromptEngine, MediaType, Platform, PromptContext
from .quality_engine import QualityEngine, QualityMetrics, ValidationStatus
from .text_pipeline import text_pipeline
from .image_pipeline import image_pipeline
from .video_pipeline import video_pipeline
from .enhanced_storage import get_enhanced_media_storage
from .enhanced_provider_management import enhanced_provider_manager

logger = logging.getLogger(__name__)


@dataclass
class MediaGenerationRequest:
    """Unified request for media generation."""
    product_ids: List[str]
    media_types: List[str]  # ["text", "image", "video"]
    platforms: Optional[List[str]] = None  # ["instagram", "website", "email"]
    styles: Optional[List[str]] = None  # ["hero", "lifestyle", "social"]
    custom_config: Optional[Dict[str, Any]] = None
    tenant_id: int = 1


@dataclass
class GeneratedMediaAsset:
    """Single generated media asset."""
    asset_id: str
    product_id: str
    media_type: str  # text, image, video
    content_type: str  # seo_title, hero_image, product_video
    url: Optional[str] = None
    content: Optional[str] = None
    metadata: Dict[str, Any] = None
    quality_score: float = 0.0
    needs_review: bool = False
    file_size: Optional[int] = None
    dimensions: Optional[str] = None
    format: Optional[str] = None


@dataclass
class ProductMediaResult:
    """Complete media generation result for a single product."""
    product_id: str
    success: bool
    assets: List[GeneratedMediaAsset]
    processing_time: float
    total_cost: float
    quality_summary: Dict[str, Any]
    error_message: Optional[str] = None


@dataclass
class MediaGenerationResult:
    """Complete media generation result for all products."""
    request_id: str
    success: bool
    products: List[ProductMediaResult]
    total_processing_time: float
    total_cost: float
    summary: Dict[str, Any]


class UnifiedMediaService:
    """
    Unified service that orchestrates the complete media generation pipeline.
    """
    
    def __init__(self):
        self.context_engine = None  # Will be initialized from existing context_engine.py
        self.prompt_engine = PromptEngine()
        self.quality_engine = QualityEngine()
        self.enhanced_storage = get_enhanced_media_storage()
        
    async def generate_media(self, request: MediaGenerationRequest) -> MediaGenerationResult:
        """
        Main entry point for media generation.
        
        Args:
            request: Media generation request
            
        Returns:
            Complete media generation result
        """
        start_time = datetime.now()
        request_id = f"req_{int(start_time.timestamp())}"
        
        logger.info(f"Starting media generation request {request_id} for {len(request.product_ids)} products")
        
        try:
            # Process each product
            product_results = []
            total_cost = 0.0
            
            for product_id in request.product_ids:
                product_result = await self._generate_media_for_product(
                    product_id, request, request_id
                )
                product_results.append(product_result)
                total_cost += product_result.total_cost
            
            # Calculate summary
            total_processing_time = (datetime.now() - start_time).total_seconds()
            successful_products = len([p for p in product_results if p.success])
            total_assets = sum(len(p.assets) for p in product_results)
            
            summary = {
                "total_products": len(request.product_ids),
                "successful_products": successful_products,
                "total_assets": total_assets,
                "average_quality_score": self._calculate_average_quality(product_results),
                "assets_needing_review": sum(
                    len([a for a in p.assets if a.needs_review]) for p in product_results
                ),
                "cost_breakdown": self._calculate_cost_breakdown(product_results)
            }
            
            return MediaGenerationResult(
                request_id=request_id,
                success=successful_products > 0,
                products=product_results,
                total_processing_time=total_processing_time,
                total_cost=total_cost,
                summary=summary
            )
            
        except Exception as e:
            logger.error(f"Media generation request {request_id} failed: {e}")
            return MediaGenerationResult(
                request_id=request_id,
                success=False,
                products=[],
                total_processing_time=(datetime.now() - start_time).total_seconds(),
                total_cost=0.0,
                summary={"error": str(e)}
            )
    
    async def _generate_media_for_product(
        self, 
        product_id: str, 
        request: MediaGenerationRequest,
        request_id: str
    ) -> ProductMediaResult:
        """Generate all requested media types for a single product."""
        start_time = datetime.now()
        
        try:
            # Get product data
            product_data = self._get_product_data(product_id)
            if not product_data:
                return ProductMediaResult(
                    product_id=product_id,
                    success=False,
                    assets=[],
                    processing_time=0.0,
                    total_cost=0.0,
                    quality_summary={},
                    error_message=f"Product {product_id} not found"
                )
            
            # Generate context
            contexts = await self._generate_contexts(product_data, request)
            
            # Generate media for each type
            all_assets = []
            total_cost = 0.0
            
            for media_type in request.media_types:
                assets, cost = await self._generate_media_type(
                    product_data, media_type, contexts, request
                )
                all_assets.extend(assets)
                total_cost += cost
            
            # Calculate quality summary
            quality_summary = self._calculate_quality_summary(all_assets)
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            return ProductMediaResult(
                product_id=product_id,
                success=len(all_assets) > 0,
                assets=all_assets,
                processing_time=processing_time,
                total_cost=total_cost,
                quality_summary=quality_summary
            )
            
        except Exception as e:
            logger.error(f"Failed to generate media for product {product_id}: {e}")
            return ProductMediaResult(
                product_id=product_id,
                success=False,
                assets=[],
                processing_time=(datetime.now() - start_time).total_seconds(),
                total_cost=0.0,
                quality_summary={},
                error_message=str(e)
            )
    
    def _get_product_data(self, product_id: str) -> Optional[ProductSchema]:
        """Get product data by ID."""
        # First check example products
        for product in EXAMPLE_PRODUCTS:
            if product.product_id == product_id:
                return product
        
        # In production, this would query the database
        logger.warning(f"Product {product_id} not found in examples")
        return None
    
    async def _generate_contexts(
        self, 
        product_data: ProductSchema, 
        request: MediaGenerationRequest
    ) -> Dict[str, Any]:
        """Generate all necessary contexts for media generation."""
        
        # Create product context
        product_context = ProductContext(
            product_id=product_data.product_id,
            title=product_data.title,
            description=product_data.description,
            category=product_data.category,
            price=product_data.price,
            currency=product_data.currency,
            brand=product_data.brand,
            colors=product_data.colors,
            materials=product_data.materials,
            sizes=product_data.sizes,
            tags=product_data.tags,
            features=getattr(product_data, 'features', []),
            benefits=getattr(product_data, 'benefits', []),
            usage_contexts=getattr(product_data, 'usage_contexts', [])
        )
        
        # Create brand context
        brand_context = BrandContext(
            brand_name=product_data.brand,
            tone=product_data.shop_branding.tone,
            style=product_data.shop_branding.style,
            values=getattr(product_data.shop_branding, 'values', []),
            target_audience=getattr(product_data.shop_branding, 'target_audience', 'general'),
            brand_colors=getattr(product_data.shop_branding, 'colors', []),
            typography=getattr(product_data.shop_branding, 'typography', {}),
            voice_characteristics=getattr(product_data.shop_branding, 'voice_characteristics', [])
        )
        
        # Create market context (simplified for now)
        market_context = MarketContext(
            target_market="global",
            season="current",
            trends=[],
            competitors=[],
            price_positioning="mid-range"
        )
        
        return {
            "product": product_context,
            "brand": brand_context,
            "market": market_context
        }
    
    async def _generate_media_type(
        self,
        product_data: ProductSchema,
        media_type: str,
        contexts: Dict[str, Any],
        request: MediaGenerationRequest
    ) -> tuple[List[GeneratedMediaAsset], float]:
        """Generate media for a specific type."""
        
        assets = []
        total_cost = 0.0
        
        try:
            if media_type == "text":
                pipeline_result = await text_pipeline.generate_product_text(
                    product_data=product_data,
                    custom_config=request.custom_config or {}
                )
            elif media_type == "image":
                pipeline_result = await image_pipeline.generate_product_images(
                    product_data=product_data,
                    custom_config=request.custom_config or {}
                )
            elif media_type == "video":
                pipeline_result = await video_pipeline.generate_product_videos(
                    product_data=product_data,
                    custom_config=request.custom_config or {}
                )
            else:
                logger.warning(f"Unsupported media type: {media_type}")
                return assets, total_cost
            
            if pipeline_result.success:
                # Convert pipeline assets to unified format
                for asset in pipeline_result.assets:
                    unified_asset = GeneratedMediaAsset(
                        asset_id=asset.asset_id,
                        product_id=product_data.product_id,
                        media_type=media_type,
                        content_type=asset.metadata.get("content_type", media_type),
                        url=asset.url,
                        content=asset.content,
                        metadata=asset.metadata,
                        quality_score=asset.metadata.get("qa_score", 0.0),
                        needs_review=asset.metadata.get("needs_review", False),
                        file_size=asset.file_size,
                        dimensions=asset.dimensions,
                        format=asset.format
                    )
                    assets.append(unified_asset)
                
                # Estimate cost (would be tracked by provider manager)
                total_cost += len(assets) * 0.05  # Placeholder cost calculation
            
        except Exception as e:
            logger.error(f"Failed to generate {media_type} for {product_data.product_id}: {e}")
        
        return assets, total_cost
    
    def _calculate_quality_summary(self, assets: List[GeneratedMediaAsset]) -> Dict[str, Any]:
        """Calculate quality summary for assets."""
        if not assets:
            return {}
        
        quality_scores = [asset.quality_score for asset in assets if asset.quality_score > 0]
        
        return {
            "average_quality": sum(quality_scores) / len(quality_scores) if quality_scores else 0.0,
            "assets_needing_review": len([a for a in assets if a.needs_review]),
            "total_assets": len(assets),
            "quality_distribution": {
                "excellent": len([a for a in assets if a.quality_score >= 0.9]),
                "good": len([a for a in assets if 0.8 <= a.quality_score < 0.9]),
                "acceptable": len([a for a in assets if 0.6 <= a.quality_score < 0.8]),
                "needs_improvement": len([a for a in assets if a.quality_score < 0.6])
            }
        }
    
    def _calculate_average_quality(self, product_results: List[ProductMediaResult]) -> float:
        """Calculate average quality across all products."""
        all_scores = []
        for result in product_results:
            for asset in result.assets:
                if asset.quality_score > 0:
                    all_scores.append(asset.quality_score)
        
        return sum(all_scores) / len(all_scores) if all_scores else 0.0
    
    def _calculate_cost_breakdown(self, product_results: List[ProductMediaResult]) -> Dict[str, float]:
        """Calculate cost breakdown by media type."""
        breakdown = {"text": 0.0, "image": 0.0, "video": 0.0}
        
        for result in product_results:
            for asset in result.assets:
                # Estimate cost by media type (placeholder)
                if asset.media_type in breakdown:
                    breakdown[asset.media_type] += 0.05
        
        return breakdown


# Global service instance
unified_media_service = UnifiedMediaService()
