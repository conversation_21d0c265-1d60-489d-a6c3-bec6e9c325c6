"""
Video Generation Pipeline for E-commerce Products
Handles product video creation including promos, 360 spins, and carousel videos.
"""

import asyncio
import logging
import hashlib
import tempfile
import os
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from pathlib import Path

from .schemas import (
    ProductSchema, 
    MediaGenerationRequest, 
    MediaGenerationResult, 
    GeneratedAsset,
    VideoRequirements,
    MediaFormat
)
from .template_manager import template_manager
from .provider_interface import provider_registry
from .providers_config import get_default_provider, get_fallback_chain
from modules.storage.storage_service import media_storage_service

logger = logging.getLogger(__name__)


class VideoGenerationPipeline:
    """
    Video generation pipeline for e-commerce product videos.
    Supports product promos, 360 spins, carousel videos, and motion from stills.
    """
    
    def __init__(self):
        self.supported_formats = [MediaFormat.MP4, MediaFormat.WEBM, MediaFormat.GIF]
        self.video_types = [
            "15_second_hero",
            "30_second_story", 
            "product_360",
            "carousel_showcase",
            "motion_stills"
        ]
    
    async def generate_product_videos(
        self,
        product_data: ProductSchema,
        video_types: Optional[List[str]] = None,
        custom_config: Optional[Dict[str, Any]] = None
    ) -> MediaGenerationResult:
        """
        Generate all video types for a product.
        
        Args:
            product_data: Product information
            video_types: Specific video types to generate
            custom_config: Custom configuration overrides
            
        Returns:
            MediaGenerationResult with generated video assets
        """
        start_time = datetime.now()
        
        try:
            # Default video types based on requirements
            if not video_types:
                video_types = self._get_default_video_types(product_data)
            
            # Generate idempotency key
            idempotency_key = self._generate_idempotency_key(product_data, video_types)
            
            # Generate videos using different approaches
            generated_videos = []
            
            for video_type in video_types:
                try:
                    if video_type in ["15_second_hero", "30_second_story"]:
                        # AI-generated videos using storyboard templates
                        video_result = await self._generate_ai_video(
                            product_data, video_type, custom_config
                        )
                    elif video_type == "product_360":
                        # 360-degree product spin (requires multiple angles)
                        video_result = await self._generate_360_video(
                            product_data, custom_config
                        )
                    elif video_type == "carousel_showcase":
                        # Carousel of product images with transitions
                        video_result = await self._generate_carousel_video(
                            product_data, custom_config
                        )
                    elif video_type == "motion_stills":
                        # Motion effects applied to still images
                        video_result = await self._generate_motion_stills(
                            product_data, custom_config
                        )
                    else:
                        logger.warning(f"Unknown video type: {video_type}")
                        continue
                    
                    if video_result:
                        generated_videos.append(video_result)
                        
                except Exception as e:
                    logger.error(f"Failed to generate {video_type}: {e}")
                    continue
            
            # Process and upload videos
            final_assets = await self._process_and_upload_videos(
                generated_videos, product_data
            )
            
            # Calculate processing time
            processing_time = (datetime.now() - start_time).total_seconds()
            
            return MediaGenerationResult(
                success=True,
                assets=final_assets,
                actual_processing_time=processing_time,
                idempotency_key=idempotency_key
            )
            
        except Exception as e:
            logger.error(f"Video generation pipeline failed: {e}")
            processing_time = (datetime.now() - start_time).total_seconds()
            
            return MediaGenerationResult(
                success=False,
                error_message=str(e),
                actual_processing_time=processing_time
            )
    
    def _get_default_video_types(self, product_data: ProductSchema) -> List[str]:
        """Get default video types based on product and requirements."""
        video_types = ["15_second_hero"]  # Always include hero video
        
        # Add based on video requirements
        if product_data.video_requirements.length_seconds >= 30:
            video_types.append("30_second_story")
        
        # Add motion stills as a fallback option
        video_types.append("motion_stills")
        
        return video_types
    
    async def _generate_ai_video(
        self,
        product_data: ProductSchema,
        video_type: str,
        custom_config: Optional[Dict[str, Any]]
    ) -> Optional[Dict[str, Any]]:
        """Generate video using AI video generation providers."""
        try:
            # Get video template
            template_path = f"video/product_showcase/{video_type}"
            template_result = template_manager.render_template(template_path, product_data)
            
            # Get video provider
            provider = await self._get_video_provider()
            if not provider:
                logger.warning("No video generation provider available")
                return None
            
            # Create generation request
            request = MediaGenerationRequest(
                product_data=product_data,
                media_type="video",
                custom_config={
                    "storyboard": template_result.get("storyboard", []),
                    "audio": template_result.get("audio", {}),
                    "duration": template_result.get("parameters", {}).get("duration", 15),
                    "aspect_ratios": template_result.get("parameters", {}).get("aspect_ratios", ["1:1"]),
                    **(custom_config or {})
                }
            )
            
            # Generate video
            result = await provider.generate_media(request)
            
            if result.success and result.assets:
                return {
                    "type": video_type,
                    "assets": result.assets,
                    "metadata": {
                        "generation_method": "ai_generated",
                        "template_used": template_path,
                        "storyboard": template_result.get("storyboard", [])
                    }
                }
            
        except Exception as e:
            logger.error(f"AI video generation failed for {video_type}: {e}")
        
        return None
    
    async def _generate_360_video(
        self,
        product_data: ProductSchema,
        custom_config: Optional[Dict[str, Any]]
    ) -> Optional[Dict[str, Any]]:
        """Generate 360-degree product spin video."""
        try:
            # This would require multiple product images from different angles
            # For now, create a placeholder implementation
            logger.info("360 video generation requested - would require multiple angle images")
            
            # In a real implementation, this would:
            # 1. Generate or use multiple product images from different angles
            # 2. Create smooth transitions between angles
            # 3. Add rotation effects
            # 4. Export as video
            
            return {
                "type": "product_360",
                "assets": [],  # Would contain actual video assets
                "metadata": {
                    "generation_method": "360_spin",
                    "note": "Requires multiple angle images for full implementation"
                }
            }
            
        except Exception as e:
            logger.error(f"360 video generation failed: {e}")
        
        return None
    
    async def _generate_carousel_video(
        self,
        product_data: ProductSchema,
        custom_config: Optional[Dict[str, Any]]
    ) -> Optional[Dict[str, Any]]:
        """Generate carousel video from product images."""
        try:
            # This would create a video slideshow of product images
            logger.info("Carousel video generation requested")
            
            # In a real implementation, this would:
            # 1. Collect multiple product images (hero, lifestyle, details)
            # 2. Add smooth transitions between images
            # 3. Add text overlays with product info
            # 4. Add background music
            # 5. Export as video
            
            return {
                "type": "carousel_showcase",
                "assets": [],  # Would contain actual video assets
                "metadata": {
                    "generation_method": "image_carousel",
                    "note": "Creates slideshow from product images"
                }
            }
            
        except Exception as e:
            logger.error(f"Carousel video generation failed: {e}")
        
        return None
    
    async def _generate_motion_stills(
        self,
        product_data: ProductSchema,
        custom_config: Optional[Dict[str, Any]]
    ) -> Optional[Dict[str, Any]]:
        """Generate motion effects from still product images."""
        try:
            # This creates subtle motion effects from static images
            logger.info("Motion stills generation requested")
            
            # In a real implementation, this would:
            # 1. Take a hero product image
            # 2. Apply subtle motion effects (parallax, zoom, pan)
            # 3. Add text animations
            # 4. Create short video clips
            # 5. Export in multiple formats
            
            return {
                "type": "motion_stills",
                "assets": [],  # Would contain actual video assets
                "metadata": {
                    "generation_method": "motion_from_stills",
                    "effects": ["parallax", "zoom", "text_animation"],
                    "note": "Creates motion from static product images"
                }
            }
            
        except Exception as e:
            logger.error(f"Motion stills generation failed: {e}")
        
        return None
    
    async def _process_and_upload_videos(
        self,
        generated_videos: List[Dict[str, Any]],
        product_data: ProductSchema
    ) -> List[GeneratedAsset]:
        """Process and upload generated videos to storage."""
        final_assets = []
        
        for video_data in generated_videos:
            try:
                # Process each asset from the video generation
                for asset in video_data.get("assets", []):
                    if asset.url:
                        # Download video
                        video_bytes = await self._download_video(asset.url)
                        
                        # Process video (format conversion, compression, etc.)
                        processed_videos = await self._process_video(
                            video_bytes, video_data, product_data
                        )
                        
                        # Upload processed videos
                        for processed_video in processed_videos:
                            uploaded_asset = await self._upload_video(
                                processed_video, product_data
                            )
                            if uploaded_asset:
                                final_assets.append(uploaded_asset)
                
            except Exception as e:
                logger.error(f"Failed to process video {video_data.get('type')}: {e}")
                continue
        
        return final_assets
    
    async def _download_video(self, url: str) -> bytes:
        """Download video from URL."""
        import httpx
        
        async with httpx.AsyncClient() as client:
            response = await client.get(url)
            response.raise_for_status()
            return response.content
    
    async def _process_video(
        self,
        video_bytes: bytes,
        video_data: Dict[str, Any],
        product_data: ProductSchema
    ) -> List[Dict[str, Any]]:
        """Process video with format conversion and optimization."""
        processed_videos = []
        
        # For now, return the original video
        # In a real implementation, this would:
        # 1. Convert to different formats (MP4, WebM)
        # 2. Create different resolutions
        # 3. Add subtitles if required
        # 4. Optimize for different platforms
        
        processed_videos.append({
            "video_bytes": video_bytes,
            "format": "mp4",
            "type": video_data["type"],
            "metadata": video_data.get("metadata", {})
        })
        
        return processed_videos
    
    async def _upload_video(
        self,
        processed_video: Dict[str, Any],
        product_data: ProductSchema
    ) -> Optional[GeneratedAsset]:
        """Upload processed video to storage."""
        try:
            # Generate filename
            filename = f"{product_data.product_id}_{processed_video['type']}.{processed_video['format']}"
            
            # Upload to storage
            media_file = await media_storage_service.upload_media(
                tenant_id=1,  # Would get from context
                media_content=processed_video["video_bytes"],
                filename=filename,
                content_type=f"video/{processed_video['format']}",
                metadata=processed_video["metadata"]
            )
            
            # Create GeneratedAsset
            asset = GeneratedAsset(
                asset_id=f"video_{processed_video['type']}_{product_data.product_id}",
                asset_type="video",
                url=media_file.public_url,
                metadata={
                    **processed_video["metadata"],
                    "file_size": len(processed_video["video_bytes"]),
                    "format": processed_video["format"],
                    "storage_path": media_file.storage_path
                },
                file_size=len(processed_video["video_bytes"]),
                format=processed_video["format"]
            )
            
            return asset
            
        except Exception as e:
            logger.error(f"Failed to upload video: {e}")
            return None
    
    async def _get_video_provider(self):
        """Get video generation provider."""
        provider_name = get_default_provider("video")
        provider = provider_registry.get_provider(provider_name)
        
        if not provider:
            # Try fallback chain
            fallback_chain = get_fallback_chain("video")
            for fallback_name in fallback_chain:
                provider = provider_registry.get_provider(fallback_name)
                if provider:
                    break
        
        return provider
    
    def _generate_idempotency_key(self, product_data: ProductSchema, video_types: List[str]) -> str:
        """Generate idempotency key for video generation."""
        key_data = f"{product_data.product_id}_{product_data.product_version}_{'_'.join(sorted(video_types))}"
        return hashlib.sha256(key_data.encode()).hexdigest()[:16]


# Global pipeline instance
video_pipeline = VideoGenerationPipeline()
