"""
Text Generation Pipeline for E-commerce Content
Orchestrates text generation for SEO, marketing, and product content.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
import hashlib

from .schemas import (
    ProductSchema,
    MediaGenerationRequest,
    MediaGenerationResult,
    TextGenerationOutput,
    ValidationResult,
    QualityCheck
)
from .template_manager import template_manager
from .provider_interface import provider_registry
from .providers_config import get_default_provider, get_fallback_chain
from .qa_pipeline import qa_pipeline

logger = logging.getLogger(__name__)


class TextGenerationPipeline:
    """
    Text generation pipeline that orchestrates the creation of all text content
    for e-commerce products including SEO content, marketing copy, and product descriptions.
    """
    
    def __init__(self):
        self.content_types = [
            "seo_title",
            "seo_description", 
            "alt_text",
            "short_caption",
            "long_description",
            "bullet_points",
            "ad_copy_30",
            "ad_copy_60",
            "og_title",
            "og_description"
        ]
    
    async def generate_product_text(
        self, 
        product_data: ProductSchema,
        content_types: Optional[List[str]] = None,
        custom_config: Optional[Dict[str, Any]] = None
    ) -> MediaGenerationResult:
        """
        Generate all text content for a product.
        
        Args:
            product_data: Product information
            content_types: Specific content types to generate (default: all)
            custom_config: Custom configuration overrides
            
        Returns:
            MediaGenerationResult with generated text assets
        """
        start_time = datetime.now()
        
        try:
            # Use specified content types or default to all
            target_content_types = content_types or self.content_types
            
            # Generate idempotency key
            idempotency_key = self._generate_idempotency_key(product_data, target_content_types)
            
            # Create generation request
            request = MediaGenerationRequest(
                product_data=product_data,
                media_type="text",
                custom_config=custom_config or {},
                idempotency_key=idempotency_key
            )
            
            # Get text provider
            provider = await self._get_text_provider()
            if not provider:
                raise RuntimeError("No text generation provider available")
            
            # Generate content using multi-step approach
            text_outputs = await self._generate_multi_step_content(request, target_content_types)
            
            # Validate and post-process content
            validation_result = await self._comprehensive_validation(text_outputs, product_data)

            # Create structured output
            structured_output = TextGenerationOutput(**text_outputs)

            # Create assets with QA assessment
            assets = []
            overall_needs_review = validation_result.needs_manual_review

            for content_type, content in text_outputs.items():
                if content:
                    # Create asset
                    asset = GeneratedAsset(
                        asset_id=f"text_{content_type}_{product_data.product_id}",
                        asset_type="text",
                        content=content,
                        metadata={
                            "content_type": content_type,
                            "word_count": len(content.split()),
                            "character_count": len(content)
                        }
                    )

                    # Perform QA assessment
                    qa_assessment = await qa_pipeline.assess_content_quality(
                        asset, product_data
                    )

                    # Update asset with QA results
                    asset.metadata.update({
                        "qa_score": qa_assessment.overall_score,
                        "qa_level": qa_assessment.quality_level.value,
                        "qa_issues": qa_assessment.issues,
                        "qa_recommendations": qa_assessment.recommendations,
                        "auto_approved": qa_assessment.auto_approve,
                        "review_priority": qa_assessment.review_priority.value
                    })

                    # Update review flag
                    if qa_assessment.needs_human_review:
                        overall_needs_review = True

                    assets.append(asset)

            # Calculate processing time
            processing_time = (datetime.now() - start_time).total_seconds()

            # Create result
            result = MediaGenerationResult(
                success=True,
                assets=assets,
                actual_processing_time=processing_time,
                validation_results=validation_result.dict(),
                needs_manual_review=overall_needs_review,
                idempotency_key=idempotency_key
            )

            # Add text content to result metadata
            result.validation_results["text_outputs"] = text_outputs
            result.validation_results["structured_output"] = structured_output.dict()
            
            return result
            
        except Exception as e:
            logger.error(f"Text generation pipeline failed: {e}")
            processing_time = (datetime.now() - start_time).total_seconds()
            
            return MediaGenerationResult(
                success=False,
                error_message=str(e),
                actual_processing_time=processing_time
            )
    
    async def _generate_multi_step_content(
        self, 
        request: MediaGenerationRequest, 
        content_types: List[str]
    ) -> Dict[str, str]:
        """
        Generate content using multi-step prompting approach.
        
        Step 1: Extract key features and benefits
        Step 2: Generate core content (descriptions, bullets)
        Step 3: Generate SEO-optimized content
        Step 4: Generate marketing copy
        """
        product_data = request.product_data
        
        # Step 1: Feature extraction
        features = await self._extract_product_features(product_data)
        
        # Step 2: Core content generation
        core_content = await self._generate_core_content(product_data, features, content_types)
        
        # Step 3: SEO content generation
        seo_content = await self._generate_seo_content(product_data, core_content, content_types)
        
        # Step 4: Marketing copy generation
        marketing_content = await self._generate_marketing_content(product_data, core_content, content_types)
        
        # Combine all content
        all_content = {**core_content, **seo_content, **marketing_content}
        
        # Filter to requested content types
        return {k: v for k, v in all_content.items() if k in content_types}
    
    async def _extract_product_features(self, product_data: ProductSchema) -> Dict[str, Any]:
        """Extract key features and benefits from product data."""
        features = {
            "key_materials": product_data.materials[:3] if product_data.materials else [],
            "primary_use_cases": product_data.usage_contexts[:3] if product_data.usage_contexts else [],
            "target_demographic": product_data.shop_branding.target_audience or "customers",
            "brand_tone": product_data.shop_branding.tone.value,
            "unique_selling_points": [],
            "benefits": []
        }
        
        # Extract benefits from description
        if product_data.detailed_description:
            # Simple benefit extraction (could be enhanced with NLP)
            description_lower = product_data.detailed_description.lower()
            benefit_keywords = ["comfortable", "durable", "lightweight", "waterproof", "breathable", "stylish"]
            features["benefits"] = [kw for kw in benefit_keywords if kw in description_lower]
        
        return features
    
    async def _generate_core_content(
        self, 
        product_data: ProductSchema, 
        features: Dict[str, Any],
        content_types: List[str]
    ) -> Dict[str, str]:
        """Generate core product content."""
        content = {}
        
        # Generate long description if requested
        if "long_description" in content_types:
            try:
                template_result = template_manager.render_template(
                    "text/marketing/long_description", 
                    product_data,
                    {"extracted_features": features}
                )
                content["long_description"] = await self._call_ai_for_content(
                    template_result["system_prompt"],
                    template_result["content"],
                    template_result["constraints"]
                )
            except Exception as e:
                logger.warning(f"Failed to generate long_description: {e}")
                content["long_description"] = product_data.detailed_description
        
        # Generate bullet points if requested
        if "bullet_points" in content_types:
            try:
                template_result = template_manager.render_template(
                    "text/marketing/product_bullets",
                    product_data,
                    {"extracted_features": features}
                )
                bullets_text = await self._call_ai_for_content(
                    template_result["system_prompt"],
                    template_result["content"],
                    template_result["constraints"]
                )
                content["bullet_points"] = bullets_text
            except Exception as e:
                logger.warning(f"Failed to generate bullet_points: {e}")
                content["bullet_points"] = ""
        
        return content
    
    async def _generate_seo_content(
        self, 
        product_data: ProductSchema, 
        core_content: Dict[str, str],
        content_types: List[str]
    ) -> Dict[str, str]:
        """Generate SEO-optimized content."""
        content = {}
        
        seo_tasks = [
            ("seo_title", "text/seo/meta_title"),
            ("seo_description", "text/seo/meta_description"),
            ("alt_text", "text/seo/alt_text"),
            ("og_title", "text/seo/meta_title"),  # Reuse meta_title template
            ("og_description", "text/seo/meta_description")  # Reuse meta_description template
        ]
        
        for content_type, template_path in seo_tasks:
            if content_type not in content_types:
                continue
                
            try:
                template_result = template_manager.render_template(
                    template_path,
                    product_data,
                    {"core_content": core_content}
                )
                
                generated_content = await self._call_ai_for_content(
                    template_result["system_prompt"],
                    template_result["content"],
                    template_result["constraints"]
                )
                
                content[content_type] = generated_content
                
            except Exception as e:
                logger.warning(f"Failed to generate {content_type}: {e}")
                content[content_type] = ""
        
        return content
    
    async def _generate_marketing_content(
        self, 
        product_data: ProductSchema, 
        core_content: Dict[str, str],
        content_types: List[str]
    ) -> Dict[str, str]:
        """Generate marketing copy."""
        content = {}
        
        marketing_tasks = [
            ("short_caption", "text/marketing/short_caption"),
            ("ad_copy_30", "text/marketing/ad_copy_30"),
            ("ad_copy_60", "text/marketing/ad_copy_30")  # Use same template, adjust constraints
        ]
        
        for content_type, template_path in marketing_tasks:
            if content_type not in content_types:
                continue
                
            try:
                # Adjust constraints for 60-second ad copy
                custom_context = {"core_content": core_content}
                if content_type == "ad_copy_60":
                    custom_context["duration_override"] = "60_seconds"
                
                template_result = template_manager.render_template(
                    template_path,
                    product_data,
                    custom_context
                )
                
                generated_content = await self._call_ai_for_content(
                    template_result["system_prompt"],
                    template_result["content"],
                    template_result["constraints"]
                )
                
                content[content_type] = generated_content
                
            except Exception as e:
                logger.warning(f"Failed to generate {content_type}: {e}")
                content[content_type] = ""
        
        return content
    
    async def _call_ai_for_content(
        self, 
        system_prompt: str, 
        user_prompt: str, 
        constraints: Dict[str, Any]
    ) -> str:
        """Call AI provider for content generation."""
        provider = await self._get_text_provider()
        if not provider:
            raise RuntimeError("No text provider available")
        
        # This would be implemented in the actual provider
        # For now, return a placeholder
        return f"Generated content for: {user_prompt[:50]}..."
    
    async def _get_text_provider(self):
        """Get text generation provider."""
        provider_name = get_default_provider("text")
        provider = provider_registry.get_provider(provider_name)
        
        if not provider:
            # Try fallback chain
            fallback_chain = get_fallback_chain("text")
            for fallback_name in fallback_chain:
                provider = provider_registry.get_provider(fallback_name)
                if provider:
                    break
        
        return provider
    
    async def _comprehensive_validation(
        self, 
        text_outputs: Dict[str, str], 
        product_data: ProductSchema
    ) -> ValidationResult:
        """Perform comprehensive validation of generated content."""
        quality_checks = []
        safety_checks = []
        content_policy_checks = []
        needs_review = False
        review_reasons = []
        
        for content_type, content in text_outputs.items():
            # Quality checks
            quality_check = self._validate_content_quality(content, content_type)
            quality_checks.append(quality_check)
            
            # Safety checks
            safety_check = self._validate_content_safety(content)
            safety_checks.append(safety_check)
            
            # Content policy checks
            policy_check = self._validate_content_policy(content, product_data)
            content_policy_checks.append(policy_check)
            
            # Check if manual review is needed
            if not quality_check.passed or not safety_check.passed or not policy_check.passed:
                needs_review = True
                if not quality_check.passed:
                    review_reasons.append(f"{content_type}: Quality issues")
                if not safety_check.passed:
                    review_reasons.append(f"{content_type}: Safety concerns")
                if not policy_check.passed:
                    review_reasons.append(f"{content_type}: Policy violations")
        
        overall_passed = all(check.passed for check in quality_checks + safety_checks + content_policy_checks)
        overall_score = sum(check.score or 0 for check in quality_checks + safety_checks + content_policy_checks) / len(quality_checks + safety_checks + content_policy_checks) if quality_checks + safety_checks + content_policy_checks else 0
        
        return ValidationResult(
            overall_passed=overall_passed,
            overall_score=overall_score,
            quality_checks=quality_checks,
            safety_checks=safety_checks,
            content_policy_checks=content_policy_checks,
            needs_manual_review=needs_review,
            review_reasons=review_reasons
        )
    
    def _validate_content_quality(self, content: str, content_type: str) -> QualityCheck:
        """Validate content quality."""
        score = 1.0
        issues = []
        
        # Check if content is empty
        if not content or not content.strip():
            return QualityCheck(
                check_name=f"quality_{content_type}",
                passed=False,
                score=0.0,
                message="Empty content",
                details={"issue": "no_content"}
            )
        
        # Check for AI artifacts
        ai_artifacts = ["as an ai", "i cannot", "i don't have", "i'm sorry", "i apologize"]
        content_lower = content.lower()
        for artifact in ai_artifacts:
            if artifact in content_lower:
                score -= 0.3
                issues.append(f"Contains AI artifact: {artifact}")
        
        # Check for placeholder text
        if "{{" in content or "}}" in content:
            score -= 0.5
            issues.append("Contains unresolved placeholders")
        
        # Check readability (basic)
        words = content.split()
        if len(words) < 3:
            score -= 0.2
            issues.append("Content too short")
        
        passed = score >= 0.7
        
        return QualityCheck(
            check_name=f"quality_{content_type}",
            passed=passed,
            score=max(0.0, score),
            message="Quality check passed" if passed else f"Quality issues: {', '.join(issues)}",
            details={"issues": issues, "word_count": len(words)}
        )
    
    def _validate_content_safety(self, content: str) -> QualityCheck:
        """Validate content safety."""
        violations = []
        content_lower = content.lower()
        
        # Check for inappropriate claims
        inappropriate_claims = [
            "guaranteed", "miracle", "instant cure", "doctor recommended",
            "clinically proven", "fda approved", "medical grade"
        ]
        
        for claim in inappropriate_claims:
            if claim in content_lower:
                violations.append(f"Inappropriate claim: {claim}")
        
        passed = len(violations) == 0
        
        return QualityCheck(
            check_name="safety_check",
            passed=passed,
            score=1.0 if passed else 0.5,
            message="Safety check passed" if passed else f"Safety violations: {', '.join(violations)}",
            details={"violations": violations}
        )
    
    def _validate_content_policy(self, content: str, product_data: ProductSchema) -> QualityCheck:
        """Validate content against content policy."""
        violations = []
        
        # Check for price claims when no price data available
        if not hasattr(product_data, 'price') or not product_data.price_tiers:
            price_terms = ["cheap", "expensive", "affordable", "budget", "premium pricing"]
            content_lower = content.lower()
            for term in price_terms:
                if term in content_lower:
                    violations.append(f"Price claim without price data: {term}")
        
        passed = len(violations) == 0
        
        return QualityCheck(
            check_name="content_policy",
            passed=passed,
            score=1.0 if passed else 0.7,
            message="Policy check passed" if passed else f"Policy violations: {', '.join(violations)}",
            details={"violations": violations}
        )
    
    def _generate_idempotency_key(self, product_data: ProductSchema, content_types: List[str]) -> str:
        """Generate idempotency key for text generation."""
        key_data = f"{product_data.product_id}_{product_data.product_version}_{'_'.join(sorted(content_types))}"
        return hashlib.sha256(key_data.encode()).hexdigest()[:16]


# Global pipeline instance
text_pipeline = TextGenerationPipeline()
