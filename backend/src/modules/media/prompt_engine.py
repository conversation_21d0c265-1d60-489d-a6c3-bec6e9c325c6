"""
Professional Prompt Engineering System for E-commerce Media Generation.
Creates sophisticated prompts for images, videos, and copy that rival professional agencies.
"""

import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum

from .context_engine import ProductContext, BrandContext, MarketContext, ContentStyle, TargetAudience

logger = logging.getLogger(__name__)


class MediaType(str, Enum):
    """Types of media content."""
    PRODUCT_PHOTOGRAPHY = "product_photography"
    LIFESTYLE_PHOTOGRAPHY = "lifestyle_photography"
    PRODUCT_VIDEO = "product_video"
    LIFESTYLE_VIDEO = "lifestyle_video"
    SOCIAL_VIDEO = "social_video"
    MARKETING_COPY = "marketing_copy"
    SOCIAL_CAPTION = "social_caption"
    PRODUCT_DESCRIPTION = "product_description"


class Platform(str, Enum):
    """Target platforms for content."""
    INSTAGRAM = "instagram"
    TIKTOK = "tiktok"
    FACEBOOK = "facebook"
    PINTEREST = "pinterest"
    YOUTUBE = "youtube"
    WEBSITE = "website"
    EMAIL = "email"
    PRINT = "print"


@dataclass
class PromptContext:
    """Context for prompt generation."""
    media_type: MediaType
    platform: Optional[Platform] = None
    aspect_ratio: str = "1:1"
    duration_seconds: Optional[int] = None
    style_preference: Optional[ContentStyle] = None
    campaign_theme: Optional[str] = None
    call_to_action: Optional[str] = None
    brand_mentions: bool = True
    price_display: bool = False


@dataclass
class GeneratedPrompt:
    """Generated prompt with metadata."""
    main_prompt: str
    negative_prompt: Optional[str] = None
    style_modifiers: List[str] = None
    technical_specs: Dict[str, Any] = None
    estimated_quality_score: float = 0.0
    target_keywords: List[str] = None
    
    def __post_init__(self):
        if self.style_modifiers is None:
            self.style_modifiers = []
        if self.technical_specs is None:
            self.technical_specs = {}
        if self.target_keywords is None:
            self.target_keywords = []


class ProfessionalPromptEngine:
    """
    Professional prompt engineering system for e-commerce media generation.
    Creates agency-quality prompts for different media types and platforms.
    """
    
    def __init__(self):
        self.photography_styles = self._load_photography_styles()
        self.video_templates = self._load_video_templates()
        self.copy_frameworks = self._load_copy_frameworks()
        self.platform_specs = self._load_platform_specifications()
    
    async def generate_prompt(
        self,
        product_context: ProductContext,
        prompt_context: PromptContext,
        brand_context: Optional[BrandContext] = None,
        market_context: Optional[MarketContext] = None
    ) -> GeneratedPrompt:
        """
        Generate professional prompt for media creation.
        
        Args:
            product_context: Product information and analysis
            prompt_context: Media type and platform requirements
            brand_context: Brand guidelines and preferences
            market_context: Market trends and competitive data
            
        Returns:
            Professional prompt optimized for the specific use case
        """
        if prompt_context.media_type in [MediaType.PRODUCT_PHOTOGRAPHY, MediaType.LIFESTYLE_PHOTOGRAPHY]:
            return await self._generate_photography_prompt(
                product_context, prompt_context, brand_context, market_context
            )
        elif prompt_context.media_type in [MediaType.PRODUCT_VIDEO, MediaType.LIFESTYLE_VIDEO, MediaType.SOCIAL_VIDEO]:
            return await self._generate_video_prompt(
                product_context, prompt_context, brand_context, market_context
            )
        elif prompt_context.media_type in [MediaType.MARKETING_COPY, MediaType.SOCIAL_CAPTION, MediaType.PRODUCT_DESCRIPTION]:
            return await self._generate_copy_prompt(
                product_context, prompt_context, brand_context, market_context
            )
        else:
            raise ValueError(f"Unsupported media type: {prompt_context.media_type}")
    
    async def _generate_photography_prompt(
        self,
        product_context: ProductContext,
        prompt_context: PromptContext,
        brand_context: Optional[BrandContext],
        market_context: Optional[MarketContext]
    ) -> GeneratedPrompt:
        """Generate professional photography prompt."""
        
        # Base product description
        product_desc = self._create_product_description(product_context)
        
        # Photography style and setup
        if prompt_context.media_type == MediaType.PRODUCT_PHOTOGRAPHY:
            style_elements = self._get_product_photography_style(
                product_context, brand_context, prompt_context
            )
        else:  # Lifestyle photography
            style_elements = self._get_lifestyle_photography_style(
                product_context, brand_context, prompt_context
            )
        
        # Lighting and technical specs
        lighting = self._get_optimal_lighting(product_context, prompt_context.platform)
        
        # Composition and framing
        composition = self._get_composition_guidelines(
            product_context, prompt_context.aspect_ratio
        )
        
        # Brand-specific elements
        brand_elements = self._get_brand_visual_elements(brand_context) if brand_context else []
        
        # Construct main prompt
        prompt_parts = [
            f"Professional {style_elements['style']} photograph of {product_desc}",
            f"Shot with {style_elements['camera_setup']}",
            f"Lighting: {lighting}",
            f"Composition: {composition}",
            f"Background: {style_elements['background']}",
            f"Mood: {style_elements['mood']}"
        ]
        
        if brand_elements:
            prompt_parts.append(f"Brand elements: {', '.join(brand_elements)}")
        
        # Add quality modifiers
        quality_modifiers = [
            "ultra high resolution", "professional photography", "commercial quality",
            "sharp focus", "perfect lighting", "color accurate"
        ]
        
        main_prompt = ", ".join(prompt_parts + quality_modifiers)
        
        # Negative prompt
        negative_prompt = self._generate_photography_negative_prompt(product_context)
        
        # Technical specifications
        technical_specs = {
            "resolution": "4K",
            "color_space": "sRGB",
            "lighting_setup": lighting,
            "recommended_camera": style_elements.get("camera", "DSLR"),
            "post_processing": style_elements.get("post_processing", "minimal")
        }
        
        return GeneratedPrompt(
            main_prompt=main_prompt,
            negative_prompt=negative_prompt,
            style_modifiers=style_elements.get("modifiers", []),
            technical_specs=technical_specs,
            estimated_quality_score=self._calculate_quality_score(product_context, prompt_context),
            target_keywords=self._extract_target_keywords(product_context)
        )
    
    async def _generate_video_prompt(
        self,
        product_context: ProductContext,
        prompt_context: PromptContext,
        brand_context: Optional[BrandContext],
        market_context: Optional[MarketContext]
    ) -> GeneratedPrompt:
        """Generate professional video prompt."""
        
        # Video concept and narrative
        concept = self._create_video_concept(product_context, prompt_context, brand_context)
        
        # Shot list and sequences
        shot_list = self._generate_shot_list(product_context, prompt_context)
        
        # Audio and music direction
        audio_direction = self._get_audio_direction(product_context, brand_context, prompt_context)
        
        # Visual style and cinematography
        visual_style = self._get_video_visual_style(product_context, brand_context, prompt_context)
        
        # Construct video prompt
        prompt_parts = [
            f"Professional {prompt_context.media_type.replace('_', ' ')} featuring {product_context.title}",
            f"Concept: {concept}",
            f"Visual style: {visual_style}",
            f"Shot sequence: {shot_list}",
            f"Audio: {audio_direction}",
            f"Duration: {prompt_context.duration_seconds or 30} seconds"
        ]
        
        if prompt_context.platform:
            platform_specs = self.platform_specs.get(prompt_context.platform, {})
            if platform_specs:
                prompt_parts.append(f"Platform optimization: {platform_specs.get('video_style', '')}")
        
        main_prompt = ". ".join(prompt_parts)
        
        # Technical specifications for video
        technical_specs = {
            "resolution": "4K",
            "frame_rate": "30fps",
            "aspect_ratio": prompt_context.aspect_ratio,
            "duration": prompt_context.duration_seconds or 30,
            "format": "MP4",
            "codec": "H.264"
        }
        
        return GeneratedPrompt(
            main_prompt=main_prompt,
            technical_specs=technical_specs,
            estimated_quality_score=self._calculate_quality_score(product_context, prompt_context),
            target_keywords=self._extract_target_keywords(product_context)
        )
    
    async def _generate_copy_prompt(
        self,
        product_context: ProductContext,
        prompt_context: PromptContext,
        brand_context: Optional[BrandContext],
        market_context: Optional[MarketContext]
    ) -> GeneratedPrompt:
        """Generate professional copy prompt."""
        
        # Copy framework selection
        framework = self._select_copy_framework(prompt_context.media_type, product_context)
        
        # Tone and voice
        tone = self._determine_copy_tone(brand_context, prompt_context.platform, product_context.target_audience)
        
        # Key messages and benefits
        key_messages = self._extract_key_messages(product_context)
        
        # Call to action
        cta = prompt_context.call_to_action or self._generate_cta(product_context, prompt_context.platform)
        
        # Construct copy prompt
        prompt_parts = [
            f"Write {prompt_context.media_type.replace('_', ' ')} for {product_context.title}",
            f"Framework: {framework}",
            f"Tone: {tone}",
            f"Key benefits: {', '.join(key_messages)}",
            f"Target audience: {', '.join([aud.value for aud in product_context.target_audience])}",
            f"Call to action: {cta}"
        ]
        
        if brand_context:
            prompt_parts.append(f"Brand voice: {brand_context.brand_voice}")
            prompt_parts.append(f"Brand values: {', '.join(brand_context.brand_values)}")
        
        if prompt_context.platform:
            platform_specs = self.platform_specs.get(prompt_context.platform, {})
            char_limit = platform_specs.get("character_limit")
            if char_limit:
                prompt_parts.append(f"Character limit: {char_limit}")
        
        main_prompt = ". ".join(prompt_parts)
        
        return GeneratedPrompt(
            main_prompt=main_prompt,
            estimated_quality_score=self._calculate_quality_score(product_context, prompt_context),
            target_keywords=self._extract_target_keywords(product_context)
        )

    def _create_product_description(self, product_context: ProductContext) -> str:
        """Create detailed product description for prompts."""
        desc_parts = [product_context.title]

        if product_context.materials:
            desc_parts.append(f"made from {', '.join(product_context.materials)}")

        if product_context.colors:
            desc_parts.append(f"in {', '.join(product_context.colors)}")

        if product_context.style_keywords:
            desc_parts.append(f"with {', '.join(product_context.style_keywords[:3])} style")

        return " ".join(desc_parts)

    def _get_product_photography_style(
        self,
        product_context: ProductContext,
        brand_context: Optional[BrandContext],
        prompt_context: PromptContext
    ) -> Dict[str, Any]:
        """Get product photography style specifications."""

        # Base style from category
        category_styles = {
            "fashion_apparel": {
                "style": "fashion product",
                "camera_setup": "85mm lens, f/2.8",
                "background": "seamless white backdrop",
                "mood": "clean and professional",
                "camera": "medium format camera",
                "post_processing": "color correction and retouching"
            },
            "footwear": {
                "style": "product showcase",
                "camera_setup": "50mm lens, f/5.6",
                "background": "neutral gradient backdrop",
                "mood": "dynamic and appealing",
                "camera": "DSLR",
                "post_processing": "shadow enhancement"
            },
            "jewelry": {
                "style": "luxury product",
                "camera_setup": "macro lens, f/8",
                "background": "black velvet or white acrylic",
                "mood": "elegant and sophisticated",
                "camera": "macro photography setup",
                "post_processing": "highlight enhancement"
            }
        }

        base_style = category_styles.get(
            product_context.category.value,
            category_styles["fashion_apparel"]
        )

        # Modify based on brand context
        if brand_context:
            if brand_context.visual_style == ContentStyle.LUXURY:
                base_style["background"] = "premium textured backdrop"
                base_style["mood"] = "luxurious and exclusive"
            elif brand_context.visual_style == ContentStyle.MINIMALIST:
                base_style["background"] = "pure white seamless"
                base_style["mood"] = "clean and minimal"

        # Add style modifiers
        modifiers = ["professional lighting", "sharp details", "color accurate"]
        if product_context.price_tier == "luxury":
            modifiers.extend(["premium quality", "sophisticated composition"])

        base_style["modifiers"] = modifiers
        return base_style

    def _get_lifestyle_photography_style(
        self,
        product_context: ProductContext,
        brand_context: Optional[BrandContext],
        prompt_context: PromptContext
    ) -> Dict[str, Any]:
        """Get lifestyle photography style specifications."""

        # Lifestyle scenarios based on product
        lifestyle_styles = {
            "fashion_apparel": {
                "style": "lifestyle fashion",
                "camera_setup": "35mm lens, f/2.8",
                "background": "urban environment or natural setting",
                "mood": "authentic and aspirational",
                "scenario": "model wearing the item in daily life"
            },
            "footwear": {
                "style": "lifestyle action",
                "camera_setup": "24-70mm lens, f/4",
                "background": "relevant activity setting",
                "mood": "active and energetic",
                "scenario": "person using the product in context"
            }
        }

        base_style = lifestyle_styles.get(
            product_context.category.value,
            lifestyle_styles["fashion_apparel"]
        )

        # Adjust for target audience
        if TargetAudience.GEN_Z in product_context.target_audience:
            base_style["mood"] = "trendy and social media ready"
            base_style["background"] = "Instagram-worthy location"

        return base_style

    def _get_optimal_lighting(self, product_context: ProductContext, platform: Optional[Platform]) -> str:
        """Determine optimal lighting setup."""

        if product_context.category.value == "jewelry":
            return "soft box lighting with reflectors to minimize shadows and enhance sparkle"
        elif product_context.category.value == "fashion_apparel":
            return "even diffused lighting with key light and fill light"
        elif platform == Platform.INSTAGRAM:
            return "bright, even lighting optimized for mobile viewing"
        else:
            return "professional studio lighting with soft shadows"

    def _get_composition_guidelines(self, product_context: ProductContext, aspect_ratio: str) -> str:
        """Get composition guidelines based on product and format."""

        compositions = {
            "1:1": "centered composition with balanced negative space",
            "16:9": "rule of thirds with product positioned for visual impact",
            "9:16": "vertical composition optimized for mobile viewing",
            "4:5": "portrait orientation with product as focal point"
        }

        base_composition = compositions.get(aspect_ratio, compositions["1:1"])

        # Add product-specific composition notes
        if product_context.category.value == "footwear":
            base_composition += ", angled to show profile and sole details"
        elif product_context.category.value == "jewelry":
            base_composition += ", macro detail showing craftsmanship"

        return base_composition

    def _get_brand_visual_elements(self, brand_context: BrandContext) -> List[str]:
        """Extract brand visual elements for inclusion."""
        elements = []

        if brand_context.color_palette:
            elements.append(f"brand colors: {', '.join(brand_context.color_palette[:3])}")

        if brand_context.visual_style:
            elements.append(f"{brand_context.visual_style.value} aesthetic")

        return elements

    def _generate_photography_negative_prompt(self, product_context: ProductContext) -> str:
        """Generate negative prompt for photography."""
        negative_elements = [
            "blurry", "low quality", "distorted", "oversaturated",
            "poor lighting", "cluttered background", "amateur",
            "pixelated", "noise", "artifacts"
        ]

        # Add product-specific negatives
        if product_context.category.value == "jewelry":
            negative_elements.extend(["scratches", "tarnish", "dust"])
        elif product_context.category.value == "fashion_apparel":
            negative_elements.extend(["wrinkles", "stains", "poor fit"])

        return ", ".join(negative_elements)

    def _create_video_concept(
        self,
        product_context: ProductContext,
        prompt_context: PromptContext,
        brand_context: Optional[BrandContext]
    ) -> str:
        """Create video concept based on product and context."""

        concepts = {
            MediaType.PRODUCT_VIDEO: f"Product showcase highlighting key features and benefits of {product_context.title}",
            MediaType.LIFESTYLE_VIDEO: f"Lifestyle integration showing {product_context.title} in real-world use",
            MediaType.SOCIAL_VIDEO: f"Engaging social content featuring {product_context.title} with trending elements"
        }

        base_concept = concepts.get(prompt_context.media_type, concepts[MediaType.PRODUCT_VIDEO])

        # Add brand narrative if available
        if brand_context and brand_context.brand_values:
            base_concept += f" emphasizing {brand_context.brand_values[0]}"

        return base_concept

    def _generate_shot_list(self, product_context: ProductContext, prompt_context: PromptContext) -> str:
        """Generate shot list for video."""

        if prompt_context.media_type == MediaType.PRODUCT_VIDEO:
            shots = [
                "hero shot of product",
                "detail shots of key features",
                "360-degree rotation",
                "usage demonstration",
                "final branding shot"
            ]
        elif prompt_context.media_type == MediaType.LIFESTYLE_VIDEO:
            shots = [
                "lifestyle context establishment",
                "product introduction in scene",
                "usage in natural environment",
                "benefit demonstration",
                "call to action"
            ]
        else:  # Social video
            shots = [
                "attention-grabbing opener",
                "quick product reveal",
                "key benefit highlight",
                "social proof or testimonial",
                "strong call to action"
            ]

        return ", ".join(shots)

    def _get_audio_direction(
        self,
        product_context: ProductContext,
        brand_context: Optional[BrandContext],
        prompt_context: PromptContext
    ) -> str:
        """Get audio direction for video."""

        if prompt_context.platform == Platform.TIKTOK:
            return "trending background music with upbeat tempo"
        elif brand_context and brand_context.brand_voice == "luxury":
            return "sophisticated instrumental music with premium feel"
        elif product_context.category.value == "sports_fitness":
            return "energetic music matching active lifestyle"
        else:
            return "professional background music matching brand tone"

    def _get_video_visual_style(
        self,
        product_context: ProductContext,
        brand_context: Optional[BrandContext],
        prompt_context: PromptContext
    ) -> str:
        """Get video visual style direction."""

        if brand_context:
            if brand_context.visual_style == ContentStyle.MINIMALIST:
                return "clean, minimal aesthetic with smooth transitions"
            elif brand_context.visual_style == ContentStyle.LUXURY:
                return "premium cinematography with elegant movements"

        if prompt_context.platform == Platform.TIKTOK:
            return "dynamic, fast-paced editing with trending visual effects"
        elif prompt_context.platform == Platform.INSTAGRAM:
            return "Instagram-optimized visuals with engaging transitions"

        return "professional cinematography with smooth camera movements"

    def _select_copy_framework(self, media_type: MediaType, product_context: ProductContext) -> str:
        """Select appropriate copywriting framework."""

        frameworks = {
            MediaType.MARKETING_COPY: "AIDA (Attention, Interest, Desire, Action)",
            MediaType.SOCIAL_CAPTION: "Hook, Value, Call-to-Action",
            MediaType.PRODUCT_DESCRIPTION: "Features, Benefits, Social Proof"
        }

        return frameworks.get(media_type, "Problem-Solution-Benefit")

    def _determine_copy_tone(
        self,
        brand_context: Optional[BrandContext],
        platform: Optional[Platform],
        target_audience: List[TargetAudience]
    ) -> str:
        """Determine appropriate tone for copy."""

        if brand_context:
            return brand_context.brand_voice

        if platform == Platform.TIKTOK and TargetAudience.GEN_Z in target_audience:
            return "casual, trendy, and authentic"
        elif TargetAudience.LUXURY_BUYERS in target_audience:
            return "sophisticated and exclusive"
        elif TargetAudience.PROFESSIONALS in target_audience:
            return "professional and authoritative"

        return "friendly and approachable"

    def _extract_key_messages(self, product_context: ProductContext) -> List[str]:
        """Extract key messages from product context."""
        messages = []

        if product_context.key_features:
            messages.extend(product_context.key_features[:3])

        if product_context.use_cases:
            messages.append(f"Perfect for {product_context.use_cases[0]}")

        if product_context.price_tier == "budget":
            messages.append("Great value")
        elif product_context.price_tier == "luxury":
            messages.append("Premium quality")

        return messages[:5]  # Limit to top 5 messages

    def _generate_cta(self, product_context: ProductContext, platform: Optional[Platform]) -> str:
        """Generate appropriate call to action."""

        if platform == Platform.INSTAGRAM:
            return "Shop now - link in bio"
        elif platform == Platform.TIKTOK:
            return "Get yours today!"
        elif product_context.price_tier == "luxury":
            return "Experience luxury - shop now"
        else:
            return "Shop now and save"

    def _calculate_quality_score(self, product_context: ProductContext, prompt_context: PromptContext) -> float:
        """Calculate estimated quality score for the prompt."""
        score = 0.5  # Base score

        # Add points for rich product context
        if product_context.key_features:
            score += 0.1
        if product_context.style_keywords:
            score += 0.1
        if product_context.materials:
            score += 0.1

        # Add points for specific targeting
        if product_context.target_audience:
            score += 0.1

        # Add points for platform optimization
        if prompt_context.platform:
            score += 0.1

        return min(score, 1.0)

    def _extract_target_keywords(self, product_context: ProductContext) -> List[str]:
        """Extract target keywords for SEO and optimization."""
        keywords = [product_context.title.lower()]

        if product_context.category:
            keywords.append(product_context.category.value.replace("_", " "))

        if product_context.style_keywords:
            keywords.extend(product_context.style_keywords)

        if product_context.materials:
            keywords.extend(product_context.materials)

        return list(set(keywords))  # Remove duplicates

    def _load_photography_styles(self) -> Dict[str, Any]:
        """Load photography style configurations."""
        return {
            "product": {"lighting": "studio", "background": "neutral"},
            "lifestyle": {"lighting": "natural", "background": "environmental"}
        }

    def _load_video_templates(self) -> Dict[str, Any]:
        """Load video template configurations."""
        return {
            "product_showcase": {"duration": 30, "shots": 5},
            "lifestyle": {"duration": 45, "shots": 7}
        }

    def _load_copy_frameworks(self) -> Dict[str, Any]:
        """Load copywriting framework templates."""
        return {
            "AIDA": {"structure": ["attention", "interest", "desire", "action"]},
            "PAS": {"structure": ["problem", "agitation", "solution"]}
        }

    def _load_platform_specifications(self) -> Dict[Platform, Dict[str, Any]]:
        """Load platform-specific specifications."""
        return {
            Platform.INSTAGRAM: {
                "character_limit": 2200,
                "video_style": "square or vertical",
                "hashtag_limit": 30
            },
            Platform.TIKTOK: {
                "character_limit": 300,
                "video_style": "vertical, fast-paced",
                "hashtag_limit": 100
            },
            Platform.FACEBOOK: {
                "character_limit": 63206,
                "video_style": "horizontal or square",
                "hashtag_limit": 30
            }
        }


# Create service instance
prompt_engine = ProfessionalPromptEngine()
