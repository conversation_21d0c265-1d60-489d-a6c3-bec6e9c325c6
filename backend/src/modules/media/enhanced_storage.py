"""
Enhanced Media Storage Integration for Generated Content
Handles metadata, versioning, EXIF data, and CDN distribution for AI-generated media.
"""

import logging
import hashlib
import json
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from pathlib import Path
from PIL import Image
from PIL.ExifTags import TAGS
import io

from .schemas import GeneratedAsset, ProductSchema, MediaFormat
from modules.storage.storage_service import MediaStorageService, MediaFile

logger = logging.getLogger(__name__)


class EnhancedMediaStorage:
    """
    Enhanced storage service for AI-generated media with metadata management.
    """
    
    def __init__(self, base_storage_service: MediaStorageService):
        self.storage_service = base_storage_service
        self.metadata_version = "1.0"
    
    async def store_generated_asset(
        self,
        asset: GeneratedAsset,
        product_data: ProductSchema,
        tenant_id: int,
        version: Optional[int] = None
    ) -> MediaFile:
        """
        Store generated asset with comprehensive metadata.
        
        Args:
            asset: Generated asset to store
            product_data: Product context
            tenant_id: Tenant ID for isolation
            version: Asset version number
            
        Returns:
            MediaFile with enhanced metadata
        """
        try:
            # Prepare enhanced metadata
            enhanced_metadata = await self._prepare_enhanced_metadata(
                asset, product_data, version
            )
            
            # Handle different asset types
            if asset.asset_type == "text":
                return await self._store_text_asset(
                    asset, enhanced_metadata, tenant_id
                )
            elif asset.asset_type == "image":
                return await self._store_image_asset(
                    asset, enhanced_metadata, tenant_id
                )
            elif asset.asset_type == "video":
                return await self._store_video_asset(
                    asset, enhanced_metadata, tenant_id
                )
            else:
                raise ValueError(f"Unsupported asset type: {asset.asset_type}")
                
        except Exception as e:
            logger.error(f"Failed to store asset {asset.asset_id}: {e}")
            raise
    
    async def _prepare_enhanced_metadata(
        self,
        asset: GeneratedAsset,
        product_data: ProductSchema,
        version: Optional[int]
    ) -> Dict[str, Any]:
        """Prepare comprehensive metadata for storage."""
        
        # Base metadata
        metadata = {
            "asset_id": asset.asset_id,
            "asset_type": asset.asset_type,
            "generation_timestamp": datetime.now().isoformat(),
            "metadata_version": self.metadata_version,
            "version": version or 1,
            
            # Product context
            "product": {
                "product_id": product_data.product_id,
                "title": product_data.title,
                "category": product_data.category,
                "brand": product_data.brand,
                "colors": product_data.colors,
                "materials": product_data.materials
            },
            
            # Generation metadata
            "generation": asset.metadata or {},
            
            # SEO metadata
            "seo": {
                "alt_text": self._generate_alt_text(asset, product_data),
                "title": self._generate_seo_title(asset, product_data),
                "description": self._generate_seo_description(asset, product_data),
                "keywords": self._generate_keywords(product_data)
            },
            
            # Technical metadata
            "technical": {
                "file_size": asset.file_size,
                "format": asset.format,
                "dimensions": asset.dimensions,
                "created_by": "ai_generation_pipeline"
            }
        }
        
        # Add QA metadata if available
        if "qa_score" in asset.metadata:
            metadata["quality_assurance"] = {
                "qa_score": asset.metadata.get("qa_score"),
                "qa_level": asset.metadata.get("qa_level"),
                "auto_approved": asset.metadata.get("auto_approved", False),
                "review_priority": asset.metadata.get("review_priority", "none"),
                "qa_issues": asset.metadata.get("qa_issues", []),
                "qa_recommendations": asset.metadata.get("qa_recommendations", [])
            }
        
        return metadata
    
    async def _store_text_asset(
        self,
        asset: GeneratedAsset,
        metadata: Dict[str, Any],
        tenant_id: int
    ) -> MediaFile:
        """Store text asset as JSON file."""
        
        # Create text content structure
        text_data = {
            "content": asset.content,
            "metadata": metadata,
            "content_type": metadata["generation"].get("content_type", "general"),
            "word_count": len(asset.content.split()) if asset.content else 0,
            "character_count": len(asset.content) if asset.content else 0
        }
        
        # Convert to JSON bytes
        content_bytes = json.dumps(text_data, indent=2, ensure_ascii=False).encode('utf-8')
        
        # Generate filename
        filename = f"{asset.asset_id}.json"
        
        # Store with enhanced metadata
        return await self.storage_service.upload_media(
            tenant_id=tenant_id,
            media_content=content_bytes,
            filename=filename,
            content_type="application/json",
            metadata=metadata
        )
    
    async def _store_image_asset(
        self,
        asset: GeneratedAsset,
        metadata: Dict[str, Any],
        tenant_id: int
    ) -> MediaFile:
        """Store image asset with EXIF metadata."""
        
        if not asset.url:
            raise ValueError("Image asset must have URL for download")
        
        # Download image content
        image_bytes = await self._download_asset_content(asset.url)
        
        # Add EXIF metadata to image
        enhanced_image_bytes = await self._add_exif_metadata(image_bytes, metadata)
        
        # Generate filename
        file_extension = asset.format or "webp"
        filename = f"{asset.asset_id}.{file_extension}"
        
        # Determine content type
        content_type_map = {
            "webp": "image/webp",
            "jpg": "image/jpeg",
            "jpeg": "image/jpeg",
            "png": "image/png",
            "avif": "image/avif"
        }
        content_type = content_type_map.get(file_extension.lower(), "image/webp")
        
        # Store with enhanced metadata
        return await self.storage_service.upload_media(
            tenant_id=tenant_id,
            media_content=enhanced_image_bytes,
            filename=filename,
            content_type=content_type,
            metadata=metadata
        )
    
    async def _store_video_asset(
        self,
        asset: GeneratedAsset,
        metadata: Dict[str, Any],
        tenant_id: int
    ) -> MediaFile:
        """Store video asset with metadata."""
        
        if not asset.url:
            raise ValueError("Video asset must have URL for download")
        
        # Download video content
        video_bytes = await self._download_asset_content(asset.url)
        
        # Generate filename
        file_extension = asset.format or "mp4"
        filename = f"{asset.asset_id}.{file_extension}"
        
        # Determine content type
        content_type_map = {
            "mp4": "video/mp4",
            "webm": "video/webm",
            "mov": "video/quicktime",
            "avi": "video/x-msvideo"
        }
        content_type = content_type_map.get(file_extension.lower(), "video/mp4")
        
        # Store with enhanced metadata
        return await self.storage_service.upload_media(
            tenant_id=tenant_id,
            media_content=video_bytes,
            filename=filename,
            content_type=content_type,
            metadata=metadata
        )
    
    async def _download_asset_content(self, url: str) -> bytes:
        """Download asset content from URL."""
        import httpx
        
        async with httpx.AsyncClient() as client:
            response = await client.get(url)
            response.raise_for_status()
            return response.content
    
    async def _add_exif_metadata(self, image_bytes: bytes, metadata: Dict[str, Any]) -> bytes:
        """Add EXIF metadata to image."""
        try:
            # Load image
            image = Image.open(io.BytesIO(image_bytes))
            
            # Prepare EXIF data
            exif_data = {
                "ImageDescription": metadata["seo"]["description"][:255],  # Limit length
                "Artist": "AI Generation Pipeline",
                "Software": "ProductMedia AI",
                "DateTime": datetime.now().strftime("%Y:%m:%d %H:%M:%S"),
                "UserComment": json.dumps({
                    "product_id": metadata["product"]["product_id"],
                    "asset_id": metadata["asset_id"],
                    "qa_score": metadata.get("quality_assurance", {}).get("qa_score")
                })[:255]  # Limit length
            }
            
            # Note: Full EXIF implementation would require piexif library
            # For now, we'll preserve the original image and add metadata to storage metadata
            
            # Save image to bytes
            output_buffer = io.BytesIO()
            image.save(output_buffer, format=image.format or 'WEBP', quality=90, optimize=True)
            
            return output_buffer.getvalue()
            
        except Exception as e:
            logger.warning(f"Failed to add EXIF metadata: {e}")
            return image_bytes  # Return original if EXIF fails
    
    def _generate_alt_text(self, asset: GeneratedAsset, product_data: ProductSchema) -> str:
        """Generate SEO-optimized alt text."""
        if asset.asset_type == "text":
            return f"Generated text content for {product_data.title}"
        elif asset.asset_type == "image":
            style = asset.metadata.get("style", "product")
            return f"{style.replace('_', ' ').title()} image of {product_data.title} by {product_data.brand}"
        elif asset.asset_type == "video":
            return f"Product video showcasing {product_data.title}"
        else:
            return f"Generated {asset.asset_type} for {product_data.title}"
    
    def _generate_seo_title(self, asset: GeneratedAsset, product_data: ProductSchema) -> str:
        """Generate SEO title."""
        return f"{product_data.title} - {product_data.brand} | {asset.asset_type.title()}"
    
    def _generate_seo_description(self, asset: GeneratedAsset, product_data: ProductSchema) -> str:
        """Generate SEO description."""
        return f"AI-generated {asset.asset_type} for {product_data.title} by {product_data.brand}. {product_data.description[:100]}..."
    
    def _generate_keywords(self, product_data: ProductSchema) -> List[str]:
        """Generate SEO keywords."""
        keywords = [
            product_data.brand.lower(),
            product_data.category.lower(),
            product_data.title.lower()
        ]
        
        # Add colors and materials
        keywords.extend([color.lower() for color in product_data.colors])
        keywords.extend([material.lower() for material in product_data.materials])
        
        # Add tags
        keywords.extend([tag.lower() for tag in product_data.tags])
        
        return list(set(keywords))  # Remove duplicates
    
    async def create_asset_version(
        self,
        original_asset: GeneratedAsset,
        product_data: ProductSchema,
        tenant_id: int,
        changes: Dict[str, Any]
    ) -> MediaFile:
        """Create a new version of an existing asset."""
        
        # Get current version from metadata
        current_version = 1
        if original_asset.metadata and "version" in original_asset.metadata:
            current_version = original_asset.metadata["version"]
        
        # Create new asset with incremented version
        new_asset = GeneratedAsset(
            asset_id=f"{original_asset.asset_id}_v{current_version + 1}",
            asset_type=original_asset.asset_type,
            url=original_asset.url,
            content=original_asset.content,
            metadata={
                **original_asset.metadata,
                "version": current_version + 1,
                "parent_asset_id": original_asset.asset_id,
                "changes": changes,
                "version_created_at": datetime.now().isoformat()
            },
            file_size=original_asset.file_size,
            dimensions=original_asset.dimensions,
            format=original_asset.format
        )
        
        return await self.store_generated_asset(
            new_asset, product_data, tenant_id, current_version + 1
        )
    
    async def get_asset_versions(
        self,
        asset_id: str,
        tenant_id: int
    ) -> List[MediaFile]:
        """Get all versions of an asset."""
        # This would query the storage service for all versions
        # Implementation depends on how versioning is stored
        logger.info(f"Getting versions for asset {asset_id} - would query storage service")
        return []  # Placeholder
    
    async def cleanup_old_versions(
        self,
        asset_id: str,
        tenant_id: int,
        keep_versions: int = 5
    ) -> Dict[str, Any]:
        """Clean up old versions of an asset, keeping the most recent ones."""
        logger.info(f"Cleaning up old versions for asset {asset_id} - would implement cleanup logic")
        return {"cleaned_versions": 0, "kept_versions": keep_versions}


# Global enhanced storage instance
def get_enhanced_media_storage() -> EnhancedMediaStorage:
    """Get enhanced media storage instance."""
    from modules.storage.storage_service import media_storage_service
    return EnhancedMediaStorage(media_storage_service)
