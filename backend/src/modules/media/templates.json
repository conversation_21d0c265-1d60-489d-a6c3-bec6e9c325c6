{"text_templates": {"seo": {"meta_title": {"system_prompt": "You are an expert SEO copywriter. Create compelling, keyword-rich meta titles that drive clicks while staying under 60 characters.", "template": "Create an SEO meta title (max 60 chars) for {{title}} by {{brand}}. Target keywords: {{primary_keywords}}. Tone: {{shop_branding.tone}}. Include brand name. No price claims.", "constraints": {"max_length": 60, "required_elements": ["brand", "primary_keyword"], "forbidden_phrases": ["best", "cheapest", "guaranteed"]}}, "meta_description": {"system_prompt": "You are an expert SEO copywriter. Create compelling meta descriptions that improve click-through rates while staying under 160 characters.", "template": "Write an SEO meta description (max 160 chars) for {{title}} emphasizing {{primary_keywords}}, {{materials}}, and target {{target_audience}}. Tone: {{shop_branding.tone}}. No price numbers, no claims not in product data. Include call-to-action.", "constraints": {"max_length": 160, "required_elements": ["primary_keywords", "call_to_action"], "forbidden_phrases": ["doctor recommended", "clinically proven"]}}, "alt_text": {"system_prompt": "You are an accessibility expert. Create descriptive, concise alt text that helps visually impaired users understand product images.", "template": "Create descriptive alt text (max 125 chars) for {{title}} in {{colors[0]}}. Include material {{materials[0]}}, key visual features, and usage context {{usage_contexts[0]}}. Be specific and factual.", "constraints": {"max_length": 125, "required_elements": ["color", "material", "product_type"], "style": "descriptive, factual"}}}, "marketing": {"short_caption": {"system_prompt": "You are a social media marketing expert. Create engaging, concise captions that drive engagement.", "template": "Write a short social media caption (max 100 chars) for {{title}}. Highlight key benefit from {{usage_contexts}}. Tone: {{shop_branding.tone}}. Include relevant emoji. No hashtags.", "constraints": {"max_length": 100, "tone_match": true, "include_emoji": true}}, "ad_copy_30": {"system_prompt": "You are a performance marketing copywriter. Create compelling 30-second ad copy that drives conversions.", "template": "Write 30-second ad copy for {{title}} targeting {{target_audience}}. Emphasize {{usage_contexts[0]}} and {{materials[0]}}. Tone: {{shop_branding.tone}}. Include strong CTA. Mention {{brand}}.", "constraints": {"duration": "30_seconds", "required_elements": ["benefit", "cta", "brand"], "tone_match": true}}, "product_bullets": {"system_prompt": "You are a product copywriter. Create clear, benefit-focused bullet points that help customers make purchase decisions.", "template": "Create 3-5 bullet points for {{title}}. Focus on benefits of {{materials}}, {{usage_contexts}}, and key features from {{detailed_description}}. Each bullet max 80 chars. Start with benefit, not feature.", "constraints": {"count": "3-5", "max_length_per_bullet": 80, "format": "benefit_focused"}}, "long_description": {"system_prompt": "You are an expert product copywriter. Create compelling, detailed product descriptions that convert browsers into buyers.", "template": "Write a comprehensive product description for {{title}} by {{brand}}. Emphasize {{materials}} construction, {{usage_contexts}} applications, and benefits for {{target_audience}}. Tone: {{shop_branding.tone}}. Include key features from existing description but enhance with benefits and emotional appeal. Max 300 words.", "constraints": {"max_length": 300, "tone_match": true, "include_benefits": true, "emotional_appeal": true}}}}, "image_templates": {"apparel": {"hero_studio": {"system_prompt": "Create a professional studio product shot optimized for e-commerce.", "template": "Create a clean studio hero image of {{title}} in {{colors[0]}}. Material: {{materials[0]}}. Style: {{shop_branding.tone}}. Product should be centered, natural shadows, no logos other than provided one, no visible watermark. Output high resolution suitable for e-commerce, white background, show product at 3/4 angle.", "negative_prompt": "blurry, low quality, watermark, text overlay, multiple products, cluttered background, poor lighting", "parameters": {"aspect_ratio": "1:1", "background": "white", "lighting": "studio", "angle": "3/4_view"}}, "lifestyle": {"system_prompt": "Create an aspirational lifestyle image showing the product in use.", "template": "Generate a lifestyle image showing {{title}} used in {{usage_contexts[0]}} by a {{target_audience}}. Convey {{shop_branding.tone}} aesthetic, natural lighting, product prominent, environment tasteful and non-branded. Include subtle depth-of-field.", "negative_prompt": "staged, artificial, branded environment, competitor logos, poor composition", "parameters": {"aspect_ratio": "16:9", "setting": "natural", "focus": "product_in_use"}}, "model_wearing": {"system_prompt": "Create an image of the product being worn by a model in an appropriate setting.", "template": "Show {{title}} in {{colors[0]}} worn by a model representing {{target_audience}} in {{usage_contexts[0]}} setting. {{shop_branding.tone}} aesthetic, professional photography, model should complement not overshadow product.", "negative_prompt": "inappropriate clothing, distracting background, poor fit, unprofessional", "parameters": {"aspect_ratio": "3:4", "model_type": "diverse", "setting": "contextual"}}}, "electronics": {"hero_studio": {"system_prompt": "Create a technical product shot highlighting design and features.", "template": "Create a clean technical hero image of {{title}} in {{colors[0]}}. Highlight {{materials[0]}} construction and key features. Style: {{shop_branding.tone}}. Professional lighting, white background, show product at optimal angle to display features and ports.", "negative_prompt": "cluttered, poor lighting, reflections, dust, scratches", "parameters": {"aspect_ratio": "1:1", "background": "white", "lighting": "technical", "angle": "feature_highlighting"}}, "lifestyle_tech": {"system_prompt": "Show the product in a modern, tech-savvy environment.", "template": "Generate a lifestyle image showing {{title}} in {{usage_contexts[0]}} environment. Modern, clean aesthetic matching {{shop_branding.tone}}. Show product in use by {{target_audience}}, emphasize functionality and design.", "negative_prompt": "outdated technology, cluttered workspace, poor cable management", "parameters": {"aspect_ratio": "16:9", "setting": "modern_workspace", "focus": "functionality"}}}, "home": {"hero_studio": {"system_prompt": "Create an elegant product shot suitable for home decor.", "template": "Create an elegant studio image of {{title}} in {{colors[0]}}. Material: {{materials[0]}}. Style: {{shop_branding.tone}}. Soft, natural lighting, neutral background, show product styling and craftsmanship details.", "negative_prompt": "harsh lighting, busy background, poor styling, cheap appearance", "parameters": {"aspect_ratio": "1:1", "background": "neutral", "lighting": "soft_natural", "angle": "styling_focused"}}, "room_setting": {"system_prompt": "Show the product in a beautifully styled room setting.", "template": "Generate a room setting image showing {{title}} in {{usage_contexts[0]}} space. {{shop_branding.tone}} interior design aesthetic, product should be focal point, complementary decor, natural lighting.", "negative_prompt": "cluttered room, poor styling, distracting elements, bad lighting", "parameters": {"aspect_ratio": "16:9", "setting": "styled_interior", "focus": "product_in_context"}}}}, "video_templates": {"product_showcase": {"15_second_hero": {"system_prompt": "Create a compelling 15-second product showcase video.", "storyboard": [{"scene": 1, "duration": "0-4s", "description": "Hero shot with product name overlay", "template": "Open with clean hero shot of {{title}} in {{colors[0]}}. Smooth zoom or rotation. Product name and {{brand}} overlay. {{shop_branding.tone}} aesthetic."}, {"scene": 2, "duration": "4-10s", "description": "Product in use context", "template": "Show product in {{usage_contexts[0]}} context. Highlight key benefit from {{materials[0]}} or functionality. Smooth transitions."}, {"scene": 3, "duration": "10-15s", "description": "Close-up and CTA", "template": "Close-up on key features or details. End with {{shop_name}} logo and clear CTA. Maintain {{shop_branding.tone}} throughout."}], "audio": {"music": "soft_instrumental", "voiceover": false, "sound_effects": "subtle"}, "parameters": {"aspect_ratios": ["1:1", "9:16", "16:9"], "duration": 15, "style": "professional"}}, "30_second_story": {"system_prompt": "Create a narrative 30-second video that tells the product story.", "storyboard": [{"scene": 1, "duration": "0-6s", "description": "Problem or context setup", "template": "Establish context where {{title}} solves a need in {{usage_contexts[0]}}. Show relatable scenario for {{target_audience}}."}, {"scene": 2, "duration": "6-20s", "description": "Product introduction and benefits", "template": "Introduce {{title}} by {{brand}}. Highlight {{materials[0]}} and key benefits. Show product in action in {{usage_contexts[0]}}."}, {"scene": 3, "duration": "20-30s", "description": "Resolution and CTA", "template": "Show satisfied outcome. Feature {{shop_name}} branding. Strong CTA matching {{shop_branding.tone}}."}], "audio": {"music": "upbeat_instrumental", "voiceover": true, "sound_effects": "contextual"}, "parameters": {"aspect_ratios": ["16:9", "1:1"], "duration": 30, "style": "narrative"}}}}, "template_metadata": {"version": "1.0", "last_updated": "2024-01-01", "categories": ["apparel", "electronics", "home", "beauty", "sports"], "supported_tones": ["luxury", "minimal", "friendly", "technical", "playful", "professional", "vintage", "modern"], "placeholder_syntax": "{{field_name}}", "validation_rules": {"required_placeholders": ["title", "brand", "shop_branding.tone"], "optional_placeholders": ["colors", "materials", "usage_contexts", "target_audience", "primary_keywords"], "constraint_enforcement": true}}}