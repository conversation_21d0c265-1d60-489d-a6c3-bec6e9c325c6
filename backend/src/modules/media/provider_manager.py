"""
Enhanced Provider Manager for Media Generation Providers.
Handles dynamic loading, management, rate limiting, cost tracking, and health monitoring.
"""

import importlib
import logging
import asyncio
from pathlib import Path
from typing import Dict, List, Optional, Any, Type
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum

from .provider_interface import (
    MediaProviderPlugin,
    ProviderConfig,
    provider_registry
)

logger = logging.getLogger(__name__)


class ProviderStatus(str, Enum):
    """Provider status options."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"
    RATE_LIMITED = "rate_limited"
    QUOTA_EXCEEDED = "quota_exceeded"


@dataclass
class RateLimit:
    """Rate limiting configuration."""
    requests_per_minute: int = 60
    requests_per_hour: int = 1000
    requests_per_day: int = 10000
    concurrent_requests: int = 10
    cost_per_day: float = 100.0


@dataclass
class UsageStats:
    """Usage statistics for a provider."""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    concurrent_requests: int = 0
    request_timestamps: List[datetime] = field(default_factory=list)
    last_request_time: Optional[datetime] = None


@dataclass
class CostTracker:
    """Cost tracking for a provider."""
    daily_cost: float = 0.0
    monthly_cost: float = 0.0
    cost_per_request: float = 0.05
    last_reset: datetime = field(default_factory=datetime.now)


@dataclass
class HealthMetrics:
    """Health metrics for a provider."""
    status: ProviderStatus = ProviderStatus.ACTIVE
    last_check: Optional[datetime] = None
    response_time: float = 0.0
    error_rate: float = 0.0
    uptime_percentage: float = 100.0
    total_requests: int = 0
    successful_requests: int = 0


class MediaProviderManager:
    """Enhanced manager for loading and managing media providers with rate limiting and monitoring."""

    def __init__(self, plugins_dir: Optional[str] = None):
        self.plugins_dir = Path(plugins_dir) if plugins_dir else Path(__file__).parent / "providers"
        self.plugins_dir.mkdir(exist_ok=True)
        self._loaded_plugins: Dict[str, Type[MediaProviderPlugin]] = {}

        # Enhanced management features
        self.rate_limits: Dict[str, RateLimit] = {}
        self.usage_stats: Dict[str, UsageStats] = {}
        self.cost_trackers: Dict[str, CostTracker] = {}
        self.health_metrics: Dict[str, HealthMetrics] = {}

        # Initialize default configurations
        self._initialize_default_configs()

    def discover_providers(self) -> List[str]:
        """Discover available media providers."""
        plugins = []

        # Look for provider modules in the providers directory
        if self.plugins_dir.exists():
            for item in self.plugins_dir.iterdir():
                if item.is_file() and item.suffix == '.py' and not item.name.startswith('_'):
                    plugin_name = item.stem
                    plugins.append(plugin_name)

        logger.info(f"Discovered {len(plugins)} providers: {plugins}")
        return plugins

    async def load_provider(self, provider_name: str) -> Optional[Type[MediaProviderPlugin]]:
        """Load a media provider by name."""
        try:
            # Try to import the plugin module
            module_path = f"modules.media.providers.{provider_name}"
            module = importlib.import_module(module_path)

            # Look for the provider class
            provider_class = None
            for attr_name in dir(module):
                attr = getattr(module, attr_name)
                if (isinstance(attr, type) and
                    issubclass(attr, MediaProviderPlugin) and
                    attr != MediaProviderPlugin):
                    provider_class = attr
                    break

            if provider_class:
                self._loaded_plugins[provider_name] = provider_class
                logger.info(f"Loaded provider plugin: {provider_name}")
                return provider_class
            else:
                logger.error(f"No provider class found in plugin: {provider_name}")
                return None

        except ImportError as e:
            logger.error(f"Failed to import plugin {provider_name}: {e}")
            return None
        except Exception as e:
            logger.error(f"Error loading plugin {provider_name}: {e}")
            return None

    async def load_all_providers(self) -> Dict[str, bool]:
        """Load all discovered providers."""
        providers = self.discover_providers()
        results = {}

        for provider_name in providers:
            provider_class = await self.load_provider(provider_name)
            results[provider_name] = provider_class is not None

        return results

    def get_provider_class(self, provider_name: str) -> Optional[Type[MediaProviderPlugin]]:
        """Get a loaded provider class."""
        return self._loaded_plugins.get(provider_name)

    async def create_provider_instance(
        self,
        provider_name: str,
        config: ProviderConfig
    ) -> Optional[MediaProviderPlugin]:
        """Create an instance of a media provider."""
        provider_class = self.get_provider_class(provider_name)
        if not provider_class:
            logger.error(f"Provider not loaded: {provider_name}")
            return None

        try:
            provider_instance = provider_class()
            success = await provider_instance.initialize(config)

            if success:
                provider_registry.register_provider(provider_instance, config)
                logger.info(f"Registered provider instance: {provider_name}")
                return provider_instance
            else:
                logger.error(f"Failed to initialize provider: {provider_name}")
                return None

        except Exception as e:
            logger.error(f"Error creating provider instance {provider_name}: {e}")
            return None

    def get_loaded_providers(self) -> List[str]:
        """Get list of loaded provider names."""
        return list(self._loaded_plugins.keys())

    async def unload_provider(self, provider_name: str) -> bool:
        """Unload a provider."""
        if provider_name in self._loaded_plugins:
            # Remove from registry if registered
            provider = provider_registry.get_provider(provider_name)
            if provider:
                await provider.cleanup()
                provider_registry.unregister_provider(provider_name)

            del self._loaded_plugins[provider_name]
            logger.info(f"Unloaded provider: {provider_name}")
            return True

        return False

    async def reload_provider(self, provider_name: str) -> bool:
        """Reload a provider."""
        # Unload first
        await self.unload_provider(provider_name)

        # Reload
        provider_class = await self.load_provider(provider_name)
        return provider_class is not None


# Global provider manager instance
provider_manager = MediaProviderManager()


async def initialize_providers(configs: Dict[str, ProviderConfig]) -> Dict[str, bool]:
    """
    Initialize media providers from configuration.

    Args:
        configs: Dictionary mapping provider names to their configurations

    Returns:
        Dictionary with initialization results
    """
    results = {}

    # Load all available providers
    load_results = await provider_manager.load_all_providers()

    # Initialize configured providers
    for provider_name, config in configs.items():
        if provider_name in load_results and load_results[provider_name]:
            provider_instance = await provider_manager.create_provider_instance(provider_name, config)
            results[provider_name] = provider_instance is not None
        else:
            logger.warning(f"Provider {provider_name} not available or failed to load")
            results[provider_name] = False

    return results

    def _initialize_default_configs(self):
        """Initialize default configurations for providers."""
        default_providers = ["mock", "banana", "gemini", "veo3"]

        for provider_name in default_providers:
            self.rate_limits[provider_name] = RateLimit()
            self.usage_stats[provider_name] = UsageStats()
            self.cost_trackers[provider_name] = CostTracker()
            self.health_metrics[provider_name] = HealthMetrics()

    async def can_make_request(self, provider_name: str, estimated_cost: float = 0.0) -> tuple[bool, str]:
        """Check if a request can be made to the provider."""
        if provider_name not in self.rate_limits:
            return False, f"Provider {provider_name} not configured"

        rate_limit = self.rate_limits[provider_name]
        usage = self.usage_stats[provider_name]
        cost_tracker = self.cost_trackers[provider_name]
        health = self.health_metrics[provider_name]

        # Check provider health
        if health.status != ProviderStatus.ACTIVE:
            return False, f"Provider {provider_name} is {health.status.value}"

        # Check concurrent requests
        if usage.concurrent_requests >= rate_limit.concurrent_requests:
            return False, f"Concurrent request limit exceeded ({usage.concurrent_requests}/{rate_limit.concurrent_requests})"

        # Check daily cost limit
        if cost_tracker.daily_cost + estimated_cost > rate_limit.cost_per_day:
            return False, f"Daily cost limit would be exceeded"

        # Check rate limits
        now = datetime.now()

        # Clean old timestamps
        hour_ago = now - timedelta(hours=1)
        minute_ago = now - timedelta(minutes=1)
        day_ago = now - timedelta(days=1)

        usage.request_timestamps = [ts for ts in usage.request_timestamps if ts > day_ago]

        # Check limits
        recent_hour = len([ts for ts in usage.request_timestamps if ts > hour_ago])
        recent_minute = len([ts for ts in usage.request_timestamps if ts > minute_ago])
        recent_day = len(usage.request_timestamps)

        if recent_minute >= rate_limit.requests_per_minute:
            return False, f"Per-minute rate limit exceeded ({recent_minute}/{rate_limit.requests_per_minute})"

        if recent_hour >= rate_limit.requests_per_hour:
            return False, f"Per-hour rate limit exceeded ({recent_hour}/{rate_limit.requests_per_hour})"

        if recent_day >= rate_limit.requests_per_day:
            return False, f"Per-day rate limit exceeded ({recent_day}/{rate_limit.requests_per_day})"

        return True, "OK"

    async def record_request_start(self, provider_name: str, estimated_cost: float = 0.0):
        """Record the start of a request."""
        if provider_name not in self.usage_stats:
            return

        usage = self.usage_stats[provider_name]
        usage.concurrent_requests += 1
        usage.request_timestamps.append(datetime.now())
        usage.last_request_time = datetime.now()

    async def record_request_end(
        self,
        provider_name: str,
        success: bool,
        actual_cost: float = 0.0,
        response_time: float = 0.0
    ):
        """Record the end of a request."""
        if provider_name not in self.usage_stats:
            return

        usage = self.usage_stats[provider_name]
        cost_tracker = self.cost_trackers[provider_name]
        health = self.health_metrics[provider_name]

        # Update usage stats
        usage.concurrent_requests = max(0, usage.concurrent_requests - 1)
        usage.total_requests += 1

        if success:
            usage.successful_requests += 1
            health.successful_requests += 1
        else:
            usage.failed_requests += 1

        # Update cost tracking
        cost_tracker.daily_cost += actual_cost

        # Update health metrics
        health.total_requests += 1
        health.response_time = (health.response_time + response_time) / 2  # Moving average
        health.error_rate = (health.total_requests - health.successful_requests) / health.total_requests
        health.last_check = datetime.now()

    async def health_check_all_providers(self):
        """Perform health checks on all providers."""
        for provider_name in self.health_metrics:
            await self._health_check_provider(provider_name)

    async def _health_check_provider(self, provider_name: str):
        """Perform health check on a specific provider."""
        try:
            health = self.health_metrics[provider_name]

            # Simple health check - in production this would ping the provider
            health.last_check = datetime.now()

            # Update status based on error rate
            if health.error_rate > 0.5:  # More than 50% errors
                health.status = ProviderStatus.ERROR
            elif health.error_rate > 0.2:  # More than 20% errors
                health.status = ProviderStatus.INACTIVE
            else:
                health.status = ProviderStatus.ACTIVE

        except Exception as e:
            logger.error(f"Health check failed for {provider_name}: {e}")
            self.health_metrics[provider_name].status = ProviderStatus.ERROR

    def get_all_provider_status(self) -> Dict[str, Dict[str, Any]]:
        """Get status of all providers."""
        status = {}

        for provider_name in self.health_metrics:
            health = self.health_metrics[provider_name]
            usage = self.usage_stats[provider_name]
            cost = self.cost_trackers[provider_name]
            limits = self.rate_limits[provider_name]

            status[provider_name] = {
                "provider_name": provider_name,
                "status": health.status.value,
                "health": {
                    "last_check": health.last_check.isoformat() if health.last_check else None,
                    "response_time": health.response_time,
                    "error_rate": health.error_rate,
                    "uptime_percentage": health.uptime_percentage
                },
                "usage": {
                    "total_requests": usage.total_requests,
                    "successful_requests": usage.successful_requests,
                    "failed_requests": usage.failed_requests,
                    "concurrent_requests": usage.concurrent_requests,
                    "last_request": usage.last_request_time.isoformat() if usage.last_request_time else None
                },
                "costs": {
                    "daily_cost": cost.daily_cost,
                    "monthly_cost": cost.monthly_cost,
                    "cost_per_request": cost.cost_per_request
                },
                "limits": {
                    "requests_per_minute": limits.requests_per_minute,
                    "requests_per_hour": limits.requests_per_hour,
                    "requests_per_day": limits.requests_per_day,
                    "concurrent_requests": limits.concurrent_requests,
                    "cost_per_day": limits.cost_per_day
                }
            }

        return status


async def get_provider(provider_name: str) -> Optional[MediaProviderPlugin]:
    """Get an initialized provider instance."""
    return provider_registry.get_provider(provider_name)