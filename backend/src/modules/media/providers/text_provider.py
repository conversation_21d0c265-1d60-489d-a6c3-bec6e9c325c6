"""
Text Generation Provider for E-commerce Content
Handles text generation using various AI providers (OpenAI, Anthropic, etc.)
"""

import asyncio
import logging
import re
from typing import Dict, List, Optional, Any
import httpx
from datetime import datetime

from ..provider_interface import MediaProviderPlugin, ProviderConfig
from ..schemas import MediaGenerationRequest, MediaGenerationResult, GeneratedAsset, TextGenerationOutput
from ..template_manager import template_manager

logger = logging.getLogger(__name__)


class TextGenerationProvider(MediaProviderPlugin):
    """
    Text generation provider for creating SEO content, product descriptions, 
    ad copy, and other marketing text using AI language models.
    """
    
    def __init__(self):
        self.config: Optional[ProviderConfig] = None
        self.client: Optional[httpx.AsyncClient] = None
        self._initialized = False
    
    @property
    def provider_name(self) -> str:
        return "text_generator"
    
    @property
    def supported_media_types(self) -> List[str]:
        return ["text"]
    
    async def initialize(self, config: ProviderConfig) -> bool:
        """Initialize the text generation provider."""
        try:
            self.config = config
            
            # Initialize HTTP client
            self.client = httpx.AsyncClient(
                timeout=httpx.Timeout(config.timeout),
                headers={
                    "Authorization": f"Bearer {config.api_key}",
                    "Content-Type": "application/json"
                }
            )
            
            self._initialized = True
            logger.info(f"Initialized text generation provider: {config.name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize text provider: {e}")
            return False
    
    async def generate_media(self, request: MediaGenerationRequest) -> MediaGenerationResult:
        """Generate text content for the product."""
        if not self._initialized or not self.client:
            raise RuntimeError("Provider not initialized")
        
        try:
            start_time = datetime.now()
            
            # Generate different types of text content
            text_outputs = await self._generate_all_text_types(request)
            
            # Create generated assets
            assets = []
            for content_type, content in text_outputs.items():
                if content and content.strip():
                    asset = GeneratedAsset(
                        asset_id=f"text_{content_type}_{request.product_data.product_id}",
                        asset_type="text",
                        content=content,
                        metadata={
                            "content_type": content_type,
                            "product_id": request.product_data.product_id,
                            "generated_at": datetime.now().isoformat(),
                            "character_count": len(content),
                            "word_count": len(content.split())
                        }
                    )
                    assets.append(asset)
            
            # Calculate processing time
            processing_time = (datetime.now() - start_time).total_seconds()
            
            # Validate content
            validation_results = await self._validate_generated_content(text_outputs, request)
            
            return MediaGenerationResult(
                success=True,
                assets=assets,
                actual_processing_time=processing_time,
                validation_results=validation_results,
                needs_manual_review=validation_results.get("needs_review", False),
                idempotency_key=request.idempotency_key
            )
            
        except Exception as e:
            logger.error(f"Text generation failed: {e}")
            return MediaGenerationResult(
                success=False,
                error_message=str(e),
                idempotency_key=request.idempotency_key
            )
    
    async def _generate_all_text_types(self, request: MediaGenerationRequest) -> Dict[str, str]:
        """Generate all types of text content for the product."""
        product_data = request.product_data
        
        # Define text generation tasks
        text_tasks = [
            ("seo_title", "text/seo/meta_title"),
            ("seo_description", "text/seo/meta_description"),
            ("alt_text", "text/seo/alt_text"),
            ("short_caption", "text/marketing/short_caption"),
            ("ad_copy_30", "text/marketing/ad_copy_30"),
            ("product_bullets", "text/marketing/product_bullets"),
        ]
        
        # Generate content concurrently
        tasks = []
        for content_type, template_path in text_tasks:
            task = self._generate_single_text_type(content_type, template_path, product_data)
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        text_outputs = {}
        for i, (content_type, _) in enumerate(text_tasks):
            result = results[i]
            if isinstance(result, Exception):
                logger.error(f"Failed to generate {content_type}: {result}")
                text_outputs[content_type] = ""
            else:
                text_outputs[content_type] = result
        
        return text_outputs
    
    async def _generate_single_text_type(self, content_type: str, template_path: str, product_data) -> str:
        """Generate a single type of text content."""
        try:
            # Render template
            template_result = template_manager.render_template(template_path, product_data)
            
            if not template_result["validation"]["passed"]:
                logger.warning(f"Template validation failed for {content_type}: {template_result['validation']}")
            
            # Prepare AI generation request
            system_prompt = template_result["system_prompt"]
            user_prompt = template_result["content"]
            
            # Call AI provider
            generated_text = await self._call_ai_provider(system_prompt, user_prompt, template_result["constraints"])
            
            # Post-process and validate
            processed_text = self._post_process_text(generated_text, template_result["constraints"])
            
            return processed_text
            
        except Exception as e:
            logger.error(f"Error generating {content_type}: {e}")
            return ""
    
    async def _call_ai_provider(self, system_prompt: str, user_prompt: str, constraints: Dict[str, Any]) -> str:
        """Call the AI provider to generate text."""
        if not self.client or not self.config:
            raise RuntimeError("Provider not initialized")
        
        # Prepare request based on provider type
        if "openai" in self.config.base_url.lower():
            return await self._call_openai(system_prompt, user_prompt, constraints)
        elif "anthropic" in self.config.base_url.lower():
            return await self._call_anthropic(system_prompt, user_prompt, constraints)
        else:
            # Generic OpenAI-compatible API
            return await self._call_openai(system_prompt, user_prompt, constraints)
    
    async def _call_openai(self, system_prompt: str, user_prompt: str, constraints: Dict[str, Any]) -> str:
        """Call OpenAI-compatible API."""
        payload = {
            "model": "gpt-4",
            "messages": [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            "max_tokens": constraints.get("max_length", 500),
            "temperature": 0.7,
            "top_p": 0.9
        }
        
        response = await self.client.post(
            f"{self.config.base_url}/chat/completions",
            json=payload
        )
        response.raise_for_status()
        
        result = response.json()
        return result["choices"][0]["message"]["content"].strip()
    
    async def _call_anthropic(self, system_prompt: str, user_prompt: str, constraints: Dict[str, Any]) -> str:
        """Call Anthropic Claude API."""
        payload = {
            "model": "claude-3-sonnet-20240229",
            "max_tokens": constraints.get("max_length", 500),
            "system": system_prompt,
            "messages": [
                {"role": "user", "content": user_prompt}
            ]
        }
        
        response = await self.client.post(
            f"{self.config.base_url}/messages",
            json=payload,
            headers={"anthropic-version": "2023-06-01"}
        )
        response.raise_for_status()
        
        result = response.json()
        return result["content"][0]["text"].strip()
    
    def _post_process_text(self, text: str, constraints: Dict[str, Any]) -> str:
        """Post-process generated text to meet constraints."""
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text.strip())
        
        # Truncate if too long
        max_length = constraints.get("max_length")
        if max_length and len(text) > max_length:
            # Try to truncate at word boundary
            words = text[:max_length].split()
            if len(words) > 1:
                text = ' '.join(words[:-1])
            else:
                text = text[:max_length]
        
        # Remove forbidden phrases
        forbidden_phrases = constraints.get("forbidden_phrases", [])
        for phrase in forbidden_phrases:
            text = re.sub(re.escape(phrase), "", text, flags=re.IGNORECASE)
        
        return text.strip()
    
    async def _validate_generated_content(self, text_outputs: Dict[str, str], request: MediaGenerationRequest) -> Dict[str, Any]:
        """Validate generated content for quality and compliance."""
        validation_results = {
            "overall_passed": True,
            "content_checks": {},
            "safety_checks": {},
            "needs_review": False,
            "review_reasons": []
        }
        
        for content_type, content in text_outputs.items():
            # Basic content validation
            content_valid = self._validate_content_basic(content, content_type)
            validation_results["content_checks"][content_type] = content_valid
            
            if not content_valid["passed"]:
                validation_results["overall_passed"] = False
            
            # Safety validation
            safety_valid = self._validate_content_safety(content)
            validation_results["safety_checks"][content_type] = safety_valid
            
            if not safety_valid["passed"]:
                validation_results["needs_review"] = True
                validation_results["review_reasons"].extend(safety_valid.get("violations", []))
        
        return validation_results
    
    def _validate_content_basic(self, content: str, content_type: str) -> Dict[str, Any]:
        """Basic content validation."""
        validation = {"passed": True, "issues": []}
        
        # Check if content is empty
        if not content or not content.strip():
            validation["passed"] = False
            validation["issues"].append("Empty content")
            return validation
        
        # Check for placeholder text
        if "{{" in content or "}}" in content:
            validation["passed"] = False
            validation["issues"].append("Contains unresolved placeholders")
        
        # Check for common AI artifacts
        ai_artifacts = ["as an ai", "i cannot", "i don't have", "i'm sorry"]
        content_lower = content.lower()
        for artifact in ai_artifacts:
            if artifact in content_lower:
                validation["passed"] = False
                validation["issues"].append(f"Contains AI artifact: {artifact}")
        
        return validation
    
    def _validate_content_safety(self, content: str) -> Dict[str, Any]:
        """Safety and compliance validation."""
        validation = {"passed": True, "violations": []}
        
        # Check for inappropriate content (basic)
        inappropriate_terms = ["guaranteed", "miracle", "instant", "free money"]
        content_lower = content.lower()
        
        for term in inappropriate_terms:
            if term in content_lower:
                validation["violations"].append(f"Contains inappropriate claim: {term}")
        
        if validation["violations"]:
            validation["passed"] = False
        
        return validation
    
    async def get_job_status(self, job_id: str) -> Dict[str, Any]:
        """Get job status (text generation is typically synchronous)."""
        return {
            "job_id": job_id,
            "status": "completed",
            "progress": 100
        }
    
    async def download_media(self, media_url: str) -> bytes:
        """Download media content (not applicable for text)."""
        raise NotImplementedError("Text content doesn't require download")
    
    async def get_provider_info(self) -> Dict[str, Any]:
        """Get provider information."""
        return {
            "name": self.provider_name,
            "supported_media_types": self.supported_media_types,
            "capabilities": [
                "seo_content",
                "marketing_copy",
                "product_descriptions",
                "alt_text",
                "multi_language"
            ],
            "constraints": {
                "max_concurrent_requests": 10,
                "rate_limit_per_minute": 60
            }
        }
    
    async def cleanup(self) -> None:
        """Cleanup provider resources."""
        if self.client:
            await self.client.aclose()
            self.client = None
        self._initialized = False
