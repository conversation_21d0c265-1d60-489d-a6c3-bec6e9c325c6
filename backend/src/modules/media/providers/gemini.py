"""
Gemini AI Provider Plugin for ProductVideo platform.
Provides image and video generation using Google's Gemini AI services.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any

import httpx

from ..provider_interface import (
    MediaProviderPlugin,
    ProviderConfig
)
from ..schemas import MediaGenerationRequest, MediaGenerationResult

logger = logging.getLogger(__name__)


class GeminiProvider(MediaProviderPlugin):
    """Gemini AI provider plugin for image and video generation."""

    def __init__(self):
        self.client: Optional[httpx.AsyncClient] = None
        self.config: Optional[ProviderConfig] = None
        self.base_url = "https://generativelanguage.googleapis.com/v1beta"
        self.default_model = "gemini-1.5-flash"

    @property
    def provider_name(self) -> str:
        return "gemini"

    @property
    def supported_media_types(self) -> List[str]:
        return ["image", "video"]

    async def initialize(self, config: ProviderConfig) -> bool:
        """Initialize the Gemini provider."""
        try:
            self.config = config
            self.client = httpx.AsyncClient(
                timeout=config.timeout,
                headers={
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {config.api_key}" if config.api_key else ""
                }
            )

            if config.base_url:
                self.base_url = config.base_url

            logger.info(f"Initialized Gemini provider with model: {self.default_model}")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize Gemini provider: {e}")
            return False

    async def generate_media(self, request: MediaGenerationRequest) -> MediaGenerationResult:
        """Generate media using Gemini."""
        if not self.client or not self.config:
            return MediaGenerationResult(
                success=False,
                error_message="Provider not initialized"
            )

        try:
            if request.media_type == "image":
                return await self._generate_image(request)
            elif request.media_type == "video":
                return await self._generate_video(request)
            else:
                return MediaGenerationResult(
                    success=False,
                    error_message=f"Unsupported media type: {request.media_type}"
                )

        except Exception as e:
            logger.error(f"Gemini media generation failed: {e}")
            return MediaGenerationResult(
                success=False,
                error_message=str(e)
            )

    async def _generate_image(self, request: MediaGenerationRequest) -> MediaGenerationResult:
        """Generate an image using Gemini."""
        if not self.config.api_key:
            logger.warning("Gemini API key not configured, using mock response")
            return self._mock_generation_result(request, "image")

        try:
            # Create optimized prompt
            prompt = self._create_image_prompt(request)

            # Prepare the request payload
            payload = {
                "contents": [{
                    "parts": [{
                        "text": f"Generate an image: {prompt}"
                    }]
                }],
                "generationConfig": {
                    "temperature": 0.7,
                    "topK": 40,
                    "topP": 0.95,
                    "maxOutputTokens": 1024,
                }
            }

            # Make the API call
            response = await self.client.post(
                f"{self.base_url}/models/{self.default_model}:generateContent",
                json=payload,
                params={"key": self.config.api_key}
            )

            if response.status_code == 200:
                result = response.json()
                # Extract the generated content
                content = result.get("candidates", [{}])[0].get("content", {})

                # For now, return a mock URL since Gemini text generation
                # doesn't directly generate images
                images = []
                for i in range(request.num_images):
                    images.append({
                        "image_url": f"https://storage.example.com/generated-image-{hash(prompt)}_{i}.jpg",
                        "thumbnail_url": f"https://storage.example.com/preview-{hash(prompt)}_{i}.jpg",
                        "width": self._get_width_for_aspect(request.aspect_ratio),
                        "height": self._get_height_for_aspect(request.aspect_ratio),
                        "style": request.style,
                        "variant_name": f"variant_{i+1}"
                    })

                return MediaGenerationResult(
                    success=True,
                    provider_job_id=f"gen_{hash(prompt)}",
                    images=images,
                    estimated_completion_time=30
                )
            else:
                logger.error(f"Gemini API error: {response.status_code} - {response.text}")
                return MediaGenerationResult(
                    success=False,
                    error_message=f"API error: {response.status_code}"
                )

        except Exception as e:
            logger.error(f"Error generating image with Gemini: {e}")
            return MediaGenerationResult(
                success=False,
                error_message=str(e)
            )

    async def _generate_video(self, request: MediaGenerationRequest) -> MediaGenerationResult:
        """Generate a video using Gemini."""
        if not self.config.api_key:
            logger.warning("Gemini API key not configured, using mock response")
            return self._mock_generation_result(request, "video")

        try:
            # Create video prompt
            prompt = self._create_video_prompt(request)

            payload = {
                "contents": [{
                    "parts": [{
                        "text": f"Generate a video concept: {prompt}"
                    }]
                }],
                "generationConfig": {
                    "temperature": 0.7,
                    "topK": 40,
                    "topP": 0.95,
                    "maxOutputTokens": 1024,
                }
            }

            response = await self.client.post(
                f"{self.base_url}/models/{self.default_model}:generateContent",
                json=payload,
                params={"key": self.config.api_key}
            )

            if response.status_code == 200:
                # Generate variants with different aspect ratios
                variants = []
                variant_names = ["square", "vertical", "horizontal", "story"]
                for i, variant_name in enumerate(variant_names):
                    aspect_ratio = self._get_aspect_ratio_for_variant(variant_name)
                    variants.append({
                        "variant_name": variant_name,
                        "video_url": f"https://storage.example.com/generated-video-{hash(prompt)}_{i}.mp4",
                        "thumbnail_url": f"https://storage.example.com/preview-{hash(prompt)}_{i}.jpg",
                        "duration": 30,
                        "resolution": self._get_resolution_for_aspect(aspect_ratio),
                        "aspect_ratio": aspect_ratio
                    })

                return MediaGenerationResult(
                    success=True,
                    provider_job_id=f"gen_{hash(prompt)}",
                    variants=variants,
                    estimated_completion_time=180
                )
            else:
                logger.error(f"Gemini API error: {response.status_code} - {response.text}")
                return MediaGenerationResult(
                    success=False,
                    error_message=f"API error: {response.status_code}"
                )

        except Exception as e:
            logger.error(f"Error generating video with Gemini: {e}")
            return MediaGenerationResult(
                success=False,
                error_message=str(e)
            )

    def _create_image_prompt(self, request: MediaGenerationRequest) -> str:
        """Create optimized prompt for Gemini image generation."""
        if request.custom_prompt:
            return request.custom_prompt

        base_prompt = f"Professional product photography of {request.product_title}"

        if request.product_description:
            base_prompt += f", {request.product_description[:150]}"

        # Add style-specific elements
        style_prompts = {
            "product_photography": "clean white background, studio lighting, high resolution, commercial photography",
            "lifestyle": "lifestyle setting, natural lighting, in-use context, appealing environment",
            "minimalist": "minimal background, clean composition, simple elegant styling",
            "luxury": "premium setting, sophisticated lighting, high-end presentation, luxury aesthetic",
            "social_media": "trendy, eye-catching, social media optimized, vibrant colors"
        }

        style_addition = style_prompts.get(request.style, "professional product photography")
        base_prompt += f", {style_addition}"

        # Add quality and technical specifications
        base_prompt += ", 8K resolution, sharp focus, professional lighting, high quality"

        return base_prompt

    def _create_video_prompt(self, request: MediaGenerationRequest) -> str:
        """Create optimized prompt for Gemini video generation."""
        base_prompt = f"Create a professional product video showcasing {request.product_title}."

        if request.product_description:
            base_prompt += f" Product details: {request.product_description[:200]}"

        base_prompt += " The video should be high-quality, well-lit, with smooth camera movements and professional presentation."

        if request.template_id:
            template_styles = {
                "modern_product_showcase": "modern, clean aesthetic with smooth transitions",
                "dynamic_lifestyle": "dynamic, energetic with lifestyle integration",
                "minimalist_clean": "minimalist, elegant with focus on product details",
                "luxury_premium": "luxury, sophisticated with premium feel"
            }
            style = template_styles.get(request.template_id, "professional product showcase")
            base_prompt += f" Style: {style}."

        return base_prompt

    def _get_width_for_aspect(self, aspect_ratio: str) -> int:
        """Get width for aspect ratio."""
        dimensions = {
            "1:1": 1024,
            "16:9": 1920,
            "9:16": 1080,
            "4:5": 1080,
            "3:4": 1080
        }
        return dimensions.get(aspect_ratio, 1024)

    def _get_height_for_aspect(self, aspect_ratio: str) -> int:
        """Get height for aspect ratio."""
        dimensions = {
            "1:1": 1024,
            "16:9": 1080,
            "9:16": 1920,
            "4:5": 1350,
            "3:4": 1440
        }
        return dimensions.get(aspect_ratio, 1024)

    def _get_aspect_ratio_for_variant(self, variant_name: str) -> str:
        """Get aspect ratio for variant."""
        ratios = {
            "square": "1:1",
            "vertical": "9:16",
            "horizontal": "16:9",
            "story": "9:16"
        }
        return ratios.get(variant_name, "16:9")

    def _get_resolution_for_aspect(self, aspect_ratio: str) -> str:
        """Get resolution for aspect ratio."""
        resolutions = {
            "1:1": "1080x1080",
            "9:16": "1080x1920",
            "16:9": "1920x1080",
            "4:5": "1080x1350"
        }
        return resolutions.get(aspect_ratio, "1920x1080")

    def _mock_generation_result(self, request: MediaGenerationRequest, media_type: str) -> MediaGenerationResult:
        """Create a mock generation result for testing."""
        file_extension = "jpg" if media_type == "image" else "mp4"
        generation_id = f"mock_{hash(request.product_title)}_{media_type}"

        if media_type == "image":
            images = []
            for i in range(request.num_images):
                images.append({
                    "image_url": f"https://storage.example.com/{generation_id}_{i}.{file_extension}",
                    "thumbnail_url": f"https://storage.example.com/preview_{generation_id}_{i}.jpg",
                    "width": self._get_width_for_aspect(request.aspect_ratio),
                    "height": self._get_height_for_aspect(request.aspect_ratio),
                    "style": request.style,
                    "variant_name": f"variant_{i+1}"
                })

            return MediaGenerationResult(
                success=True,
                provider_job_id=generation_id,
                images=images,
                estimated_completion_time=30
            )
        else:  # video
            variants = []
            for i, variant_name in enumerate(["square", "vertical", "horizontal", "story"]):
                aspect_ratio = self._get_aspect_ratio_for_variant(variant_name)
                variants.append({
                    "variant_name": variant_name,
                    "video_url": f"https://storage.example.com/{generation_id}_{i}.{file_extension}",
                    "thumbnail_url": f"https://storage.example.com/preview_{generation_id}_{i}.jpg",
                    "duration": 30,
                    "resolution": self._get_resolution_for_aspect(aspect_ratio),
                    "aspect_ratio": aspect_ratio
                })

            return MediaGenerationResult(
                success=True,
                provider_job_id=generation_id,
                variants=variants,
                estimated_completion_time=180
            )

    async def get_job_status(self, job_id: str) -> Dict[str, Any]:
        """Get Gemini job status."""
        if not self.client:
            return {"status": "error", "error": "Provider not initialized"}

        try:
            # Gemini doesn't have a direct job status endpoint for generated content
            # This is a mock implementation
            return {
                "status": "completed",
                "progress": 100,
                "ready": True,
                "job_id": job_id
            }
        except Exception as e:
            logger.error(f"Failed to get Gemini job status: {e}")
            return {"status": "error", "error": str(e)}

    async def download_media(self, media_url: str) -> bytes:
        """Download media from Gemini."""
        if not self.client:
            raise ValueError("Provider not initialized")

        response = await self.client.get(media_url)
        response.raise_for_status()
        return response.content

    async def get_provider_info(self) -> Dict[str, Any]:
        """Get Gemini provider information."""
        return {
            "name": "Google Gemini AI",
            "supported_formats": ["image", "video"],
            "models": [self.default_model],
            "max_images_per_request": 4,
            "max_variants_per_request": 4,
            "supported_aspect_ratios": ["1:1", "16:9", "9:16", "4:5", "3:4"],
            "max_video_duration_seconds": 60,
            "estimated_cost_per_image": 0.04,  # Approximate cost in USD
            "estimated_cost_per_video": 1.0    # Approximate cost in USD
        }

    async def cleanup(self) -> None:
        """Cleanup Gemini provider resources."""
        if self.client:
            await self.client.aclose()
            self.client = None
            logger.info("Cleaned up Gemini provider")