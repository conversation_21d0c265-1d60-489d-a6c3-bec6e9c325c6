"""
Mock Provider Plugin for ProductVideo platform.
Provides mock implementations for testing and development.
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any

from ..provider_interface import (
    MediaProviderPlugin,
    ProviderConfig
)
from ..schemas import MediaGenerationRequest, MediaGenerationResult

logger = logging.getLogger(__name__)


class MockProvider(MediaProviderPlugin):
    """Mock provider plugin for testing and development."""

    def __init__(self):
        self.config: Optional[ProviderConfig] = None

    @property
    def provider_name(self) -> str:
        return "mock"

    @property
    def supported_media_types(self) -> List[str]:
        return ["image", "video", "voice"]

    async def initialize(self, config: ProviderConfig) -> bool:
        """Initialize the mock provider."""
        self.config = config
        logger.info("Initialized Mock provider")
        return True

    async def generate_media(self, request: MediaGenerationRequest) -> MediaGenerationResult:
        """Mock media generation."""
        await asyncio.sleep(1)  # Simulate processing time

        if request.media_type == "image":
            images = []
            for i in range(request.num_images):
                images.append({
                    "image_url": f"https://mock-storage.com/images/{request.product_title}_variant_{i+1}.jpg",
                    "thumbnail_url": f"https://mock-storage.com/thumbnails/{request.product_title}_variant_{i+1}.jpg",
                    "width": self._get_width_for_aspect(request.aspect_ratio),
                    "height": self._get_height_for_aspect(request.aspect_ratio),
                    "style": request.style,
                    "variant_name": f"variant_{i+1}"
                })

            return MediaGenerationResult(
                success=True,
                provider_job_id=f"mock_img_job_{datetime.now().timestamp()}",
                images=images,
                estimated_completion_time=30
            )

        elif request.media_type == "video":
            videos = []
            for i in range(request.variants_count):
                videos.append({
                    "video_url": f"https://mock-storage.com/videos/{request.product_title}_variant_{i+1}.mp4",
                    "thumbnail_url": f"https://mock-storage.com/thumbnails/{request.product_title}_variant_{i+1}.jpg",
                    "width": self._get_width_for_aspect(request.aspect_ratio),
                    "height": self._get_height_for_aspect(request.aspect_ratio),
                    "variant_name": f"variant_{i+1}"
                })

            return MediaGenerationResult(
                success=True,
                provider_job_id=f"mock_vid_job_{datetime.now().timestamp()}",
                variants=videos,
                estimated_completion_time=30
            )

        elif request.media_type == "voice":
            return MediaGenerationResult(
                success=True,
                provider_job_id=f"mock_voice_job_{datetime.now().timestamp()}",
                variants=[{
                    "voice_url": f"https://mock-storage.com/voices/{request.text_input[:10]}.mp3",
                    "duration": 5
                }],
                estimated_completion_time=5
            )

        else:
            return MediaGenerationResult(
                success=False,
                error_message=f"Unsupported media type: {request.media_type}"
            )

    def _get_width_for_aspect(self, aspect_ratio: str) -> int:
        """Get width for aspect ratio."""
        return 1024

    def _get_height_for_aspect(self, aspect_ratio: str) -> int:
        """Get height for aspect ratio."""
        return 1024

    async def get_job_status(self, job_id: str) -> Dict[str, Any]:
        """Mock job status."""
        return {
            "status": "completed",
            "progress": 100,
            "ready": True
        }

    async def download_media(self, media_url: str) -> bytes:
        """Mock media download."""
        return b"mock_media_content"

    async def get_provider_info(self) -> Dict[str, Any]:
        """Get mock provider information."""
        return {
            "name": "Mock Provider",
            "supported_formats": ["image", "video", "voice"],
            "models": ["mock-model-v1"],
            "max_items_per_request": 4,
            "supported_aspect_ratios": ["1:1", "16:9", "9:16", "4:5", "3:4"],
            "estimated_cost_per_item": 0.0  # Free for testing
        }

    async def cleanup(self) -> None:
        """Cleanup mock provider resources."""
        logger.info("Cleaned up Mock provider")