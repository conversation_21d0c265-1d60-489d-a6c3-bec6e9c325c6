"""
Banana AI Provider Plugin for ProductVideo platform.
Provides image generation using Banana AI services.
"""

import asyncio
import json
import logging
from typing import Dict, List, Optional, Any

import httpx

from ..provider_interface import (
    MediaProviderPlugin,
    ProviderConfig
)
from ..schemas import MediaGenerationRequest, MediaGenerationResult, GeneratedAsset

logger = logging.getLogger(__name__)


class BananaProvider(MediaProviderPlugin):
    """Banana AI provider plugin for image generation."""

    def __init__(self):
        self.client: Optional[httpx.AsyncClient] = None
        self.config: Optional[ProviderConfig] = None
        self.base_url = "https://api.banana.dev/v1"
        self.model_key = "flux-1.1-pro"  # Using Flux 1.1 Pro model

    @property
    def provider_name(self) -> str:
        return "banana"

    @property
    def supported_media_types(self) -> List[str]:
        return ["image"]

    async def initialize(self, config: ProviderConfig) -> bool:
        """Initialize the Banana provider."""
        try:
            self.config = config
            self.client = httpx.AsyncClient(
                timeout=config.timeout,
                headers={
                    "Authorization": f"Bearer {config.api_key}",
                    "Content-Type": "application/json"
                }
            )

            if config.base_url:
                self.base_url = config.base_url

            logger.info(f"Initialized Banana provider with model: {self.model_key}")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize Banana provider: {e}")
            return False

    async def generate_media(self, request: MediaGenerationRequest) -> MediaGenerationResult:
        """Generate images using Banana."""
        if not self.client or not self.config:
            return MediaGenerationResult(
                success=False,
                error_message="Provider not initialized"
            )

        try:
            # Create optimized prompt for product images
            prompt = self._create_image_prompt(request)

            # Get configuration from request
            num_images = getattr(request, 'num_variants', 4)
            aspect_ratio = request.custom_config.get("aspect_ratio", "1:1") if request.custom_config else "1:1"

            payload = {
                "modelKey": self.model_key,
                "modelInputs": {
                    "prompt": prompt,
                    "negative_prompt": request.custom_config.get("negative_prompt", "") if request.custom_config else "",
                    "num_images": num_images,
                    "width": self._get_width_for_aspect(aspect_ratio),
                    "height": self._get_height_for_aspect(aspect_ratio),
                    "guidance_scale": request.custom_config.get("guidance", 7.5) if request.custom_config else 7.5,
                    "num_inference_steps": request.custom_config.get("steps", 50) if request.custom_config else 50,
                    "seed": request.custom_config.get("seed", -1) if request.custom_config else -1,
                    "safety_check": request.safety_checks
                }
            }

            response = await self.client.post(
                f"{self.base_url}/start/v4",
                json=payload
            )
            response.raise_for_status()

            data = response.json()

            # Process generated images into GeneratedAsset objects
            assets = []
            if "modelOutputs" in data and "images" in data["modelOutputs"]:
                for i, image_data in enumerate(data["modelOutputs"]["images"]):
                    asset = GeneratedAsset(
                        asset_id=f"banana_img_{request.product_data.product_id}_{i}",
                        asset_type="image",
                        url=image_data.get("url"),
                        metadata={
                            "provider": "banana",
                            "model": self.model_key,
                            "prompt": prompt,
                            "width": self._get_width_for_aspect(aspect_ratio),
                            "height": self._get_height_for_aspect(aspect_ratio),
                            "variant_index": i,
                            "style": request.custom_config.get("style", "professional") if request.custom_config else "professional",
                            "aspect_ratio": aspect_ratio,
                            "thumbnail_url": image_data.get("thumbnail_url")
                        },
                        dimensions=f"{self._get_width_for_aspect(aspect_ratio)}x{self._get_height_for_aspect(aspect_ratio)}",
                        format="jpg"
                    )
                    assets.append(asset)

            # Legacy images format for backward compatibility
            images = []
            for asset in assets:
                images.append({
                    "image_url": asset.url,
                    "thumbnail_url": asset.metadata.get("thumbnail_url"),
                    "width": asset.metadata.get("width"),
                    "height": asset.metadata.get("height"),
                    "style": asset.metadata.get("style"),
                    "variant_name": f"variant_{asset.metadata.get('variant_index', 0) + 1}"
                })

            return MediaGenerationResult(
                success=True,
                provider_job_id=data.get("id"),
                assets=assets,
                images=images,  # Legacy support
                estimated_completion_time=data.get("estimated_time", 60),
                idempotency_key=request.idempotency_key
            )

        except Exception as e:
            logger.error(f"Banana image generation failed: {e}")
            return MediaGenerationResult(
                success=False,
                error_message=str(e)
            )

    def _create_image_prompt(self, request: MediaGenerationRequest) -> str:
        """Create optimized prompt for Banana image generation."""
        # Use custom prompt from template if available
        if request.custom_config and "prompt" in request.custom_config:
            return request.custom_config["prompt"]

        # Fallback to legacy prompt creation for backward compatibility
        if hasattr(request, 'product_title'):
            base_prompt = f"Professional product photography of {request.product_title}"
        else:
            # Use product data if available
            product_data = getattr(request, 'product_data', None)
            if product_data:
                base_prompt = f"Professional product photography of {product_data.title}"

                # Add product context
                if product_data.materials:
                    base_prompt += f", made of {', '.join(product_data.materials[:2])}"

                if product_data.colors:
                    base_prompt += f", in {product_data.colors[0]} color"

                # Add brand tone context
                tone_map = {
                    "luxury": "premium, sophisticated, high-end",
                    "minimal": "clean, simple, minimalist",
                    "friendly": "approachable, warm, inviting",
                    "technical": "precise, detailed, professional",
                    "playful": "fun, vibrant, energetic"
                }

                tone_desc = tone_map.get(product_data.shop_branding.tone.value, "professional")
                base_prompt += f", {tone_desc} aesthetic"
            else:
                base_prompt = "Professional product photography"

        # Add style-specific elements
        style = request.custom_config.get("style", "professional") if request.custom_config else "professional"
        style_prompts = {
            "hero_studio": "clean white background, studio lighting, high resolution, commercial photography, centered composition",
            "lifestyle": "lifestyle setting, natural lighting, in-use context, appealing environment, real-world scenario",
            "model_wearing": "worn by model, fashion photography, lifestyle context, professional modeling",
            "lifestyle_tech": "modern workspace, tech environment, clean setup, professional use case",
            "room_setting": "interior design, home setting, styled room, natural lighting, contextual placement",
            "minimalist": "minimal background, clean composition, simple elegant styling",
            "luxury": "premium setting, sophisticated lighting, high-end presentation, luxury aesthetic",
            "social_media": "trendy, eye-catching, social media optimized, vibrant colors"
        }

        style_addition = style_prompts.get(style, "professional product photography")
        base_prompt += f", {style_addition}"

        # Add quality and technical specifications
        base_prompt += ", 8K resolution, sharp focus, professional lighting, high quality, commercial grade"

        # Add negative prompt elements to main prompt as exclusions
        base_prompt += ", no watermarks, no text overlays, no logos except product branding"

        return base_prompt

    def _get_width_for_aspect(self, aspect_ratio: str) -> int:
        """Get width for aspect ratio."""
        dimensions = {
            "1:1": 1024,
            "16:9": 1920,
            "9:16": 1080,
            "4:5": 1080,
            "3:4": 1080
        }
        return dimensions.get(aspect_ratio, 1024)

    def _get_height_for_aspect(self, aspect_ratio: str) -> int:
        """Get height for aspect ratio."""
        dimensions = {
            "1:1": 1024,
            "16:9": 1080,
            "9:16": 1920,
            "4:5": 1350,
            "3:4": 1440
        }
        return dimensions.get(aspect_ratio, 1024)

    async def get_job_status(self, job_id: str) -> Dict[str, Any]:
        """Get Banana job status."""
        if not self.client:
            return {"status": "error", "error": "Provider not initialized"}

        try:
            response = await self.client.get(
                f"{self.base_url}/check/v4/{job_id}"
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Failed to get Banana job status: {e}")
            return {"status": "error", "error": str(e)}

    async def download_media(self, media_url: str) -> bytes:
        """Download media from Banana."""
        if not self.client:
            raise ValueError("Provider not initialized")

        response = await self.client.get(media_url)
        response.raise_for_status()
        return response.content

    async def get_provider_info(self) -> Dict[str, Any]:
        """Get Banana provider information."""
        return {
            "name": "Banana AI",
            "supported_formats": ["image"],
            "models": [self.model_key],
            "max_images_per_request": 4,
            "supported_aspect_ratios": ["1:1", "16:9", "9:16", "4:5", "3:4"],
            "estimated_cost_per_image": 0.02  # Approximate cost in USD
        }

    async def cleanup(self) -> None:
        """Cleanup Banana provider resources."""
        if self.client:
            await self.client.aclose()
            self.client = None
            logger.info("Cleaned up Banana provider")