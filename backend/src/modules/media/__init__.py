"""
Media Module
Provides AI-powered media services for ProductVideo platform.
Generic module that uses provider plugins for different AI services.
"""

# Main services
from .service import media_service

# Provider system
from .provider_interface import provider_registry
from .provider_manager import provider_manager

# Template management
from .template_manager import template_manager, get_template_manager, render_product_template

# Configuration
from .providers_config import (
    PROVIDER_CONFIGS,
    get_provider_config,
    get_default_provider,
    get_fallback_chain
)

# Models and schemas
from . import models
from . import schemas

__all__ = [
    # Services
    "media_service",

    # Provider system
    "provider_registry",
    "provider_manager",

    # Template management
    "template_manager",
    "get_template_manager",
    "render_product_template",

    # Configuration
    "PROVIDER_CONFIGS",
    "get_provider_config",
    "get_default_provider",
    "get_fallback_chain",

    # Modules
    "models",
    "schemas",
]
