import logging
from typing import Dict, Any, Optional
from datetime import datetime, timezone
from contextlib import asynccontextmanager

from core.db.database import get_db_session_factory
from modules.media.models import MediaVariant, MediaVariantStatus
from plugins import get_media_service
from core.metrics import media_push_duration, media_push_failures

logger = logging.getLogger(__name__)


class MediaPushProcessor:
    """Enhanced media push processor with retry logic, monitoring, and multi-platform support."""

    def __init__(self):
        self.db_factory = get_db_session_factory()
        self.max_retries = 3
        self.retry_delay_base = 2  # Base delay in seconds for exponential backoff

    @asynccontextmanager
    async def get_db_session(self):
        """Context manager for database sessions."""
        db = self.db_factory()
        try:
            yield db
        finally:
            await db.close()

    def process(self, job_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process media push job with enhanced error handling and monitoring.

        Args:
            job_data: Job data dictionary

        Returns:
            Job result with detailed status
        """
        import asyncio
        return asyncio.run(self._process_async(job_data))

    async def _process_async(self, job_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Async implementation with comprehensive error handling and retry logic.
        """
        tenant_id = job_data.get("tenant_id")
        variant_id = job_data.get("variant_id")
        product_id = job_data.get("product_id")
        shop_domain = job_data.get("shop_domain")
        store_type = job_data.get("store_type", "shopify")

        if not all([tenant_id, variant_id, product_id, shop_domain]):
            raise ValueError("Missing required parameters: tenant_id, variant_id, product_id, shop_domain")

        start_time = datetime.now(timezone.utc)
        logger.info(f"Processing media push job for variant {variant_id} to {store_type} ({shop_domain})")

        try:
            # Get media service for the platform
            media_service = get_media_service()
            if not media_service:
                raise ValueError(f"No media service available for store type: {store_type}")

            async with self.get_db_session() as db:
                # Get and validate media variant
                variant = await db.get(MediaVariant, variant_id)
                if not variant:
                    raise ValueError(f"Media variant {variant_id} not found")

                # Check if already pushed
                if variant.push_status == "completed" and variant.media_id:
                    logger.info(f"Variant {variant_id} already pushed to platform")
                    return {
                        "success": True,
                        "variant_id": variant_id,
                        "media_id": variant.media_id,
                        "status": "already_pushed"
                    }

                # Determine media URL
                media_url = self._get_media_url(variant)
                if not media_url:
                    raise ValueError(f"Media variant {variant_id} has no media URL")

                # Update variant status to pushing
                variant.push_status = "pushing"
                variant.push_error_message = None
                await db.commit()

                # Attempt to push with retry logic
                push_result = await self._push_with_retry(
                    media_service, store_type, shop_domain, product_id,
                    media_url, variant.variant_name, job_data
                )

                end_time = datetime.now(timezone.utc)
                duration = (end_time - start_time).total_seconds()

                if push_result["success"]:
                    # Update variant with success
                    variant.push_status = "completed"
                    variant.media_id = push_result.get("media_id")
                    variant.pushed_at = end_time
                    await db.commit()

                    # Record success metrics
                    media_push_duration.labels(
                        platform=store_type,
                        status="success"
                    ).observe(duration)

                    logger.info(f"Successfully pushed variant {variant_id} to {store_type}")

                    return {
                        "success": True,
                        "variant_id": variant_id,
                        "media_id": push_result.get("media_id"),
                        "platform": store_type,
                        "duration_seconds": duration,
                        "status": "completed"
                    }
                else:
                    # Update variant with failure
                    variant.push_status = "failed"
                    variant.push_error_message = push_result.get("error", "Unknown error")
                    await db.commit()

                    # Record failure metrics
                    media_push_failures.labels(
                        platform=store_type,
                        failure_reason=push_result.get("failure_reason", "unknown")
                    ).inc()

                    logger.error(f"Failed to push variant {variant_id} to {store_type}: {push_result.get('error')}")

                    return {
                        "success": False,
                        "variant_id": variant_id,
                        "error": push_result.get("error"),
                        "platform": store_type,
                        "duration_seconds": duration,
                        "status": "failed"
                    }

        except Exception as e:
            logger.error(f"Critical error in media push job for variant {variant_id}: {e}")

            # Update variant status on critical error
            try:
                async with self.get_db_session() as db:
                    variant = await db.get(MediaVariant, variant_id)
                    if variant:
                        variant.push_status = "failed"
                        variant.push_error_message = f"Critical error: {str(e)}"
                        await db.commit()
            except Exception as db_error:
                logger.error(f"Failed to update variant status: {db_error}")

            # Record critical failure metrics
            media_push_failures.labels(
                platform=store_type,
                failure_reason="critical_error"
            ).inc()

            raise e

    def _get_media_url(self, variant: MediaVariant) -> Optional[str]:
        """Extract the appropriate media URL from variant."""
        return variant.video_url or variant.image_url or variant.voice_url

    async def _push_with_retry(
        self,
        media_service,
        store_type: str,
        shop_domain: str,
        product_id: str,
        media_url: str,
        variant_name: str,
        job_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Push media with retry logic and exponential backoff.

        Returns:
            Push result dictionary
        """
        last_error = None
        last_failure_reason = "unknown"

        for attempt in range(self.max_retries):
            try:
                logger.info(f"Push attempt {attempt + 1}/{self.max_retries} for {media_url}")

                # Prepare push parameters
                push_params = {
                    "store_type": store_type,
                    "shop_domain": shop_domain,
                    "product_id": product_id,
                    "media_url": media_url,
                    "alt_text": job_data.get("alt_text", f"Product media - {variant_name}")
                }

                # Add optional parameters
                if "position" in job_data:
                    push_params["position"] = job_data["position"]

                # Execute push
                push_result = await media_service.push_media_to_product(**push_params)

                if push_result.get("success"):
                    return push_result

                # Handle specific error types
                error_msg = push_result.get("error", "Unknown error")
                last_error = error_msg

                # Check for rate limiting
                if "rate limit" in error_msg.lower() or "429" in error_msg:
                    last_failure_reason = "rate_limited"
                    if attempt < self.max_retries - 1:
                        # Longer delay for rate limiting
                        delay = self.retry_delay_base * (3 ** attempt)
                        logger.warning(f"Rate limited, waiting {delay}s before retry")
                        import asyncio
                        await asyncio.sleep(delay)
                        continue

                # Check for temporary errors
                elif any(keyword in error_msg.lower() for keyword in ["timeout", "connection", "temporary"]):
                    last_failure_reason = "temporary_error"
                    if attempt < self.max_retries - 1:
                        delay = self.retry_delay_base ** attempt
                        logger.warning(f"Temporary error, waiting {delay}s before retry")
                        import asyncio
                        await asyncio.sleep(delay)
                        continue

                # Permanent error - don't retry
                last_failure_reason = "permanent_error"
                break

            except Exception as e:
                last_error = str(e)
                last_failure_reason = "exception"
                logger.error(f"Push attempt {attempt + 1} failed with exception: {e}")

                if attempt < self.max_retries - 1:
                    delay = self.retry_delay_base ** attempt
                    logger.warning(f"Exception occurred, waiting {delay}s before retry")
                    import asyncio
                    await asyncio.sleep(delay)
                else:
                    break

        return {
            "success": False,
            "error": last_error or "All retry attempts failed",
            "failure_reason": last_failure_reason
        }