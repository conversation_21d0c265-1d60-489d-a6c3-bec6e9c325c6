"""
Shopify Product Syncer - Handles synchronization of Shopify products.
"""

import logging
import time
from datetime import datetime
from typing import Dict, Any, Optional, List, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import text as sql_text

from ....base.base_syncer import BaseSyncer
from modules.sync.models import SyncCheckpoint

logger = logging.getLogger(__name__)


class ProductsSyncer(BaseSyncer):
    """
    Syncer for Shopify products.

    Handles the synchronization of product data from Airbyte to the production database.
    """

    def __init__(self, airbyte_engine=None, platform_config=None):
        super().__init__(airbyte_engine, platform_config)
        self.entity_type = 'products'

    def sync(self, db: Session, store_id: int) -> Dict[str, Any]:
        """
        Sync products for a specific store with comprehensive statistics tracking.
        """
        total_processed = 0
        batch_size = self.get_batch_size()
        last_sync_time = self.get_last_sync_time(db, store_id)

        store = self.get_store_info(db, store_id)
        shop_url = self.get_shop_url(store)

        # Start comprehensive statistics tracking
        self.start_sync_tracking(db, store_id)
        self.sync_stats['source_filter_criteria'] = {'shop_url': shop_url}

        logger.info(f"🚀 Starting products sync for store {store_id} (shop_url: {shop_url})")

        try:
            airbyte_engine = self.airbyte_engine or self.create_airbyte_engine()
            with airbyte_engine.connect() as airbyte_conn:
                # Check total count for this store
                test_query = airbyte_conn.execute(sql_text("SELECT COUNT(*) FROM products WHERE shop_url = :shop_url"), {'shop_url': shop_url})
                total_count = test_query.scalar()
                logger.info(f"Debug: Found {total_count} records in Airbyte products table for shop_url: {shop_url}")

                # Process data in batches until no more data
                offset = 0
                iteration = 1
                errors_in_sync = 0

                while True:
                    batch_start_time = time.time()
                    logger.info(f"Iteration {iteration}: Fetching data from Airbyte products table")

                    try:
                        select_sql = self.get_select_query(store, last_sync_time, offset, batch_size)
                        airbyte_result = airbyte_conn.execute(sql_text(select_sql), {
                            'last_sync_time': last_sync_time,
                            'batch_size': batch_size,
                            'offset': offset,
                            'store_id': store_id,
                            'shop_url': shop_url
                        })
                        rows = airbyte_result.fetchall()

                        if not rows:
                            logger.info(f"🎉 Iteration {iteration}: No more records to sync!")
                            break

                        logger.info(f"Iteration {iteration}: Fetched {len(rows)} records from Airbyte")

                        # Transform and insert data
                        insert_data = []
                        batch_errors = 0

                        for row in rows:
                            try:
                                transformed_row = self.transform_row(row, store_id)
                                if transformed_row:
                                    insert_data.append(transformed_row)
                                else:
                                    self.track_data_quality_issue('invalid')
                                    batch_errors += 1
                            except Exception as e:
                                self.track_error('transformation_error', str(e), str(row[0]) if row else None)
                                self.track_data_quality_issue('transformation_error')
                                batch_errors += 1
                                continue

                        batch_processed = 0
                        if insert_data:
                            try:
                                insert_sql = self.get_insert_query()
                                from psycopg2.extras import execute_values
                                with db.connection().connection.cursor() as cursor:
                                    execute_values(cursor, insert_sql, insert_data)
                                    batch_processed = cursor.rowcount

                                db.commit()
                                total_processed += batch_processed
                                logger.info(f"Iteration {iteration}: Successfully inserted {batch_processed} products (total: {total_processed})")

                                # Track successful batch
                                batch_time = time.time() - batch_start_time
                                self.track_batch_processing(iteration, len(insert_data), batch_time, batch_processed, batch_errors)

                            except Exception as e:
                                db.rollback()
                                self.track_error('database_insert_error', str(e))
                                batch_errors += len(insert_data)
                                errors_in_sync += batch_errors
                                logger.error(f"Error inserting batch {iteration}: {e}")

                        # Move to next batch
                        offset += batch_size
                        iteration += 1

                    except Exception as e:
                        self.track_error('batch_processing_error', str(e))
                        errors_in_sync += 1
                        logger.error(f"Error processing batch {iteration}: {e}")
                        break

                logger.info(f"🏁 PRODUCTS SYNC COMPLETE!")
                logger.info(f"   Total iterations: {iteration - 1}")
                logger.info(f"   Total records processed: {total_processed}")
                logger.info(f"   Total errors: {errors_in_sync}")

                # Finalize comprehensive statistics
                self.finalize_sync_statistics(db, store_id)

                # Update sync checkpoint with comprehensive statistics
                self.update_sync_checkpoint_comprehensive(db, store_id, total_processed)

                return {
                    'entity_type': 'products',
                    'processed_count': total_processed,
                    'error_count': errors_in_sync,
                    'status': 'completed',
                    'iterations_completed': iteration - 1,
                    'statistics': self.sync_stats.copy()
                }

        except Exception as e:
            db.rollback()
            self.track_error('sync_failed', str(e))
            self.finalize_sync_statistics(db, store_id)
            logger.error(f"❌ Error in products sync: {e}", exc_info=True)
            return {
                'entity_type': 'products',
                'processed_count': total_processed,
                'error_count': errors_in_sync + 1,
                'status': 'failed',
                'error': str(e),
                'statistics': self.sync_stats.copy()
            }

    def get_select_query(self, store, last_sync_time: datetime, offset: int, batch_size: int) -> str:
        """Generate the SELECT query for fetching products from Airbyte."""
        return """
        SELECT
            id::text as external_id,
            title as title,
            body_html as description,
            vendor as vendor,
            product_type as product_type,
            tags as tags,
            status as status,
            (published_at IS NOT NULL)::boolean as published,
            :store_id as store_id,
            COALESCE(created_at, _airbyte_extracted_at) as created_at,
            COALESCE(updated_at, _airbyte_extracted_at) as updated_at,
            handle as handle,
            row_to_json(products.*)::text as full_json,
            published_at as published_at,
            options::text as options,
            seo::text as seo,
            NULL as metafields,
            NULL as collections,
            featured_media::text as featured_media,
            COALESCE(updated_at, _airbyte_extracted_at) as source_updated_at
        FROM products
        WHERE _airbyte_extracted_at > :last_sync_time
          AND id IS NOT NULL
          AND title IS NOT NULL
          AND shop_url = :shop_url
        ORDER BY id::text
        LIMIT :batch_size OFFSET :offset
        """

    def get_insert_query(self) -> str:
        """Generate the INSERT query for bulk inserting products."""
        return """
        INSERT INTO products (
            external_id, title, description, vendor, product_type, tags,
            status, published, store_id, created_at, updated_at, handle,
            full_json, published_at, options, seo, metafields, collections,
            featured_media, source_updated_at
        ) VALUES %s
        ON CONFLICT (external_id) DO UPDATE SET
            title = EXCLUDED.title,
            description = EXCLUDED.description,
            vendor = EXCLUDED.vendor,
            product_type = EXCLUDED.product_type,
            tags = EXCLUDED.tags,
            status = EXCLUDED.status,
            published = EXCLUDED.published,
            handle = EXCLUDED.handle,
            full_json = EXCLUDED.full_json,
            published_at = EXCLUDED.published_at,
            options = EXCLUDED.options,
            seo = EXCLUDED.seo,
            metafields = EXCLUDED.metafields,
            collections = EXCLUDED.collections,
            featured_media = EXCLUDED.featured_media,
            updated_at = NOW(),
            source_updated_at = EXCLUDED.source_updated_at
        WHERE products.source_updated_at IS NULL
           OR EXCLUDED.source_updated_at > products.source_updated_at
        """

    def transform_row(self, row: tuple, store_id: int) -> Optional[tuple]:
        """
        Transform a row from Airbyte format to production format.

        Row structure from SELECT:
        0: external_id, 1: title, 2: description, 3: vendor, 4: product_type, 5: tags,
        6: status, 7: published, 8: store_id, 9: created_at, 10: updated_at, 11: handle,
        12: full_json, 13: published_at, 14: options, 15: seo, 16: metafields, 17: collections,
        18: featured_media, 19: source_updated_at
        """
        # All transformations are done in the SQL query, so just return the row
        return row
