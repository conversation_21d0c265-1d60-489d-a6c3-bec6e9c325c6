"""
Shopify Image Syncer - Handles synchronization of Shopify product images.
"""

import logging
from datetime import datetime
from typing import Dict, Any, Optional, List, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import text as sql_text

from ....base.base_syncer import BaseSyncer

logger = logging.getLogger(__name__)


class ProductImagesSyncer(BaseSyncer):
    """
    Syncer for Shopify product images.

    Handles the synchronization of product image data from Airbyte to the production database.
    """

    def __init__(self, airbyte_engine=None, platform_config=None):
        super().__init__(airbyte_engine, platform_config)
        self.entity_type = 'product_images'

    def sync(self, db: Session, store_id: int) -> Dict[str, Any]:
        """
        Sync product images for a specific store.
        """
        total_processed = 0
        batch_size = self.get_batch_size()
        last_sync_time = self.get_last_sync_time(db, store_id)

        store = self.get_store_info(db, store_id)
        shop_url = self.get_shop_url(store)

        logger.info(f"🚀 Starting images sync for store {store_id} (shop_url: {shop_url})")

        try:
            airbyte_engine = self.airbyte_engine or self.create_airbyte_engine()
            with airbyte_engine.connect() as airbyte_conn:
                # Check total count for this store
                test_query = airbyte_conn.execute(sql_text("SELECT COUNT(*) FROM product_images WHERE shop_url = :shop_url"), {'shop_url': shop_url})
                total_count = test_query.scalar()
                logger.info(f"Debug: Found {total_count} records in Airbyte product_images table for shop_url: {shop_url}")

                # Process data in batches until no more data
                offset = 0
                iteration = 1

                while True:
                    logger.info(f"Iteration {iteration}: Fetching data from Airbyte product_images table")

                    select_sql = self.get_select_query(store, last_sync_time, offset, batch_size)
                    airbyte_result = airbyte_conn.execute(sql_text(select_sql), {
                        'last_sync_time': last_sync_time,
                        'batch_size': batch_size,
                        'offset': offset,
                        'shop_url': shop_url
                    })
                    rows = airbyte_result.fetchall()

                    if not rows:
                        logger.info(f"🎉 Iteration {iteration}: No more records to sync!")
                        break

                    logger.info(f"Iteration {iteration}: Fetched {len(rows)} records from Airbyte")

                    # Get product and variant ID mappings from production database
                    product_mappings = {}
                    variant_mappings = {}

                    product_external_ids = [row[1] for row in rows if row[1]]  # product_external_id is at index 1
                    variant_external_ids = [row[2] for row in rows if row[2]]  # variant_external_id is at index 2

                    if product_external_ids:
                        unique_product_ids = list(set(product_external_ids))
                        placeholders = ','.join([':param' + str(i) for i in range(len(unique_product_ids))])
                        mapping_query = f"""
                        SELECT external_id, id FROM products
                        WHERE external_id IN ({placeholders}) AND store_id = :store_id
                        """
                        params = {f'param{i}': pid for i, pid in enumerate(unique_product_ids)}
                        params['store_id'] = store_id
                        mapping_result = db.execute(sql_text(mapping_query), params)
                        product_mappings = {row[0]: row[1] for row in mapping_result.fetchall()}

                    if variant_external_ids:
                        unique_variant_ids = list(set(variant_external_ids))
                        placeholders = ','.join([':param' + str(i) for i in range(len(unique_variant_ids))])
                        mapping_query = f"""
                        SELECT external_id, id FROM product_variants
                        WHERE external_id IN ({placeholders})
                        """
                        params = {f'param{i}': vid for i, vid in enumerate(unique_variant_ids)}
                        mapping_result = db.execute(sql_text(mapping_query), params)
                        variant_mappings = {row[0]: row[1] for row in mapping_result.fetchall()}

                    # Transform and insert data
                    insert_data = []
                    for row in rows:
                        transformed_row = self.transform_row(row, store_id, product_mappings, variant_mappings)
                        if transformed_row:
                            insert_data.append(transformed_row)

                    if insert_data:
                        insert_sql = self.get_insert_query()
                        from psycopg2.extras import execute_values
                        with db.connection().connection.cursor() as cursor:
                            execute_values(cursor, insert_sql, insert_data)
                            batch_processed = cursor.rowcount

                        db.commit()
                        total_processed += batch_processed
                        logger.info(f"Iteration {iteration}: Successfully inserted {batch_processed} product_images (total: {total_processed})")

                    # Move to next batch
                    offset += batch_size
                    iteration += 1

                logger.info(f"🏁 IMAGES SYNC COMPLETE!")
                logger.info(f"   Total iterations: {iteration - 1}")
                logger.info(f"   Total records processed: {total_processed}")

                # Update sync checkpoint
                self.update_sync_checkpoint(db, store_id, total_processed)

                return {
                    'entity_type': 'product_images',
                    'processed_count': total_processed,
                    'error_count': 0,
                    'status': 'completed',
                    'iterations_completed': iteration - 1
                }

        except Exception as e:
            db.rollback()
            logger.error(f"❌ Error in images sync: {e}", exc_info=True)
            return {
                'entity_type': 'product_images',
                'processed_count': total_processed,
                'error_count': 1,
                'status': 'failed',
                'error': str(e)
            }

    def get_select_query(self, store, last_sync_time: datetime, offset: int, batch_size: int) -> str:
        """Generate the SELECT query for fetching images from Airbyte."""
        return """
        SELECT
            CONCAT(id::text, '_', ROW_NUMBER() OVER (PARTITION BY id ORDER BY _airbyte_extracted_at)) as external_id,
            product_id::text as product_external_id,
            CASE
                WHEN variant_ids IS NOT NULL AND jsonb_array_length(variant_ids) > 0
                THEN (variant_ids->>0)::text
                ELSE NULL
            END as variant_external_id,
            src as src,
            alt as alt,
            width::integer as width,
            height::integer as height,
            COALESCE(position, 1) as position,
            row_to_json(product_images.*)::text as full_json,
            COALESCE(updated_at, _airbyte_extracted_at) as source_updated_at
        FROM product_images
        WHERE _airbyte_extracted_at > :last_sync_time
          AND id IS NOT NULL
          AND product_id IS NOT NULL
          AND shop_url = :shop_url
        ORDER BY id::text, _airbyte_extracted_at
        LIMIT :batch_size OFFSET :offset
        """

    def get_insert_query(self) -> str:
        """Generate the INSERT query for bulk inserting images."""
        return """
        INSERT INTO product_images (
            external_id, product_id, variant_id, src, alt, width, height,
            position, full_json, source_updated_at
        ) VALUES %s
        ON CONFLICT (external_id) DO UPDATE SET
            src = EXCLUDED.src,
            alt = EXCLUDED.alt,
            width = EXCLUDED.width,
            height = EXCLUDED.height,
            position = EXCLUDED.position,
            full_json = EXCLUDED.full_json,
            updated_at = NOW(),
            source_updated_at = EXCLUDED.source_updated_at
        WHERE product_images.source_updated_at IS NULL
            OR EXCLUDED.source_updated_at > product_images.source_updated_at
        """

    def transform_row(self, row: tuple, store_id: int, product_mappings: Dict[str, int], variant_mappings: Dict[str, int]) -> Optional[tuple]:
        """
        Transform a row from Airbyte format to production format.

        Row structure from SELECT:
        0: external_id, 1: product_external_id, 2: variant_external_id, 3: src, 4: alt,
        5: width, 6: height, 7: position, 8: full_json, 9: source_updated_at
        """
        external_id, product_external_id, variant_external_id, *rest = row
        product_id = product_mappings.get(product_external_id)
        variant_id = variant_mappings.get(variant_external_id) if variant_external_id else None

        if product_id:
            # Insert product_id and variant_id at the beginning
            return (external_id, product_id, variant_id) + tuple(rest)
        else:
            # Skip images without valid product IDs
            logger.warning(f"Skipping image {external_id} - no valid product_id found for product_external_id {product_external_id}")
            return None