"""
Shopify Metafield Product Syncer - Handles synchronization of Shopify product metafields.
"""

import logging
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import text as sql_text
import json

from ....base.base_syncer import BaseSyncer
from modules.sync.models import SyncCheckpoint

logger = logging.getLogger(__name__)


class MetafieldProductsSyncer(BaseSyncer):
    """
    Syncer for Shopify product metafields.

    Aggregates metafields from metafield_products table and updates products.metafields column.
    """

    def __init__(self, airbyte_engine=None, platform_config=None):
        super().__init__(airbyte_engine, platform_config)
        self.entity_type = 'metafield_products'

    def get_destination_table(self) -> str:
        """Override to specify the correct destination table for statistics tracking.

        Metafield syncers write to products table, not metafield_products.
        """
        return 'products'

    def sync(self, db: Session, store_id: int) -> Dict[str, Any]:
        """
        Sync product metafields for a specific store.
        """
        total_processed = 0
        batch_size = self.get_batch_size()
        # Temporarily disable checkpoint logic to avoid circular import issues
        last_sync_time = datetime(2020, 1, 1, tzinfo=timezone.utc)

        store = self.get_store_info(db, store_id)
        shop_url = self.get_shop_url(store)

        logger.info(f"🚀 Starting product metafields sync for store {store_id} (shop_url: {shop_url})")

        try:
            airbyte_engine = self.airbyte_engine or self.create_airbyte_engine()
            with airbyte_engine.connect() as airbyte_conn:
                # Check total count for this store
                test_query = airbyte_conn.execute(sql_text("SELECT COUNT(*) FROM metafield_products"))
                total_count = test_query.scalar()
                logger.info(f"Debug: Found {total_count} metafield records in Airbyte metafield_products table")

                # Process data in batches until no more data
                offset = 0
                iteration = 1

                while True:
                    logger.info(f"Iteration {iteration}: Fetching metafield data from Airbyte")

                    select_sql = self.get_select_query(store, last_sync_time, offset, batch_size)
                    airbyte_result = airbyte_conn.execute(sql_text(select_sql), {
                        'last_sync_time': last_sync_time,
                        'batch_size': batch_size,
                        'offset': offset,
                        'store_id': store_id
                    })
                    rows = airbyte_result.fetchall()

                    if not rows:
                        logger.info(f"🎉 Iteration {iteration}: No more metafield records to sync!")
                        break

                    logger.info(f"Iteration {iteration}: Fetched {len(rows)} metafield records from Airbyte")

                    # Debug: Log first few rows to understand data structure
                    if rows:
                        logger.info(f"Sample metafield row: {rows[0]}")
                        logger.info(f"Owner IDs in this batch: {[row[0] for row in rows[:5]]}")

                    # Filter metafields to only include products that exist in this store
                    filtered_metafields = self.filter_metafields_by_store(db, rows, store_id)
                    logger.info(f"Iteration {iteration}: After filtering by store {store_id}, {len(filtered_metafields)} metafield records remain")

                    # Aggregate metafields by owner_id and update products
                    metafields_by_owner = self.aggregate_metafields(filtered_metafields)

                    if metafields_by_owner:
                        update_count = self.update_products_metafields(db, metafields_by_owner, store_id)
                        total_processed += update_count
                        logger.info(f"Iteration {iteration}: Successfully updated {update_count} products with metafields (total: {total_processed})")

                    # Move to next batch
                    offset += batch_size
                    iteration += 1

                logger.info(f"🏁 PRODUCT METAFIELDS SYNC COMPLETE!")
                logger.info(f"   Total iterations: {iteration - 1}")
                logger.info(f"   Total products updated: {total_processed}")

                # Update sync checkpoint
                self.update_sync_checkpoint(db, store_id, total_processed)

                return {
                    'entity_type': 'metafield_products',
                    'processed_count': total_processed,
                    'error_count': 0,
                    'status': 'completed',
                    'iterations_completed': iteration - 1
                }

        except Exception as e:
            db.rollback()
            logger.error(f"❌ Error in product metafields sync: {e}", exc_info=True)
            return {
                'entity_type': 'metafield_products',
                'processed_count': total_processed,
                'error_count': 1,
                'status': 'failed',
                'error': str(e)
            }

    def get_select_query(self, store, last_sync_time: datetime, offset: int, batch_size: int) -> str:
        """Generate the SELECT query for fetching product metafields from Airbyte."""
        return """
        SELECT
            owner_id,
            key,
            value,
            namespace,
            type,
            COALESCE(updated_at, _airbyte_extracted_at) as updated_at
        FROM metafield_products
        WHERE _airbyte_extracted_at > :last_sync_time
          AND owner_id IS NOT NULL
          AND key IS NOT NULL
        ORDER BY owner_id, namespace, key
        LIMIT :batch_size OFFSET :offset
        """

    def aggregate_metafields(self, rows: List[Tuple]) -> Dict[str, Dict[str, Any]]:
        """
        Aggregate metafields by owner_id.

        Returns:
            Dict[owner_id, Dict[namespace, Dict[key, value]]]
        """
        metafields_by_owner = {}

        for row in rows:
            owner_id, key, value, namespace, field_type, updated_at = row

            if owner_id not in metafields_by_owner:
                metafields_by_owner[owner_id] = {}

            if namespace not in metafields_by_owner[owner_id]:
                metafields_by_owner[owner_id][namespace] = {}

            # Store the metafield with its metadata
            metafields_by_owner[owner_id][namespace][key] = {
                'value': value,
                'type': field_type,
                'updated_at': updated_at.isoformat() if updated_at else None
            }

        return metafields_by_owner

    def filter_metafields_by_store(self, db: Session, rows: List[Tuple], store_id: int) -> List[Tuple]:
        """
        Filter metafield rows to only include products that exist in the specified store.
        """
        if not rows:
            return []

        # Extract owner_ids from the rows
        owner_ids = [str(row[0]) for row in rows]  # owner_id is the first column

        # Query products table to find which owner_ids exist in this store
        placeholders = ', '.join(['%s'] * len(owner_ids))
        query = f"""
            SELECT external_id
            FROM products
            WHERE store_id = %s AND external_id IN ({placeholders})
        """

        try:
            result = db.execute(sql_text(query), [store_id] + owner_ids)
            existing_product_ids = {str(row[0]) for row in result.fetchall()}

            # Filter rows to only include products that exist in this store
            filtered_rows = [row for row in rows if str(row[0]) in existing_product_ids]

            logger.info(f"Filtered {len(rows)} metafield rows down to {len(filtered_rows)} for store {store_id}")
            return filtered_rows

        except Exception as e:
            logger.error(f"Error filtering metafields by store: {e}")
            return []

    def update_products_metafields(self, db: Session, metafields_by_owner: Dict[str, Dict[str, Any]], store_id: int) -> int:
        """
        Update products table with aggregated metafields.
        """
        update_count = 0

        for owner_id, metafields in metafields_by_owner.items():
            try:
                # Convert to JSON for storage
                metafields_json = json.dumps(metafields)

                # Update the product record
                update_sql = """
                UPDATE products
                SET metafields = :metafields,
                    updated_at = NOW()
                WHERE external_id = :external_id
                  AND store_id = :store_id
                """

                result = db.execute(sql_text(update_sql), {
                    'metafields': metafields_json,
                    'external_id': str(owner_id),  # Ensure owner_id is string to match VARCHAR column
                    'store_id': store_id
                })

                if result.rowcount > 0:
                    update_count += 1
                else:
                    logger.warning(f"No product found with external_id {owner_id} for store {store_id}")

            except Exception as e:
                logger.error(f"Error updating metafields for product {owner_id}: {e}")
                # Rollback immediately to clear the aborted transaction state
                db.rollback()
                continue

        # Commit all changes at once
        try:
            db.commit()
        except Exception as e:
            logger.error(f"Error committing metafield updates: {e}")
            db.rollback()
            return 0

        return update_count

    def update_sync_checkpoint(self, db: Session, store_id: int, processed_count: int):
        """Update sync checkpoint after successful sync, using airbyte connection for counting."""
        from sqlalchemy import text as sql_text
        from datetime import datetime, timezone

        checkpoint = db.query(SyncCheckpoint).filter(
            SyncCheckpoint.store_id == store_id,
            SyncCheckpoint.entity_type == self.entity_type
        ).first()

        now = datetime.now(timezone.utc)

        # Use airbyte engine to get the actual count from the source table
        airbyte_engine = self.airbyte_engine or self.create_airbyte_engine()
        with airbyte_engine.connect() as airbyte_conn:
            total_count_query = sql_text(f"SELECT COUNT(*) FROM {self.entity_type}")
            total_count_result = airbyte_conn.execute(total_count_query)
            actual_total_count = total_count_result.scalar()

        if checkpoint:
            checkpoint.last_successful_sync_at = now
            checkpoint.last_sync_status = 'completed'
            checkpoint.updated_at = now
            checkpoint.total_records = actual_total_count
        else:
            checkpoint = SyncCheckpoint(
                store_id=store_id,
                entity_type=self.entity_type,
                last_successful_sync_at=now,
                total_records=actual_total_count,
                last_sync_status='completed',
                created_at=now,
                updated_at=now
            )
            db.add(checkpoint)

        # Record sync checkpoint update metric
        from core.metrics import sync_checkpoint_updates
        sync_checkpoint_updates.labels(
            store_domain="",  # We don't have store_domain here, could be enhanced
            entity_type=self.entity_type,
            stage="completed"
        ).inc()

        db.commit()
        logger.debug(f"Updated checkpoint for {self.entity_type}: {processed_count} processed, {checkpoint.total_records} total in airbyte table")