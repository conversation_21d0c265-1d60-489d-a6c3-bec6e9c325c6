"""
Shopify-specific processor for handling Shopify data synchronization.
"""

import logging
from typing import Dict, Any, Optional, List
from sqlalchemy.orm import Session

from ...base.base_processor import BaseProcessor

logger = logging.getLogger(__name__)


class ShopifyProcessor(BaseProcessor):
    """
    Processor for Shopify platform data synchronization.

    Handles Shopify-specific logic like:
    - Shop URL extraction
    - Entity type mapping
    - Shopify-specific data transformations
    """

    def __init__(self):
        super().__init__('shopify')

    def _get_supported_entity_types(self) -> List[str]:
        """Get Shopify-specific entity types."""
        entity_types = self.config.get('entity_types')
        if not entity_types:
            raise ValueError(f"No entity_types configured for platform shopify")
        return entity_types

    def _get_syncer_class(self, entity_type: str):
        """Get the appropriate syncer class for Shopify entity types."""
        from .syncers import (
            ProductsSyncer,
            ProductVariantsSyncer,
            ProductImagesSyncer,
            InventoryLevelsSyncer,
            MetafieldProductsSyncer,
            MetafieldProductVariantsSyncer,
            MetafieldProductImagesSyncer
        )

        syncer_map = {
            'products': ProductsSyncer,
            'product_variants': ProductVariantsSyncer,
            'product_images': ProductImagesSyncer,
            'inventory_levels': InventoryLevelsSyncer,
            'metafield_products': MetafieldProductsSyncer,
            'metafield_product_variants': MetafieldProductVariantsSyncer,
            'metafield_product_images': MetafieldProductImagesSyncer
        }

        return syncer_map.get(entity_type)

    def get_shop_url(self, store) -> str:
        """Extract shop URL from Shopify store domain."""
        if store.shop_domain and store.shop_domain.endswith('.myshopify.com'):
            return store.shop_domain.replace('.myshopify.com', '')
        return store.shop_name or f"store-{store.id}"

    def validate_store(self, store) -> bool:
        """Validate that store is a Shopify store."""
        return store.platform == 'shopify' and store.is_active