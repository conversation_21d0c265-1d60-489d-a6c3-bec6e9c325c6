"""
Bulk sync processor factory - Creates platform-specific processors for data synchronization.

This provides a clean, modular architecture for handling different e-commerce platforms
with their specific sync requirements and data transformations.
"""

import logging
from typing import Dict, Any, Optional, List
from sqlalchemy.orm import Session

from modules.stores.models import Store

logger = logging.getLogger(__name__)


class BulkSyncProcessor:
    """
    Factory class for creating platform-specific sync processors.

    This replaces the monolithic sync processor with a modular architecture that:
    - Supports multiple e-commerce platforms (Shopify, WooCommerce, etc.)
    - Provides platform-specific data transformations
    - Enables easy addition of new platforms and entity types
    - Maintains clean separation of concerns
    """

    def __init__(self, airbyte_engine=None):
        self.airbyte_engine = airbyte_engine

    def sync_all_entities(
        self,
        db: Session,
        store_id: Optional[int] = None,
        entity_types: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Sync all specified entity types for a store using the appropriate platform processor.

        Args:
            db: Database session
            store_id: Store ID to sync (optional - if None, syncs all stores found in data)
            entity_types: List of entity types to sync (optional)

        Returns:
            Sync results summary
        """
        # Get store information to determine platform
        if store_id:
            store = db.query(Store).filter(Store.id == store_id).first()
            if not store:
                raise ValueError(f"Store {store_id} not found")
        else:
            # For auto-discovery, we'll need to determine platform from data
            # For now, default to Shopify
            store = None

        # Create platform-specific processor
        platform = store.platform if store else 'shopify'
        processor = self._create_platform_processor(platform, self.airbyte_engine)

        # Delegate to platform-specific processor
        return processor.sync_all_entities(db, store_id, entity_types)

    def _create_platform_processor(self, platform: str, airbyte_engine=None):
        """Create the appropriate platform-specific processor."""
        if platform == 'shopify':
            from .platforms.shopify.shopify_processor import ShopifyProcessor
            return ShopifyProcessor()
        else:
            raise ValueError(f"Unsupported platform: {platform}")

    # Legacy method for backward compatibility
    def sync_store(self, db: Session, store_id: int, entity_types: Optional[List[str]] = None) -> Dict[str, Any]:
        """Legacy method - use sync_all_entities instead."""
        return self.sync_all_entities(db, store_id, entity_types)