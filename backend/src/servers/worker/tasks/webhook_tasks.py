"""
Webhook-related Celery tasks.

This module contains all tasks related to processing webhooks from
external platforms like Shopify.
"""

import logging
from typing import Dict, Any, Optional
from datetime import datetime, timezone

from servers.worker.celery_app import celery_app
from servers.worker.base_task import LoggedTask
from core.metrics import webhook_sync_triggers, sync_triggered_total, sync_completed_total

logger = logging.getLogger(__name__)


@celery_app.task(name='webhook.process_webhook_event', base=LoggedTask)
def process_webhook_event(webhook_event_id: int) -> Dict[str, Any]:
    """
    Process a webhook event and trigger appropriate sync operations.
    
    Args:
        webhook_event_id: ID of the webhook event to process
        
    Returns:
        Processing results
    """
    from core.db.database import SessionLocal
    from modules.sync.models import WebhookEvent
    from modules.sync.airbyte_service import AirbyteService
    
    db = SessionLocal()
    
    try:
        # Get webhook event
        webhook_event = db.query(WebhookEvent).filter(
            WebhookEvent.id == webhook_event_id
        ).first()
        
        if not webhook_event:
            logger.error(f"Webhook event {webhook_event_id} not found")
            return {'status': 'failed', 'error': 'Webhook event not found'}
        
        logger.info(f"Processing webhook event {webhook_event_id}: {webhook_event.topic}")

        # Map webhook topic to entity type
        entity_type = _map_webhook_to_entity_type(webhook_event.topic)

        if not entity_type:
            logger.warning(f"No entity type mapping for webhook topic: {webhook_event.topic}")
            webhook_event.status = 'completed'
            webhook_event.completed_at = datetime.now(timezone.utc)
            db.commit()
            return {
                'status': 'skipped',
                'reason': 'no_entity_mapping',
                'topic': webhook_event.topic
            }

        # Check if sync should be triggered
        if _should_trigger_sync(webhook_event, entity_type, db):
            # Record webhook sync trigger metric
            webhook_sync_triggers.labels(
                shop_domain=webhook_event.shop_domain,
                topic=webhook_event.topic,
                entity_type=entity_type
            ).inc()

            # Trigger Airbyte sync directly
            airbyte_service = AirbyteService()

            # Get store to find connection ID
            from modules.stores.models import Store
            store = db.query(Store).filter(Store.id == webhook_event.store_id).first()

            if not store or not store.airbyte_connection_id:
                logger.warning(f"No Airbyte connection found for store {webhook_event.store_id}")
                result = {
                    'status': 'sync_not_needed',
                    'reason': 'no_airbyte_connection',
                    'entity_type': entity_type,
                    'webhook_event_id': webhook_event_id
                }
            else:
                # Record sync triggered metric
                sync_triggered_total.labels(
                    trigger_type='webhook',
                    entity_type=entity_type,
                    store_domain=store.shop_domain
                ).inc()

                # Trigger Airbyte sync
                import asyncio
                job_id = asyncio.run(airbyte_service.trigger_sync(store.airbyte_connection_id, store.id))

                if job_id:
                    logger.info(f"Triggered Airbyte sync job {job_id} for webhook {webhook_event_id}")
                    result = {
                        'status': 'sync_triggered',
                        'airbyte_job_id': job_id,
                        'entity_type': entity_type,
                        'webhook_event_id': webhook_event_id
                    }
                else:
                    logger.warning(f"Failed to trigger Airbyte sync for webhook {webhook_event_id}")
                    # Record failed sync
                    sync_completed_total.labels(
                        trigger_type='webhook',
                        entity_type=entity_type,
                        store_domain=store.shop_domain,
                        status='failed'
                    ).inc()
                    result = {
                        'status': 'sync_failed',
                        'reason': 'airbyte_sync_failed',
                        'entity_type': entity_type,
                        'webhook_event_id': webhook_event_id
                    }
        else:
            logger.info(f"Sync not needed for webhook {webhook_event_id}")
            result = {
                'status': 'sync_not_needed',
                'entity_type': entity_type,
                'webhook_event_id': webhook_event_id
            }
        
        # Mark webhook as processed
        webhook_event.status = 'completed'
        webhook_event.completed_at = datetime.now(timezone.utc)
        db.commit()
        
        return result
        
    except Exception as e:
        db.rollback()
        logger.error(f"Error processing webhook event {webhook_event_id}: {e}", exc_info=True)
        raise
    finally:
        db.close()


@celery_app.task(name='webhook.cleanup_old_webhook_events', base=LoggedTask)
def cleanup_old_webhook_events(days_old: int = 7):
    """
    Clean up old processed webhook events to prevent database bloat.
    
    Args:
        days_old: Number of days old processed events to keep
    """
    from core.db.database import SessionLocal
    from modules.sync.models import WebhookEvent
    from datetime import datetime, timezone, timedelta
    
    db = SessionLocal()
    
    try:
        cutoff_date = datetime.now(timezone.utc) - timedelta(days=days_old)
        
        # Delete old processed webhook events
        deleted_count = db.query(WebhookEvent).filter(
            WebhookEvent.completed_at < cutoff_date,
            WebhookEvent.status == 'completed'
        ).delete()
        
        db.commit()
        
        logger.info(f"Cleaned up {deleted_count} old webhook event records")
        return {'deleted_count': deleted_count}
        
    except Exception as e:
        db.rollback()
        logger.error(f"Error cleaning up old webhook events: {e}", exc_info=True)
        raise
    finally:
        db.close()


@celery_app.task(name='webhook.retry_failed_webhooks', base=LoggedTask)
def retry_failed_webhooks(max_retries: int = 3):
    """
    Retry processing of failed webhook events.
    
    Args:
        max_retries: Maximum number of retry attempts
    """
    from core.db.database import SessionLocal
    from modules.sync.models import WebhookEvent
    from datetime import datetime, timezone, timedelta
    
    db = SessionLocal()
    
    try:
        # Find unprocessed webhook events older than 5 minutes
        cutoff_time = datetime.now(timezone.utc) - timedelta(minutes=5)
        
        failed_webhooks = db.query(WebhookEvent).filter(
            WebhookEvent.status != 'completed',
            WebhookEvent.created_at < cutoff_time
        ).limit(100).all()  # Process in batches
        
        retry_count = 0
        
        for webhook_event in failed_webhooks:
            try:
                # Trigger processing
                process_webhook_event.delay(webhook_event.id)
                retry_count += 1
                
            except Exception as e:
                logger.error(f"Error retrying webhook {webhook_event.id}: {e}")
                continue
        
        logger.info(f"Triggered retry for {retry_count} failed webhook events")
        return {'retried_count': retry_count}
        
    except Exception as e:
        logger.error(f"Error in retry failed webhooks: {e}", exc_info=True)
        raise
    finally:
        db.close()




def _map_webhook_to_entity_type(topic: str) -> Optional[str]:
    """
    Map Shopify webhook topic to entity type for sync.

    Args:
        topic: Shopify webhook topic

    Returns:
        Entity type string or None if no mapping exists
    """
    # Shopify webhook topic to entity type mapping
    topic_mapping = {
        'products/create': 'products',
        'products/update': 'products',
        'products/delete': 'products',
        'product_variants/create': 'product_variants',
        'product_variants/update': 'product_variants',
        'product_variants/delete': 'product_variants',
        'product_images/create': 'product_images',
        'product_images/update': 'product_images',
        'product_images/delete': 'product_images',
        'inventory_levels/update': 'inventory_levels',
        'inventory_levels/connect': 'inventory_levels',
        'inventory_levels/disconnect': 'inventory_levels',
    }

    return topic_mapping.get(topic)


def _should_trigger_sync(webhook_event, entity_type: str, db) -> bool:
    """
    Determine if a sync should be triggered based on webhook event and recent sync history.

    Args:
        webhook_event: WebhookEvent instance
        entity_type: Mapped entity type
        db: Database session

    Returns:
        True if sync should be triggered, False otherwise
    """
    from datetime import datetime, timezone, timedelta
    from modules.sync.models import SyncJob

    # Always trigger sync for inventory updates (critical for stock levels)
    if entity_type == 'inventory_levels':
        return True

    # For other entity types, check if there's been a recent sync
    # Only sync if no sync has been performed in the last 5 minutes for this entity type
    five_minutes_ago = datetime.now(timezone.utc) - timedelta(minutes=5)

    recent_sync = db.query(SyncJob).filter(
        SyncJob.store_id == webhook_event.store_id,
        SyncJob.entity_type == entity_type,  # Check if entity_type matches
        SyncJob.created_at >= five_minutes_ago,
        SyncJob.status.in_(['completed', 'running'])
    ).first()

    if recent_sync:
        logger.info(f"Recent sync found for {entity_type} on store {webhook_event.store_id}, skipping webhook-triggered sync")
        return False

    return True
