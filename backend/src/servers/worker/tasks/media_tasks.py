"""
Media generation and processing Celery tasks.

This module contains all tasks related to generating and processing
media content like videos and images.
"""

import logging
from typing import Dict, Any, Optional, List

from servers.worker.celery_app import celery_app
from servers.worker.base_task import LoggedTask

logger = logging.getLogger(__name__)




@celery_app.task(name='media.generate_media', bind=True, base=LoggedTask, max_retries=3)
def generate_media(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Generate media using the full payload from the request.

    This task handles the complete media generation workflow including:
    - Processing the full payload with mode, model, settings, items
    - Using appropriate providers based on the model specified
    - Handling reference images and custom prompts
    - Creating variants and updating job status

    Args:
        task_data: Complete task data including full payload

    Returns:
        Generation results
    """
    from core.db.database import SessionLocal
    from sqlalchemy import select, update
    from modules.media.models import MediaJob, MediaVariant, MediaJobStatus, MediaVariantStatus
    from modules.media.service import media_service
    from datetime import datetime

    db = SessionLocal()

    try:
        job_id = task_data.get("job_id")
        user_id = task_data.get("user_id")
        full_payload = task_data.get("full_payload")

        if not job_id:
            raise ValueError("Job ID is required")

        # Get the job from database
        job_result = db.execute(select(MediaJob).filter(MediaJob.id == job_id))
        job = job_result.scalar_one_or_none()

        if not job:
            raise ValueError(f"Job {job_id} not found")

        # Update job status to processing
        job.status = MediaJobStatus.PROCESSING
        job.started_at = datetime.now()
        db.commit()

        logger.info(f"Starting media generation for job {job_id} with payload: {full_payload}")

        # Extract generation parameters from full payload
        media_type = full_payload.get("media_type", "image")
        mode = full_payload.get("mode", media_type)
        model = full_payload.get("model", "banana")
        settings = full_payload.get("settings", {})
        items = full_payload.get("items", [])

        # Process each product item
        results = []
        for item in items:
            product_id = str(item.get("productId"))
            prompt = item.get("prompt", "")
            reference_images = item.get("referenceImageUrls", [])

            logger.info(f"Processing product {product_id} with {len(reference_images)} reference images")

            # Create media generation request
            from modules.media.schemas import MediaGenerationRequest
            media_request = MediaGenerationRequest(
                product_title=product_id,  # Use product ID as title for now
                media_type=media_type,
                custom_config={
                    "mode": mode,
                    "model": model,
                    "settings": settings,
                    "prompt": prompt,
                    "reference_images": reference_images,
                    "aspect_ratio": settings.get("aspectRatio", "1:1"),
                    "guidance": settings.get("guidance", 7.5),
                    "steps": settings.get("steps", 25),
                    "seed": settings.get("seed"),
                    "upscale": settings.get("upscale", True),
                    "safety": settings.get("safety", True)
                },
                num_images=4,
                variants_count=4,
                aspect_ratio=settings.get("aspectRatio", "1:1"),
                style="professional",
                model=model,
                settings=settings
            )

            # Generate media using the provider system
            result = media_service.generate_media_with_provider(model, media_request)

            if result.success:
                # Update variants with results
                variants = db.execute(
                    select(MediaVariant).filter(MediaVariant.job_id == job.id)
                ).scalars().all()

                # For now, just mark all variants as completed
                # In a full implementation, you'd process each variant differently
                for variant in variants:
                    variant.status = MediaVariantStatus.READY
                    variant.image_url = result.images[0].get("url") if result.images else None
                    variant.updated_at = datetime.now()

                results.append({
                    "product_id": product_id,
                    "status": "success",
                    "media_urls": [img.get("url") for img in (result.images or [])]
                })
            else:
                # Mark variants as failed
                variants = db.execute(
                    select(MediaVariant).filter(MediaVariant.job_id == job.id)
                ).scalars().all()

                for variant in variants:
                    variant.status = MediaVariantStatus.FAILED
                    variant.error_message = result.error_message
                    variant.updated_at = datetime.now()

                results.append({
                    "product_id": product_id,
                    "status": "failed",
                    "error": result.error_message
                })

        # Update job status
        job.status = MediaJobStatus.COMPLETED
        job.completed_at = datetime.now()
        db.commit()

        logger.info(f"Media generation completed for job {job_id}")
        return {
            "job_id": job_id,
            "status": "completed",
            "results": results,
            "total_products": len(items),
            "successful_products": len([r for r in results if r["status"] == "success"])
        }

    except Exception as e:
        logger.error(f"Media generation failed for job {task_data.get('job_id')}: {e}", exc_info=True)

        # Update job status to failed
        try:
            if 'job' in locals() and job:
                job.status = MediaJobStatus.FAILED
                job.error_message = str(e)
                job.completed_at = datetime.now()
                db.commit()
        except Exception as db_error:
            logger.error(f"Failed to update job status: {db_error}")

        # Retry with exponential backoff
        if self.request.retries < self.max_retries:
            countdown = 2 ** self.request.retries * 60  # 1min, 2min, 4min
            logger.info(f"Retrying media generation for job {task_data.get('job_id')} in {countdown}s")
            raise self.retry(countdown=countdown, exc=e)

        raise

    finally:
        db.close()


@celery_app.task(name='media.cleanup_old_media', base=LoggedTask)
def cleanup_old_media(days_old: int = 90):
    """
    Clean up old generated media files to save storage space.

    Args:
        days_old: Number of days old media to keep
    """
    from core.db.database import SessionLocal
    from datetime import datetime, timezone, timedelta
    import os

    db = SessionLocal()

    try:
        cutoff_date = datetime.now(timezone.utc) - timedelta(days=days_old)

        # This would typically query a media table and delete old files
        # For now, just log the operation
        logger.info(f"Would clean up media files older than {cutoff_date}")

        # TODO: Implement actual media cleanup logic
        # - Query media records older than cutoff_date
        # - Delete files from storage
        # - Remove database records

        return {'status': 'completed', 'message': 'Media cleanup not yet implemented'}

    except Exception as e:
        logger.error(f"Error cleaning up old media: {e}", exc_info=True)
        raise
    finally:
        db.close()


