"""
Comprehensive tests for the media generation pipeline.
Tests schema validation, provider integration, task orchestration, and quality assurance.
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime
import json

from modules.media.schemas import (
    ProductSchema, 
    MediaGenerationRequest, 
    MediaGenerationResult,
    GeneratedAsset,
    EXAMPLE_PRODUCTS
)
from modules.media.text_pipeline import text_pipeline
from modules.media.image_pipeline import image_pipeline
from modules.media.video_pipeline import video_pipeline
from modules.media.template_manager import template_manager
from modules.media.qa_pipeline import qa_pipeline
from modules.media.enhanced_provider_management import enhanced_provider_manager
from modules.media.providers_config import get_best_provider_for_request


class TestSchemaValidation:
    """Test schema validation and data structures."""
    
    def test_product_schema_validation(self):
        """Test ProductSchema validation with example data."""
        # Test with valid example product
        sneaker_product = EXAMPLE_PRODUCTS[0]  # sneaker_001
        assert sneaker_product.product_id == "sneaker_001"
        assert sneaker_product.title == "Urban Runner Pro"
        assert sneaker_product.category == "apparel"
        assert len(sneaker_product.colors) > 0
        assert len(sneaker_product.materials) > 0
    
    def test_media_generation_request_validation(self):
        """Test MediaGenerationRequest validation."""
        product = EXAMPLE_PRODUCTS[0]
        
        request = MediaGenerationRequest(
            product_data=product,
            media_type="text",
            custom_config={"content_types": ["seo_title", "description"]}
        )
        
        assert request.product_data.product_id == "sneaker_001"
        assert request.media_type == "text"
        assert request.custom_config["content_types"] == ["seo_title", "description"]
    
    def test_generated_asset_validation(self):
        """Test GeneratedAsset validation."""
        asset = GeneratedAsset(
            asset_id="test_asset_001",
            asset_type="text",
            content="Test content for validation",
            metadata={"test": "metadata"}
        )
        
        assert asset.asset_id == "test_asset_001"
        assert asset.asset_type == "text"
        assert asset.content == "Test content for validation"
        assert asset.metadata["test"] == "metadata"


class TestTemplateManager:
    """Test template management system."""
    
    def test_template_loading(self):
        """Test template loading from templates.json."""
        # Test that templates are loaded
        assert hasattr(template_manager, 'templates')
        assert isinstance(template_manager.templates, dict)
    
    def test_template_rendering(self):
        """Test template rendering with product data."""
        product = EXAMPLE_PRODUCTS[0]  # sneaker_001
        
        # Test text template rendering
        try:
            result = template_manager.render_template("text/seo/meta_title", product)
            assert "content" in result
            assert isinstance(result["content"], str)
            assert len(result["content"]) > 0
        except Exception as e:
            # Template might not exist, which is acceptable for testing
            pytest.skip(f"Template not found: {e}")
    
    def test_template_validation(self):
        """Test template constraint validation."""
        # Test with mock template result
        template_result = {
            "content": "Test content that is exactly fifty characters long!",
            "constraints": {
                "max_length": 100,
                "min_length": 10,
                "required_elements": ["test"]
            }
        }
        
        # This would test constraint validation if implemented
        assert len(template_result["content"]) <= template_result["constraints"]["max_length"]
        assert len(template_result["content"]) >= template_result["constraints"]["min_length"]


class TestTextPipeline:
    """Test text generation pipeline."""
    
    @pytest.mark.asyncio
    async def test_text_generation_with_mock_provider(self):
        """Test text generation with mocked provider."""
        product = EXAMPLE_PRODUCTS[0]
        
        # Mock the text provider
        with patch('modules.media.text_pipeline.text_pipeline._get_text_provider') as mock_provider_getter:
            mock_provider = AsyncMock()
            mock_provider.generate_media.return_value = Mock(
                success=True,
                content="Generated SEO title for Urban Runner Pro"
            )
            mock_provider_getter.return_value = mock_provider
            
            # Mock QA pipeline
            with patch('modules.media.text_pipeline.qa_pipeline.assess_content_quality') as mock_qa:
                mock_qa.return_value = Mock(
                    overall_score=0.9,
                    quality_level=Mock(value="excellent"),
                    issues=[],
                    recommendations=[],
                    needs_human_review=False,
                    auto_approve=True,
                    review_priority=Mock(value="none")
                )
                
                result = await text_pipeline.generate_product_text(
                    product_data=product,
                    content_types=["seo_title"],
                    custom_config={}
                )
                
                assert result.success
                assert len(result.assets) > 0
                assert result.assets[0].asset_type == "text"
    
    @pytest.mark.asyncio
    async def test_text_validation(self):
        """Test text content validation."""
        product = EXAMPLE_PRODUCTS[0]
        
        # Test validation with sample content
        validation_result = text_pipeline._validate_content(
            content="Urban Runner Pro - Premium Athletic Sneaker",
            content_type="seo_title",
            product_data=product
        )
        
        assert hasattr(validation_result, 'passed')
        assert hasattr(validation_result, 'issues')


class TestImagePipeline:
    """Test image generation pipeline."""
    
    @pytest.mark.asyncio
    async def test_image_generation_with_mock_provider(self):
        """Test image generation with mocked provider."""
        product = EXAMPLE_PRODUCTS[0]
        
        # Mock the image provider
        with patch('modules.media.image_pipeline.image_pipeline._get_image_provider') as mock_provider_getter:
            mock_provider = AsyncMock()
            mock_provider.generate_media.return_value = Mock(
                success=True,
                assets=[Mock(
                    url="https://example.com/test-image.jpg",
                    metadata={"width": 1024, "height": 1024}
                )]
            )
            mock_provider_getter.return_value = mock_provider
            
            # Mock image download and processing
            with patch('modules.media.image_pipeline.image_pipeline._download_image') as mock_download:
                mock_download.return_value = b"fake_image_data"
                
                with patch('modules.media.image_pipeline.image_pipeline._process_single_image') as mock_process:
                    mock_process.return_value = [{
                        "image": Mock(width=800, height=800),
                        "type": "hero_studio",
                        "background": "white",
                        "size": "medium",
                        "metadata": {"processed": True}
                    }]
                    
                    with patch('modules.media.image_pipeline.get_enhanced_media_storage') as mock_storage:
                        mock_storage_instance = Mock()
                        mock_storage_instance.store_generated_asset.return_value = Mock(
                            public_url="https://cdn.example.com/stored-image.webp",
                            storage_path="/path/to/image.webp",
                            metadata={"stored": True}
                        )
                        mock_storage.return_value = mock_storage_instance
                        
                        result = await image_pipeline.generate_product_images(
                            product_data=product,
                            image_types=["hero_studio"],
                            custom_config={}
                        )
                        
                        assert result.success
                        assert len(result.assets) > 0
                        assert result.assets[0].asset_type == "image"


class TestVideoPipeline:
    """Test video generation pipeline."""
    
    @pytest.mark.asyncio
    async def test_video_generation_with_mock_provider(self):
        """Test video generation with mocked provider."""
        product = EXAMPLE_PRODUCTS[0]
        
        # Mock the video provider
        with patch('modules.media.video_pipeline.video_pipeline._get_video_provider') as mock_provider_getter:
            mock_provider = AsyncMock()
            mock_provider.generate_media.return_value = Mock(
                success=True,
                assets=[Mock(
                    url="https://example.com/test-video.mp4",
                    metadata={"duration": 15, "format": "mp4"}
                )]
            )
            mock_provider_getter.return_value = mock_provider
            
            result = await video_pipeline.generate_product_videos(
                product_data=product,
                video_types=["15_second_hero"],
                custom_config={}
            )
            
            # Video pipeline returns placeholder results for now
            assert result.success


class TestQualityAssurance:
    """Test quality assurance pipeline."""
    
    @pytest.mark.asyncio
    async def test_text_quality_assessment(self):
        """Test text content quality assessment."""
        product = EXAMPLE_PRODUCTS[0]
        
        asset = GeneratedAsset(
            asset_id="test_text_001",
            asset_type="text",
            content="Urban Runner Pro - Premium athletic sneaker with advanced cushioning technology",
            metadata={"content_type": "seo_title"}
        )
        
        assessment = await qa_pipeline.assess_content_quality(asset, product)
        
        assert hasattr(assessment, 'overall_score')
        assert hasattr(assessment, 'quality_level')
        assert hasattr(assessment, 'safety_score')
        assert hasattr(assessment, 'needs_human_review')
        assert 0.0 <= assessment.overall_score <= 1.0
    
    @pytest.mark.asyncio
    async def test_image_quality_assessment(self):
        """Test image content quality assessment."""
        product = EXAMPLE_PRODUCTS[0]
        
        asset = GeneratedAsset(
            asset_id="test_image_001",
            asset_type="image",
            url="https://example.com/test-image.jpg",
            metadata={"style": "hero_studio"},
            dimensions="1024x1024",
            format="jpg"
        )
        
        assessment = await qa_pipeline.assess_content_quality(asset, product)
        
        assert hasattr(assessment, 'overall_score')
        assert hasattr(assessment, 'technical_score')
        assert 0.0 <= assessment.overall_score <= 1.0


class TestProviderManagement:
    """Test enhanced provider management."""
    
    @pytest.mark.asyncio
    async def test_provider_selection(self):
        """Test provider selection based on availability and quotas."""
        # Test getting best provider for different media types
        text_provider = await get_best_provider_for_request("text", 0.01)
        image_provider = await get_best_provider_for_request("image", 0.05)
        
        # Should return provider names or None
        assert text_provider is None or isinstance(text_provider, str)
        assert image_provider is None or isinstance(image_provider, str)
    
    def test_provider_status_tracking(self):
        """Test provider status and metrics tracking."""
        # Get status for all providers
        status = enhanced_provider_manager.get_all_provider_status()
        
        assert isinstance(status, dict)
        
        # Check that each provider has required status fields
        for provider_name, provider_status in status.items():
            assert "provider_name" in provider_status
            assert "status" in provider_status
            assert "health" in provider_status
            assert "usage" in provider_status
            assert "costs" in provider_status
            assert "limits" in provider_status
    
    @pytest.mark.asyncio
    async def test_rate_limiting(self):
        """Test rate limiting functionality."""
        provider_name = "mock"
        
        # Test that we can check if requests are allowed
        can_make_request, reason = await enhanced_provider_manager.can_make_request(
            provider_name, estimated_cost=0.01
        )
        
        assert isinstance(can_make_request, bool)
        assert isinstance(reason, str)


class TestEndToEndWorkflow:
    """Test complete end-to-end workflows."""
    
    @pytest.mark.asyncio
    async def test_complete_text_generation_workflow(self):
        """Test complete text generation workflow from request to result."""
        product = EXAMPLE_PRODUCTS[0]
        
        # Mock all dependencies
        with patch('modules.media.text_pipeline.text_pipeline._get_text_provider') as mock_provider:
            mock_provider.return_value = AsyncMock()
            mock_provider.return_value.generate_media.return_value = Mock(
                success=True,
                content="Generated content"
            )
            
            with patch('modules.media.text_pipeline.qa_pipeline.assess_content_quality') as mock_qa:
                mock_qa.return_value = Mock(
                    overall_score=0.85,
                    quality_level=Mock(value="good"),
                    issues=[],
                    recommendations=[],
                    needs_human_review=False,
                    auto_approve=True,
                    review_priority=Mock(value="none")
                )
                
                # Run complete workflow
                result = await text_pipeline.generate_product_text(
                    product_data=product,
                    content_types=["seo_title", "description"],
                    custom_config={}
                )
                
                # Verify result structure
                assert result.success
                assert isinstance(result.assets, list)
                assert len(result.assets) > 0
                assert result.actual_processing_time > 0
                assert hasattr(result, 'validation_results')
    
    def test_example_products_completeness(self):
        """Test that example products have all required fields."""
        for product in EXAMPLE_PRODUCTS:
            # Check required fields
            assert product.product_id
            assert product.title
            assert product.category
            assert product.brand
            assert product.price > 0
            assert product.currency
            
            # Check that shop branding is properly configured
            assert hasattr(product.shop_branding, 'tone')
            assert hasattr(product.shop_branding, 'style')
            
            # Check that requirements are configured
            assert hasattr(product, 'image_requirements')
            assert hasattr(product, 'video_requirements')


# Pytest configuration
@pytest.fixture
def sample_product():
    """Fixture providing a sample product for testing."""
    return EXAMPLE_PRODUCTS[0]


@pytest.fixture
def sample_text_asset():
    """Fixture providing a sample text asset for testing."""
    return GeneratedAsset(
        asset_id="test_text_001",
        asset_type="text",
        content="Sample generated text content for testing",
        metadata={"content_type": "seo_title", "test": True}
    )


@pytest.fixture
def sample_image_asset():
    """Fixture providing a sample image asset for testing."""
    return GeneratedAsset(
        asset_id="test_image_001",
        asset_type="image",
        url="https://example.com/test-image.jpg",
        metadata={"style": "hero_studio", "test": True},
        dimensions="1024x1024",
        format="jpg",
        file_size=512000
    )


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
