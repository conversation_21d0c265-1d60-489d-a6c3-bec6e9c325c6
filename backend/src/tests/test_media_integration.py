"""
Integration tests for the media generation pipeline.
Tests the complete system integration including task orchestration and storage.
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
import json
import tempfile
import os

from servers.worker.tasks.media_tasks import generate_media_comprehensive
from modules.media.schemas import EXAMPLE_PRODUCTS
from modules.media.enhanced_storage import get_enhanced_media_storage
from modules.media.providers_config import get_provider_dashboard_data


class TestTaskOrchestration:
    """Test Celery task orchestration."""
    
    def test_task_input_validation(self):
        """Test task input validation."""
        from servers.worker.tasks.media_tasks import _validate_task_input
        
        # Test valid input
        valid_input = {
            "job_id": 123,
            "user_id": 456,
            "full_payload": {
                "mode": "text",
                "items": [{"productId": "test_001"}]
            }
        }
        
        result = _validate_task_input(valid_input)
        assert result["normalized_media_type"] == "text"
        assert len(result["validated_items"]) == 1
        
        # Test invalid input - missing job_id
        invalid_input = {
            "user_id": 456,
            "full_payload": {"items": []}
        }
        
        with pytest.raises(ValueError, match="Required field 'job_id' is missing"):
            _validate_task_input(invalid_input)
    
    def test_idempotency_key_generation(self):
        """Test idempotency key generation."""
        from servers.worker.tasks.media_tasks import _generate_idempotency_key
        
        task_data = {
            "job_id": 123,
            "user_id": 456,
            "full_payload": {"mode": "text", "items": [{"productId": "test"}]}
        }
        
        key1 = _generate_idempotency_key(task_data)
        key2 = _generate_idempotency_key(task_data)
        
        # Same input should generate same key
        assert key1 == key2
        assert len(key1) == 16  # SHA256 truncated to 16 chars
        
        # Different input should generate different key
        task_data["job_id"] = 124
        key3 = _generate_idempotency_key(task_data)
        assert key1 != key3
    
    def test_product_data_creation(self):
        """Test product data creation from item."""
        from servers.worker.tasks.media_tasks import _create_product_data_from_item
        
        # Test with example product ID
        item = {"productId": "sneaker_001"}
        full_payload = {"mode": "text"}
        
        product_data = _create_product_data_from_item(item, full_payload)
        
        assert product_data.product_id == "sneaker_001"
        assert product_data.title == "Urban Runner Pro"  # From example data
        
        # Test with unknown product ID
        item = {"productId": "unknown_product"}
        product_data = _create_product_data_from_item(item, full_payload)
        
        assert product_data.product_id == "unknown_product"
        assert product_data.title == "Product unknown_product"


class TestStorageIntegration:
    """Test enhanced storage integration."""
    
    @pytest.mark.asyncio
    async def test_enhanced_storage_text_asset(self):
        """Test storing text assets with enhanced metadata."""
        from modules.media.schemas import GeneratedAsset
        
        enhanced_storage = get_enhanced_media_storage()
        product = EXAMPLE_PRODUCTS[0]
        
        asset = GeneratedAsset(
            asset_id="test_text_storage_001",
            asset_type="text",
            content="Test SEO title for Urban Runner Pro sneakers",
            metadata={"content_type": "seo_title", "qa_score": 0.9}
        )
        
        # Mock the base storage service
        with patch.object(enhanced_storage.storage_service, 'upload_media') as mock_upload:
            mock_upload.return_value = Mock(
                public_url="https://cdn.example.com/test.json",
                storage_path="/path/to/test.json",
                metadata={"enhanced": True}
            )
            
            result = await enhanced_storage.store_generated_asset(
                asset=asset,
                product_data=product,
                tenant_id=1
            )
            
            # Verify upload was called with correct parameters
            mock_upload.assert_called_once()
            call_args = mock_upload.call_args
            
            assert call_args[1]["content_type"] == "application/json"
            assert call_args[1]["tenant_id"] == 1
            
            # Verify metadata structure
            metadata = call_args[1]["metadata"]
            assert "asset_id" in metadata
            assert "product" in metadata
            assert "seo" in metadata
            assert "quality_assurance" in metadata
    
    @pytest.mark.asyncio
    async def test_enhanced_storage_image_asset(self):
        """Test storing image assets with EXIF metadata."""
        from modules.media.schemas import GeneratedAsset
        
        enhanced_storage = get_enhanced_media_storage()
        product = EXAMPLE_PRODUCTS[0]
        
        asset = GeneratedAsset(
            asset_id="test_image_storage_001",
            asset_type="image",
            url="https://example.com/test-image.jpg",
            metadata={"style": "hero_studio", "qa_score": 0.85},
            dimensions="1024x1024",
            format="webp",
            file_size=256000
        )
        
        # Mock image download
        with patch.object(enhanced_storage, '_download_asset_content') as mock_download:
            mock_download.return_value = b"fake_image_data"
            
            # Mock EXIF processing
            with patch.object(enhanced_storage, '_add_exif_metadata') as mock_exif:
                mock_exif.return_value = b"fake_image_with_exif"
                
                # Mock storage upload
                with patch.object(enhanced_storage.storage_service, 'upload_media') as mock_upload:
                    mock_upload.return_value = Mock(
                        public_url="https://cdn.example.com/test.webp",
                        storage_path="/path/to/test.webp",
                        metadata={"enhanced": True}
                    )
                    
                    result = await enhanced_storage.store_generated_asset(
                        asset=asset,
                        product_data=product,
                        tenant_id=1
                    )
                    
                    # Verify all steps were called
                    mock_download.assert_called_once_with(asset.url)
                    mock_exif.assert_called_once()
                    mock_upload.assert_called_once()
                    
                    # Verify content type
                    call_args = mock_upload.call_args
                    assert call_args[1]["content_type"] == "image/webp"
    
    def test_seo_metadata_generation(self):
        """Test SEO metadata generation."""
        enhanced_storage = get_enhanced_media_storage()
        product = EXAMPLE_PRODUCTS[0]
        
        from modules.media.schemas import GeneratedAsset
        
        asset = GeneratedAsset(
            asset_id="test_seo_001",
            asset_type="image",
            metadata={"style": "hero_studio"}
        )
        
        # Test alt text generation
        alt_text = enhanced_storage._generate_alt_text(asset, product)
        assert "Urban Runner Pro" in alt_text
        assert "Nike" in alt_text
        
        # Test SEO title generation
        seo_title = enhanced_storage._generate_seo_title(asset, product)
        assert "Urban Runner Pro" in seo_title
        assert "Nike" in seo_title
        
        # Test keywords generation
        keywords = enhanced_storage._generate_keywords(product)
        assert "nike" in keywords
        assert "apparel" in keywords
        assert any(color.lower() in keywords for color in product.colors)


class TestProviderIntegration:
    """Test provider integration and management."""
    
    def test_provider_dashboard_data(self):
        """Test provider dashboard data generation."""
        dashboard_data = get_provider_dashboard_data()
        
        assert "providers" in dashboard_data
        assert "summary" in dashboard_data
        
        summary = dashboard_data["summary"]
        assert "total_providers" in summary
        assert "healthy_providers" in summary
        assert "total_requests_today" in summary
        assert "total_cost_today" in summary
        
        # Verify provider data structure
        providers = dashboard_data["providers"]
        for provider_name, provider_data in providers.items():
            assert "provider_name" in provider_data
            assert "status" in provider_data
            assert "health" in provider_data
            assert "usage" in provider_data
            assert "costs" in provider_data
            assert "limits" in provider_data
    
    @pytest.mark.asyncio
    async def test_provider_health_monitoring(self):
        """Test provider health monitoring."""
        from modules.media.enhanced_provider_management import enhanced_provider_manager
        
        # Test health check for all providers
        await enhanced_provider_manager.health_check_all_providers()
        
        # Verify health metrics are updated
        for provider_name in enhanced_provider_manager.health_metrics:
            health = enhanced_provider_manager.health_metrics[provider_name]
            assert health.last_check is not None
    
    @pytest.mark.asyncio
    async def test_provider_usage_recording(self):
        """Test provider usage recording."""
        from modules.media.enhanced_provider_management import enhanced_provider_manager
        
        provider_name = "mock"
        
        # Record request start
        await enhanced_provider_manager.record_request_start(provider_name, 0.01)
        
        # Verify usage stats updated
        usage = enhanced_provider_manager.usage_stats[provider_name]
        assert usage.concurrent_requests > 0
        assert len(usage.request_timestamps) > 0
        
        # Record request end
        await enhanced_provider_manager.record_request_end(
            provider_name=provider_name,
            success=True,
            actual_cost=0.01,
            response_time=1.5
        )
        
        # Verify metrics updated
        health = enhanced_provider_manager.health_metrics[provider_name]
        cost_tracker = enhanced_provider_manager.cost_trackers[provider_name]
        
        assert health.total_requests > 0
        assert cost_tracker.daily_cost > 0
        assert usage.concurrent_requests == 0


class TestCompleteWorkflow:
    """Test complete end-to-end workflows."""
    
    @pytest.mark.asyncio
    async def test_complete_media_generation_workflow(self):
        """Test complete media generation workflow with mocked dependencies."""
        
        # Prepare test data
        task_data = {
            "job_id": 123,
            "user_id": 456,
            "full_payload": {
                "mode": "text",
                "items": [{"productId": "sneaker_001"}],
                "settings": {"content_types": ["seo_title"]}
            }
        }
        
        # Mock database operations
        with patch('servers.worker.tasks.media_tasks.SessionLocal') as mock_db_session:
            mock_db = Mock()
            mock_db_session.return_value = mock_db
            
            # Mock job retrieval
            mock_job = Mock()
            mock_job.id = 123
            mock_job.status = "pending"
            mock_job.custom_config = {}
            
            mock_db.execute.return_value.scalar_one_or_none.return_value = mock_job
            
            # Mock text pipeline
            with patch('modules.media.text_pipeline.text_pipeline.generate_product_text') as mock_text_gen:
                mock_text_gen.return_value = Mock(
                    success=True,
                    assets=[Mock(
                        asset_id="test_text_001",
                        asset_type="text",
                        content="Generated SEO title",
                        metadata={"qa_score": 0.9}
                    )],
                    actual_processing_time=2.5,
                    needs_manual_review=False,
                    validation_results={}
                )
                
                # Mock variant updates
                mock_db.execute.return_value.scalars.return_value.all.return_value = []
                
                # Run the task
                result = generate_media_comprehensive(Mock(), task_data)
                
                # Verify result structure
                assert result["status"] in ["completed", "partial_success"]
                assert "job_id" in result
                assert "metrics" in result
                assert "idempotency_key" in result
                
                # Verify metrics
                metrics = result["metrics"]
                assert "total_items" in metrics
                assert "successful_items" in metrics
                assert "processing_time" in metrics
    
    def test_error_handling_and_recovery(self):
        """Test error handling and recovery mechanisms."""
        
        # Test with invalid task data
        invalid_task_data = {
            "job_id": None,  # Invalid
            "user_id": 456,
            "full_payload": {}
        }
        
        # Mock database and other dependencies
        with patch('servers.worker.tasks.media_tasks.SessionLocal') as mock_db_session:
            mock_db = Mock()
            mock_db_session.return_value = mock_db
            
            # Mock task object for retry logic
            mock_task = Mock()
            mock_task.request.retries = 0
            mock_task.max_retries = 3
            mock_task.retry = Mock(side_effect=Exception("Retry called"))
            
            # Should handle validation error gracefully
            try:
                result = generate_media_comprehensive(mock_task, invalid_task_data)
                # If it doesn't raise, it should return error result
                assert not result.get("success", True)
            except Exception as e:
                # Retry mechanism should be triggered
                assert "Retry called" in str(e) or "validation" in str(e).lower()


class TestPerformanceAndScaling:
    """Test performance and scaling considerations."""
    
    def test_concurrent_request_handling(self):
        """Test handling of concurrent requests."""
        from modules.media.enhanced_provider_management import enhanced_provider_manager
        
        provider_name = "mock"
        rate_limit = enhanced_provider_manager.rate_limits[provider_name]
        
        # Test that concurrent request limit is enforced
        usage = enhanced_provider_manager.usage_stats[provider_name]
        usage.concurrent_requests = rate_limit.concurrent_requests
        
        # Should not allow more concurrent requests
        can_make_request = asyncio.run(
            enhanced_provider_manager.can_make_request(provider_name)
        )
        assert not can_make_request[0]
        assert "concurrent" in can_make_request[1].lower()
    
    def test_memory_usage_optimization(self):
        """Test memory usage optimization in pipelines."""
        # Test that large data structures are properly cleaned up
        from modules.media.enhanced_provider_management import enhanced_provider_manager
        
        # Simulate many requests to check memory cleanup
        provider_name = "mock"
        usage = enhanced_provider_manager.usage_stats[provider_name]
        
        # Add many timestamps
        from datetime import datetime, timedelta
        now = datetime.now()
        for i in range(1000):
            usage.request_timestamps.append(now - timedelta(minutes=i))
        
        # Simulate request start (should clean old timestamps)
        asyncio.run(enhanced_provider_manager.record_request_start(provider_name))
        
        # Should have cleaned up old timestamps (older than 1 hour)
        hour_ago = now - timedelta(hours=1)
        recent_timestamps = [ts for ts in usage.request_timestamps if ts > hour_ago]
        assert len(usage.request_timestamps) == len(recent_timestamps)


# Test fixtures for integration tests
@pytest.fixture
def mock_database_session():
    """Mock database session for testing."""
    with patch('servers.worker.tasks.media_tasks.SessionLocal') as mock_session:
        mock_db = Mock()
        mock_session.return_value = mock_db
        yield mock_db


@pytest.fixture
def sample_task_data():
    """Sample task data for testing."""
    return {
        "job_id": 123,
        "user_id": 456,
        "full_payload": {
            "mode": "text",
            "items": [{"productId": "sneaker_001"}],
            "settings": {"content_types": ["seo_title", "description"]}
        }
    }


if __name__ == "__main__":
    # Run integration tests
    pytest.main([__file__, "-v", "--tb=short"])
