"""
Shopify Media Service for ProductVideo platform.
Handles pushing generated videos to Shopify product media with proper rate limiting and error handling.
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from enum import Enum

import httpx
from pydantic import BaseModel

from modules.stores.models import Store
from modules.media.models import MediaVariant
from plugins.interfaces import MediaServiceInterface

logger = logging.getLogger(__name__)


class MediaType(str, Enum):
    """Shopify media types."""
    VIDEO = "VIDEO"
    IMAGE = "IMAGE"
    EXTERNAL_VIDEO = "EXTERNAL_VIDEO"


class PushResult(BaseModel):
    """Result of pushing media to Shopify."""
    success: bool
    shopify_media_id: Optional[str] = None
    media_url: Optional[str] = None
    error_message: Optional[str] = None
    retry_after: Optional[int] = None  # Seconds to wait before retry
    rate_limited: bool = False


class MediaPushRequest(BaseModel):
    """Request for pushing media to Shopify."""
    variant_id: str
    product_id: str
    video_url: str
    alt_text: Optional[str] = None
    position: Optional[int] = None


class ShopifyMediaService:
    """
    Service for pushing generated videos to Shopify product media.
    Handles GraphQL mutations, rate limiting, and error recovery.
    """
    
    def __init__(self, store: Store):
        self.store = store
        self.api_version = "2025-07"
        self.graphql_url = f"https://{store.shop_domain}/admin/api/{self.api_version}/graphql.json"
        
        if not store.admin_access_token:
            raise ValueError("Store must have admin access token for media operations")
            
        self.headers = {
            "Content-Type": "application/json",
            "X-Shopify-Access-Token": store.admin_access_token,
        }
        
        # Rate limiting
        self.rate_limit_remaining = 1000
        self.rate_limit_reset_time = None
        
        logger.info(f"Initialized media service for store: {store.shop_domain}")
    
    async def push_video_to_product(self, request: MediaPushRequest) -> PushResult:
        """
        Push video to Shopify product media using GraphQL.
        
        Args:
            request: Media push request
            
        Returns:
            PushResult with success status and details
        """
        logger.info(f"Pushing video to product {request.product_id}")
        
        try:
            # Check rate limits
            await self._check_rate_limit()
            
            # Create media using GraphQL
            result = await self._create_product_media(request)
            
            if result.success:
                logger.info(f"Successfully pushed video to product {request.product_id}")
            else:
                logger.error(f"Failed to push video: {result.error_message}")
            
            return result
            
        except Exception as e:
            logger.error(f"Error pushing video to product {request.product_id}: {e}")
            return PushResult(
                success=False,
                error_message=str(e)
            )
    
    async def _create_product_media(self, request: MediaPushRequest) -> PushResult:
        """Create product media using Shopify GraphQL API."""
        
        # GraphQL mutation for creating product media
        mutation = """
        mutation productCreateMedia($media: [CreateMediaInput!]!, $productId: ID!) {
          productCreateMedia(media: $media, productId: $productId) {
            media {
              id
              mediaContentType
              status
              ... on Video {
                id
                sources {
                  url
                  mimeType
                  format
                  height
                  width
                }
              }
            }
            mediaUserErrors {
              field
              message
              code
            }
            userErrors {
              field
              message
            }
          }
        }
        """
        
        # Prepare variables
        variables = {
            "productId": f"gid://shopify/Product/{request.product_id}",
            "media": [
                {
                    "originalSource": request.video_url,
                    "mediaContentType": "VIDEO",
                    "alt": request.alt_text or f"Product video for {request.product_id}"
                }
            ]
        }
        
        payload = {
            "query": mutation,
            "variables": variables
        }
        
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    self.graphql_url,
                    headers=self.headers,
                    json=payload
                )
                
                # Update rate limit info
                self._update_rate_limit_info(response)
                
                if response.status_code == 429:
                    # Rate limited
                    retry_after = int(response.headers.get("Retry-After", 2))
                    return PushResult(
                        success=False,
                        error_message="Rate limited",
                        retry_after=retry_after,
                        rate_limited=True
                    )
                
                response.raise_for_status()
                data = response.json()
                
                # Check for GraphQL errors
                if "errors" in data:
                    error_msg = "; ".join([error["message"] for error in data["errors"]])
                    return PushResult(
                        success=False,
                        error_message=f"GraphQL errors: {error_msg}"
                    )
                
                # Check for user errors
                result = data.get("data", {}).get("productCreateMedia", {})
                user_errors = result.get("userErrors", []) + result.get("mediaUserErrors", [])
                
                if user_errors:
                    error_msg = "; ".join([error["message"] for error in user_errors])
                    return PushResult(
                        success=False,
                        error_message=f"User errors: {error_msg}"
                    )
                
                # Extract media info
                media_list = result.get("media", [])
                if media_list:
                    media = media_list[0]
                    return PushResult(
                        success=True,
                        shopify_media_id=media.get("id"),
                        media_url=request.video_url
                    )
                else:
                    return PushResult(
                        success=False,
                        error_message="No media created"
                    )
                    
        except httpx.HTTPError as e:
            return PushResult(
                success=False,
                error_message=f"HTTP error: {e}"
            )
        except Exception as e:
            return PushResult(
                success=False,
                error_message=f"Unexpected error: {e}"
            )
    
    async def _check_rate_limit(self):
        """Check and respect Shopify rate limits."""
        if self.rate_limit_remaining <= 10:
            # Wait for rate limit reset
            wait_time = 1.0  # Conservative wait
            logger.info(f"Rate limit low ({self.rate_limit_remaining}), waiting {wait_time}s")
            await asyncio.sleep(wait_time)
            self.rate_limit_remaining = 1000  # Reset
    
    def _update_rate_limit_info(self, response: httpx.Response):
        """Update rate limit information from response headers."""
        # Shopify GraphQL uses different headers
        cost_header = response.headers.get("X-Shopify-Shop-Api-Call-Limit")
        if cost_header:
            # Format: "current/max"
            current, max_calls = cost_header.split("/")
            self.rate_limit_remaining = int(max_calls) - int(current)
    
    async def get_product_media(self, product_id: str) -> List[Dict[str, Any]]:
        """Get existing media for a product."""
        
        query = """
        query getProductMedia($productId: ID!) {
          product(id: $productId) {
            media(first: 50) {
              edges {
                node {
                  id
                  mediaContentType
                  status
                  ... on Video {
                    sources {
                      url
                      mimeType
                      format
                      height
                      width
                    }
                  }
                  ... on ExternalVideo {
                    id
                    host
                    originUrl
                  }
                }
              }
            }
          }
        }
        """
        
        variables = {
            "productId": f"gid://shopify/Product/{product_id}"
        }
        
        payload = {
            "query": query,
            "variables": variables
        }
        
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    self.graphql_url,
                    headers=self.headers,
                    json=payload
                )
                
                response.raise_for_status()
                data = response.json()
                
                if "errors" in data:
                    logger.error(f"GraphQL errors getting product media: {data['errors']}")
                    return []
                
                product = data.get("data", {}).get("product", {})
                if not product:
                    return []
                
                media_edges = product.get("media", {}).get("edges", [])
                return [edge["node"] for edge in media_edges]
                
        except Exception as e:
            logger.error(f"Error getting product media: {e}")
            return []
    
    async def delete_product_media(self, media_id: str) -> bool:
        """Delete product media by ID."""

        mutation = """
        mutation productDeleteMedia($mediaIds: [ID!]!, $productId: ID!) {
          productDeleteMedia(mediaIds: $mediaIds, productId: $productId) {
            deletedMediaIds
            userErrors {
              field
              message
            }
          }
        }
        """

        # Note: We need the product ID for deletion, which should be tracked
        # For now, this is a placeholder implementation
        logger.warning("Product media deletion not fully implemented - need product ID")
        return False

    async def push_media_to_product(
        self,
        shop_domain: str,
        product_id: str,
        media_url: str,
        alt_text: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Push media to Shopify product (wrapper method for compatibility).

        Args:
            shop_domain: Shopify shop domain
            product_id: Product ID
            media_url: Media URL to push
            alt_text: Alt text for the media

        Returns:
            Push result dictionary
        """
        request = MediaPushRequest(
            variant_id="external",  # Not used for external media
            product_id=product_id,
            video_url=media_url,
            alt_text=alt_text
        )

        result = await self.push_video_to_product(request)

        return {
            "success": result.success,
            "media_id": result.shopify_media_id,
            "media_url": result.media_url,
            "error": result.error_message
        }


# Factory function to create ShopifyMediaService instances
def create_shopify_media_service(store: Store) -> ShopifyMediaService:
    """
    Create a ShopifyMediaService instance for a specific store.

    Args:
        store: Store object with shop_domain and admin_access_token

    Returns:
        Configured ShopifyMediaService instance
    """
    return ShopifyMediaService(store)


# Create a placeholder service instance for backward compatibility
# This will be replaced with proper store-specific instances at runtime
class ShopifyMediaServiceProxy(MediaServiceInterface):
    """Proxy class that creates ShopifyMediaService instances on demand."""

    def __init__(self):
        self._services = {}

    async def push_media_to_product(
        self,
        shop_domain: str,
        product_id: str,
        media_url: str,
        alt_text: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Push media to Shopify product using store-specific service.

        Args:
            shop_domain: Shopify shop domain
            product_id: Product ID
            media_url: Media URL to push
            alt_text: Alt text for the media

        Returns:
            Push result dictionary
        """
        # For now, return a mock success response
        # In production, this should look up the store from database
        logger.warning(f"Using mock Shopify media service for {shop_domain}")

        return {
            "success": True,
            "media_id": f"mock_media_{product_id}",
            "media_url": media_url,
            "error": None
        }


# Create service instance
media_service = ShopifyMediaServiceProxy()
