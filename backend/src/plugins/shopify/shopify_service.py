"""
Shopify GraphQL API Service
Handles all interactions with Shopify Admin and Storefront GraphQL APIs
"""

import httpx
import json
from typing import Dict, List, Optional, Any
from datetime import datetime
import logging
import asyncio
import time

from plugins.interfaces import StoreServiceInterface

logger = logging.getLogger(__name__)


class ShopifyGraphQLService(StoreServiceInterface):
    """Service for interacting with Shopify GraphQL APIs"""

    def __init__(
        self,
        shop_domain: str,
        admin_access_token: str,
        request_delay: float = 1.0,
        max_retries: int = 5,
        base_backoff_delay: float = 2.0
    ):
        self.shop_domain = shop_domain
        self.admin_access_token = admin_access_token

        # Update to a current stable API version
        # Shopify API versions follow a quarterly release schedule (YYYY-MM)
        self.admin_api_version = "2024-01"  # Update to a current stable version
        self.storefront_api_version = "2024-01"  # Update to a current stable version

        # API endpoints
        self.admin_endpoint = f"https://{shop_domain}/admin/api/{self.admin_api_version}/graphql.json"
        self.storefront_endpoint = f"https://{shop_domain}/api/{self.storefront_api_version}/graphql.json"

        # Rate limiting configuration
        self.request_delay = request_delay  # Delay between requests in seconds
        self.max_retries = max_retries  # Maximum number of retries for throttled requests
        self.base_backoff_delay = base_backoff_delay  # Base delay for exponential backoff
        self.last_request_time = 0  # Track last request time for rate limiting

        logger.info(f"Initialized Shopify service for {shop_domain} with Admin API version {self.admin_api_version}")
        logger.info(f"Rate limiting: delay={request_delay}s, max_retries={max_retries}, backoff={base_backoff_delay}s")

    async def test_admin_connection(self) -> Dict[str, Any]:
        """Test connection to Shopify Admin API"""
        query = """
        query {
            shop {
                id
                name
                email
                myshopifyDomain
                primaryDomain {
                    url
                    host
                }
                plan {
                    displayName
                }
                currencyCode
            }
        }
        """

        try:
            result = await self._execute_admin_query(query)
            if result.get("data", {}).get("shop"):
                return {
                    "success": True,
                    "message": "Successfully connected to Shopify Admin API",
                    "shop_info": result["data"]["shop"],
                }
            else:
                errors = result.get("errors", [])
                error_messages = [e.get("message", "Unknown error") for e in errors]
                return {
                    "success": False,
                    "message": f"Failed to retrieve shop information: {'; '.join(error_messages)}",
                    "errors": errors,
                }
        except Exception as e:
            logger.error(f"Admin API connection test failed: {str(e)}")
            return {"success": False, "message": f"Connection failed: {str(e)}"}

    async def test_storefront_connection(self) -> Dict[str, Any]:
        """Test connection to Shopify Storefront API"""
        if not self.storefront_access_token:
            return {"success": False, "message": "Storefront access token not provided"}

        query = """
        query {
            shop {
                name
                primaryDomain {
                    host
                }
            }
        }
        """

        try:
            result = await self._execute_storefront_query(query)
            if result.get("data", {}).get("shop"):
                return {
                    "success": True,
                    "message": "Successfully connected to Shopify Storefront API",
                    "shop_info": result["data"]["shop"],
                }
            else:
                return {
                    "success": False,
                    "message": "Failed to retrieve shop information from Storefront API",
                    "errors": result.get("errors", []),
                }
        except Exception as e:
            logger.error(f"Storefront API connection test failed: {str(e)}")
            return {"success": False, "message": f"Storefront connection failed: {str(e)}"}

    async def get_shop_info(self) -> Dict[str, Any]:
        """Get comprehensive shop information"""
        query = """
        query {
            shop {
                id
                name
                email
                myshopifyDomain
                primaryDomain {
                    url
                    host
                }
                plan {
                    displayName
                }
                currencyCode
                timezoneAbbreviation
                weightUnit
                billingAddress {
                    country
                    countryCodeV2
                }
            }
        }
        """

        result = await self._execute_admin_query(query)
        return result.get("data", {}).get("shop", {})

    async def get_products(self, limit: int = 50, offset: int = 0) -> Dict[str, Any]:
        """Get products using GraphQL Admin API with offset-based pagination"""
        all_edges = []
        cursor = None
        total_fetched = 0

        # Paginate until we have enough items for the offset
        while total_fetched < offset + limit:
            after_clause = f', after: "{cursor}"' if cursor else ""

            query = f"""
            query {{
                products(first: {min(50, offset + limit - total_fetched)}{after_clause}) {{
                    edges {{
                        node {{
                            id
                            title
                            description
                            descriptionHtml
                            handle
                            productType
                            vendor
                            status
                            tags
                            createdAt
                            updatedAt
                            publishedAt
                            onlineStoreUrl
                            onlineStorePreviewUrl
                            hasOnlyDefaultVariant
                            hasOutOfStockVariants
                            isGiftCard
                            requiresSellingPlan
                            totalInventory
                            totalVariants
                            tracksInventory
                            templateSuffix
                            giftCardTemplateSuffix
                            legacyResourceId
                            featuredImage {{
                                id
                                url
                                altText
                                width
                                height
                            }}
                            images(first: 50) {{
                                edges {{
                                    node {{
                                        id
                                        url
                                        altText
                                        width
                                        height
                                        originalSrc
                                        transformedSrc(maxWidth: 800, maxHeight: 800)
                                    }}
                                }}
                            }}
                            variants(first: 100) {{
                                edges {{
                                    node {{
                                        id
                                        title
                                        price
                                        compareAtPrice
                                        sku
                                        barcode
                                        inventoryQuantity
                                        taxable
                                        createdAt
                                        updatedAt
                                        inventoryPolicy
                                        inventoryItem {{
                                            id
                                            tracked
                                        }}
                                    }}
                                }}
                            }}
                            options {{
                                id
                                name
                                position
                                values
                            }}
                            productCategory {{
                                productTaxonomyNode {{
                                    id
                                    name
                                    fullName
                                }}
                            }}
                            seo {{
                                title
                                description
                            }}
                            metafields(first: 50) {{
                                edges {{
                                    node {{
                                        id
                                        namespace
                                        key
                                        value
                                        type
                                        description
                                    }}
                                }}
                            }}
                            collections(first: 50) {{
                                edges {{
                                    node {{
                                        id
                                        title
                                        handle
                                        description
                                        updatedAt
                                    }}
                                }}
                            }}
                        }}
                        cursor
                    }}
                    pageInfo {{
                        hasNextPage
                        endCursor
                    }}
                }}
            }}
            """

            result = await self._execute_admin_query(query)

            # Check if there are any errors in the result
            if "errors" in result:
                logger.error(f"GraphQL errors in get_products: {json.dumps(result.get('errors'))}")
                return {"errors": result.get("errors"), "edges": []}

            products = result.get("data", {}).get("products", {})
            edges = products.get("edges", [])
            page_info = products.get("pageInfo", {})

            if not edges:
                break

            all_edges.extend(edges)
            total_fetched += len(edges)

            if not page_info.get("hasNextPage", False):
                break

            cursor = page_info.get("endCursor")

        # Extract the requested slice
        start_idx = offset
        end_idx = offset + limit
        requested_edges = all_edges[start_idx:end_idx] if start_idx < len(all_edges) else []

        # Log product count for debugging
        logger.info(f"Retrieved {len(requested_edges)} products from Shopify API (offset: {offset}, limit: {limit})")

        return {
            "edges": requested_edges,
            "pageInfo": {
                "hasNextPage": page_info.get("hasNextPage", False),
                "endCursor": requested_edges[-1]["cursor"] if requested_edges else None
            }
        }


    async def get_orders(self, limit: int = 50, offset: int = 0) -> Dict[str, Any]:
        """Get orders using GraphQL Admin API with offset-based pagination"""
        all_edges = []
        cursor = None
        total_fetched = 0

        # Paginate until we have enough items for the offset
        while total_fetched < offset + limit:
            after_clause = f', after: "{cursor}"' if cursor else ""

            query = f"""
            query {{
                orders(first: {min(50, offset + limit - total_fetched)}{after_clause}) {{
                    edges {{
                        node {{
                            id
                            name
                            email
                            createdAt
                            updatedAt
                            totalPriceSet {{
                                shopMoney {{
                                    amount
                                    currencyCode
                                }}
                            }}
                            subtotalPriceSet {{
                                shopMoney {{
                                    amount
                                    currencyCode
                                }}
                            }}
                            totalTaxSet {{
                                shopMoney {{
                                    amount
                                    currencyCode
                                }}
                            }}
                            totalDiscountsSet {{
                                shopMoney {{
                                    amount
                                    currencyCode
                                }}
                            }}
                            displayFinancialStatus
                            displayFulfillmentStatus
                            customer {{
                                id
                                email
                            }}
                            lineItems(first: 50) {{
                                edges {{
                                    node {{
                                        id
                                        title
                                        quantity
                                        variant {{
                                            id
                                            product {{
                                                id
                                            }}
                                        }}
                                        originalTotalSet {{
                                            shopMoney {{
                                                amount
                                                currencyCode
                                            }}
                                        }}
                                    }}
                                }}
                            }}
                        }}
                        cursor
                    }}
                    pageInfo {{
                        hasNextPage
                        endCursor
                    }}
                }}
            }}
            """

            result = await self._execute_admin_query(query)

            if "errors" in result:
                logger.error(f"GraphQL errors in get_orders: {json.dumps(result.get('errors'))}")
                return {"errors": result.get("errors"), "edges": []}

            orders = result.get("data", {}).get("orders", {})
            edges = orders.get("edges", [])
            page_info = orders.get("pageInfo", {})

            if not edges:
                break

            all_edges.extend(edges)
            total_fetched += len(edges)

            if not page_info.get("hasNextPage", False):
                break

            cursor = page_info.get("endCursor")

        # Extract the requested slice
        start_idx = offset
        end_idx = offset + limit
        requested_edges = all_edges[start_idx:end_idx] if start_idx < len(all_edges) else []

        # Log order count for debugging
        logger.info(f"Retrieved {len(requested_edges)} orders from Shopify API (offset: {offset}, limit: {limit})")

        return {
            "edges": requested_edges,
            "pageInfo": {
                "hasNextPage": page_info.get("hasNextPage", False),
                "endCursor": requested_edges[-1]["cursor"] if requested_edges else None
            }
        }


    async def _execute_admin_query(self, query: str, variables: Optional[Dict] = None) -> Dict[str, Any]:
        """Execute a GraphQL query against the Admin API with rate limiting and retry logic"""
        headers = {"Content-Type": "application/json", "X-Shopify-Access-Token": self.admin_access_token}

        payload = {"query": query}
        if variables:
            payload["variables"] = variables

        for attempt in range(self.max_retries + 1):
            try:
                # Implement rate limiting - ensure minimum delay between requests
                current_time = time.time()
                time_since_last_request = current_time - self.last_request_time
                if time_since_last_request < self.request_delay:
                    delay = self.request_delay - time_since_last_request
                    logger.debug(f"Rate limiting: waiting {delay:.2f} seconds")
                    await asyncio.sleep(delay)

                self.last_request_time = time.time()

                async with httpx.AsyncClient() as client:
                    logger.debug(f"Sending request to {self.admin_endpoint} (attempt {attempt + 1}/{self.max_retries + 1})")
                    response = await client.post(self.admin_endpoint, headers=headers, json=payload, timeout=30.0)

                    # Log response status and headers for debugging
                    logger.debug(f"Response status: {response.status_code}")
                    logger.debug(f"Response headers: {dict(response.headers)}")

                    response.raise_for_status()
                    result = response.json()

                    # Check for throttling errors
                    if "errors" in result:
                        errors = result["errors"]
                        throttled = any(
                            error.get("extensions", {}).get("code") == "THROTTLED"
                            for error in errors
                        )

                        if throttled:
                            if attempt < self.max_retries:
                                # Calculate exponential backoff delay
                                backoff_delay = self.base_backoff_delay * (2 ** attempt)
                                logger.warning(f"Request throttled, retrying in {backoff_delay} seconds (attempt {attempt + 1}/{self.max_retries + 1})")
                                await asyncio.sleep(backoff_delay)
                                continue
                            else:
                                logger.error(f"Max retries exceeded for throttled request")
                                return result

                        # Log other GraphQL errors but don't retry
                        logger.error(f"GraphQL errors: {json.dumps(errors)}")

                    return result

            except httpx.HTTPStatusError as e:
                logger.error(f"HTTP error: {e.response.status_code} - {e.response.text}")

                # Check for rate limiting headers
                retry_after = e.response.headers.get("Retry-After")
                if retry_after and attempt < self.max_retries:
                    try:
                        delay = float(retry_after)
                        logger.warning(f"Rate limited by server, retrying after {delay} seconds")
                        await asyncio.sleep(delay)
                        continue
                    except ValueError:
                        pass

                error_data = {"errors": [{"message": f"HTTP error {e.response.status_code}: {e.response.text}"}]}
                return error_data
            except httpx.RequestError as e:
                logger.error(f"Request error: {str(e)}")
                if attempt < self.max_retries:
                    backoff_delay = self.base_backoff_delay * (2 ** attempt)
                    logger.warning(f"Request failed, retrying in {backoff_delay} seconds (attempt {attempt + 1}/{self.max_retries + 1})")
                    await asyncio.sleep(backoff_delay)
                    continue
                error_data = {"errors": [{"message": f"Request error: {str(e)}"}]}
                return error_data

        # This should not be reached, but just in case
        return {"errors": [{"message": "Max retries exceeded"}]}

    async def _execute_storefront_query(self, query: str, variables: Optional[Dict] = None) -> Dict[str, Any]:
        """Execute a GraphQL query against the Storefront API with rate limiting"""
        if not self.storefront_access_token:
            raise ValueError("Storefront access token is required")

        headers = {
            "Content-Type": "application/json",
            "X-Shopify-Storefront-Access-Token": self.storefront_access_token,
        }

        payload = {"query": query}
        if variables:
            payload["variables"] = variables

        for attempt in range(self.max_retries + 1):
            try:
                # Implement rate limiting for storefront API as well
                current_time = time.time()
                time_since_last_request = current_time - self.last_request_time
                if time_since_last_request < self.request_delay:
                    delay = self.request_delay - time_since_last_request
                    logger.debug(f"Rate limiting: waiting {delay:.2f} seconds")
                    await asyncio.sleep(delay)

                self.last_request_time = time.time()

                async with httpx.AsyncClient() as client:
                    logger.debug(f"Sending storefront request (attempt {attempt + 1}/{self.max_retries + 1})")
                    response = await client.post(self.storefront_endpoint, headers=headers, json=payload, timeout=30.0)
                    response.raise_for_status()
                    return response.json()
            except httpx.HTTPStatusError as e:
                logger.error(f"Storefront HTTP error: {e.response.status_code} - {e.response.text}")
                if attempt < self.max_retries:
                    backoff_delay = self.base_backoff_delay * (2 ** attempt)
                    logger.warning(f"Storefront request failed, retrying in {backoff_delay} seconds")
                    await asyncio.sleep(backoff_delay)
                    continue
                error_data = {"errors": [{"message": f"HTTP error {e.response.status_code}: {e.response.text}"}]}
                return error_data
            except httpx.RequestError as e:
                logger.error(f"Storefront request error: {str(e)}")
                if attempt < self.max_retries:
                    backoff_delay = self.base_backoff_delay * (2 ** attempt)
                    logger.warning(f"Storefront request failed, retrying in {backoff_delay} seconds")
                    await asyncio.sleep(backoff_delay)
                    continue
                error_data = {"errors": [{"message": f"Request error: {str(e)}"}]}
                return error_data

        # This should not be reached, but just in case
        return {"errors": [{"message": "Max retries exceeded"}]}

    async def test_connection(self) -> Dict[str, Any]:
        """Test connection to Shopify platform."""
        results = {
            "admin": {"success": False, "message": "Not tested"},
            "overall_success": False
        }

        try:
            # Test Admin API connection
            admin_result = await self.test_admin_connection()
            results["admin"] = admin_result

            # Determine overall success
            admin_success = admin_result.get("success", False)
            results["overall_success"] = admin_success

            # Include shop info from admin if successful
            if admin_success and admin_result.get("shop_info"):
                shop_info = admin_result["shop_info"]
                results.update({
                    "shop_name": shop_info.get("name"),
                    "shop_domain": shop_info.get("myshopifyDomain"),
                    "shop_id": shop_info.get("id"),
                    "currency": shop_info.get("currencyCode")
                })

            return results

        except Exception as e:
            logger.error(f"Connection test failed: {e}")
            results["admin"]["message"] = f"Connection failed: {str(e)}"
            return results

    async def _paginate_all(self, get_func, item_type: str) -> Dict[str, Any]:
        """Generic pagination helper for fetching all items"""
        all_edges = []
        offset = 0
        limit = 50

        logger.info(f"Starting full {item_type} fetch with pagination")

        while True:
            result = await get_func(limit=limit, offset=offset)

            if "errors" in result:
                logger.error(f"Error fetching {item_type} page: {result['errors']}")
                return {"errors": result["errors"], "edges": all_edges}

            edges = result.get("edges", [])
            page_info = result.get("pageInfo", {})

            if not edges:
                break

            # Add edges to our collection
            all_edges.extend(edges)
            offset += len(edges)

            logger.info(f"Fetched {len(edges)} {item_type} (total: {len(all_edges)})")

            # Check if there are more pages
            if not page_info.get("hasNextPage", False):
                break

        logger.info(f"Completed full {item_type} fetch: {len(all_edges)} {item_type} total")

        # Enhanced logging for sync summary
        if item_type == "products":
            logger.info("=== SHOPIFY PRODUCTS FETCH SUMMARY ===")
            logger.info(f"Total products fetched from Shopify: {len(all_edges)}")
            logger.info("=====================================")
        elif item_type == "orders":
            logger.info("=== SHOPIFY ORDERS FETCH SUMMARY ===")
            logger.info(f"Total orders fetched from Shopify: {len(all_edges)}")
            logger.info("===================================")

        return {
            "edges": all_edges,
            "pageInfo": {
                "hasNextPage": False,
                "endCursor": None
            }
        }
