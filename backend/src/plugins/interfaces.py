"""
Plugin interfaces for platform-agnostic operations.
"""

from typing import Dict, Any, Optional
from abc import ABC, abstractmethod


class MediaServiceInterface(ABC):
    """Abstract interface for media services across different platforms."""

    @abstractmethod
    async def push_media_to_product(
        self,
        shop_domain: str,
        product_id: str,
        media_url: str,
        alt_text: Optional[str] = None
    ) -> Dict[str, Any]:
        """Push media to product media."""
        pass


class SyncServiceInterface(ABC):
    """Abstract interface for sync services across different platforms."""

    @abstractmethod
    async def sync_products(
        self,
        db: Any,  # AsyncSession
        limit: int = 50,
        cursor: Optional[str] = None
    ) -> Dict[str, int]:
        """Sync products from platform."""
        pass

    @abstractmethod
    async def sync_orders(
        self,
        db: Any,  # AsyncSession
        limit: int = 50,
        cursor: Optional[str] = None
    ) -> Dict[str, int]:
        """Sync orders from platform."""
        pass

    @abstractmethod
    async def get_shop_info(self) -> Dict[str, Any]:
        """Get shop information."""
        pass

    @abstractmethod
    async def get_products(self, limit: int = 50, offset: int = 0) -> Dict[str, Any]:
        """Get products from platform."""
        pass

    @abstractmethod
    async def get_orders(self, limit: int = 50, offset: int = 0) -> Dict[str, Any]:
        """Get orders from platform."""
        pass

    @abstractmethod
    async def test_connection(self) -> Dict[str, Any]:
        """Test connection to platform."""
        pass


class StoreServiceInterface(ABC):
    """Abstract interface for platform-specific store API services."""

    @abstractmethod
    async def get_products(self, limit: int = 50, offset: int = 0) -> Dict[str, Any]:
        """Get products with pagination."""
        pass

    @abstractmethod
    async def get_orders(self, limit: int = 50, offset: int = 0) -> Dict[str, Any]:
        """Get orders with pagination."""
        pass

    @abstractmethod
    async def get_shop_info(self) -> Dict[str, Any]:
        """Get shop/store information."""
        pass

    @abstractmethod
    async def test_connection(self) -> Dict[str, Any]:
        """Test connection to the platform."""
        pass