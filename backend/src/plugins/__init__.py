"""
Plugin System for ProductVideo Platform.
Manages platform-specific integrations and services.
"""

import logging
import importlib
from typing import Dict, List, Optional, Any

logger = logging.getLogger(__name__)


class PluginRegistry:
    """
    Registry for managing platform-specific plugins.
    Handles dynamic loading and service discovery.
    """

    def __init__(self):
        self._services = {}
        self._load_available_plugins()

    def _load_available_plugins(self):
        """Load all available platform plugins dynamically."""
        # Define supported platforms
        supported_platforms = ["shopify"]  # Add more as they become available

        for platform in supported_platforms:
            try:
                # Dynamically import the platform plugin
                plugin_module = importlib.import_module(f"plugins.{platform}")
                sync_service = getattr(plugin_module, f"{platform}_sync_service")
                self._services[platform] = sync_service
                logger.info(f"Loaded {platform} plugin service")
            except (ImportError, AttributeError) as e:
                logger.warning(f"Failed to load {platform} plugin: {e}")
                continue

    def get_service(self, platform: str):
        """Get the sync service for a specific platform."""
        service = self._services.get(platform)
        if not service:
            raise ValueError(f"No service available for platform: {platform}")
        return service

    def get_available_platforms(self) -> List[str]:
        """Get list of available platforms."""
        return list(self._services.keys())

    def has_platform(self, platform: str) -> bool:
        """Check if a platform is available."""
        return platform in self._services


# Create global plugin registry instance
plugin_registry = PluginRegistry()


def get_platform_service(platform: str):
    """Convenience function to get a platform service."""
    return plugin_registry.get_service(platform)


def get_available_platforms() -> List[str]:
    """Convenience function to get available platforms."""
    return plugin_registry.get_available_platforms()


def has_platform(platform: str) -> bool:
    """Convenience function to check platform availability."""
    return plugin_registry.has_platform(platform)


# Media service instance
from .media_service import PluginMediaService
_media_service_instance = None

def get_media_service():
    """Get the global media service instance."""
    global _media_service_instance
    if _media_service_instance is None:
        _media_service_instance = PluginMediaService()
    return _media_service_instance


def register_plugins(app):
    """
    Register all available plugins with the FastAPI application.

    This function:
    - Registers plugin routers with the app
    - Initializes plugin services
    - Sets up plugin-specific middleware if needed
    """
    logger.info("Registering plugins with FastAPI application...")

    # Register Shopify plugin router if available
    try:
        from .shopify.router import router as shopify_router
        app.include_router(shopify_router, prefix="/api/plugins/shopify", tags=["shopify"])
        logger.info("Registered Shopify plugin router")
    except ImportError as e:
        logger.warning(f"Failed to register Shopify plugin router: {e}")

    # Add more plugin registrations here as they become available
    # For example:
    # try:
    #     from .woocommerce.router import router as woocommerce_router
    #     app.include_router(woocommerce_router, prefix="/api/plugins/woocommerce", tags=["woocommerce"])
    #     logger.info("Registered WooCommerce plugin router")
    # except ImportError as e:
    #     logger.warning(f"Failed to register WooCommerce plugin router: {e}")

    logger.info("Plugin registration completed")