"""
Plugin manager for discovering and loading plugin routes.
"""

import importlib
import os
from typing import List
from fastapi import APIRouter, FastAPI


class PluginManager:
    """Manages plugin discovery and route loading."""

    def __init__(self):
        self.plugins_dir = os.path.dirname(__file__)
        self.discovered_plugins = []

    def discover_plugins(self) -> List[str]:
        """Discover available plugins."""
        plugins = []
        for item in os.listdir(self.plugins_dir):
            plugin_path = os.path.join(self.plugins_dir, item)
            if os.path.isdir(plugin_path) and not item.startswith('__'):
                # Check if plugin has __init__.py
                if os.path.exists(os.path.join(plugin_path, '__init__.py')):
                    plugins.append(item)
        return plugins

    def load_plugin_routes(self, plugin_name: str) -> List[APIRouter]:
        """Load routes from a specific plugin."""
        routes = []

        try:
            # Try to load main router (contains all routes for the plugin)
            try:
                router_module = importlib.import_module(f'plugins.{plugin_name}.router')
                if hasattr(router_module, 'router'):
                    routes.append(router_module.router)
            except ImportError:
                pass

            # Try to load additional route modules (for future extensibility)
            plugin_dir = os.path.join(self.plugins_dir, plugin_name)
            for item in os.listdir(plugin_dir):
                if item.endswith('_routes.py'):
                    try:
                        module_name = item[:-3]  # Remove .py
                        route_module = importlib.import_module(f'plugins.{plugin_name}.{module_name}')
                        if hasattr(route_module, 'router'):
                            routes.append(route_module.router)
                    except ImportError:
                        pass

        except Exception as e:
            print(f"Error loading routes for plugin {plugin_name}: {e}")

        return routes

    def register_plugin_routes(self, app: FastAPI):
        """Register all plugin routes with the FastAPI app."""
        plugins = self.discover_plugins()

        for plugin_name in plugins:
            plugin_routes = self.load_plugin_routes(plugin_name)
            for router in plugin_routes:
                # Use plugin name as subdirectory under /api/plugins
                prefix = f"/api/plugins/{plugin_name}"
                app.include_router(router, prefix=prefix, tags=[plugin_name])