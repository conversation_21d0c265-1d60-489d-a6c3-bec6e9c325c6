# Dockerfile for ProductVideo Workers
FROM python:3.12-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    ffmpeg \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY pyproject.toml .

# Copy source code
COPY . .

# Set Python path to prioritize local modules
ENV PYTHONPATH=/app/src:$PYTHONPATH

RUN pip install --upgrade pip && pip install -e .

# Create non-root user
RUN useradd --create-home --shell /bin/bash worker
USER worker

# Command to run workers
CMD ["python", "src/servers/worker/main.py"]
