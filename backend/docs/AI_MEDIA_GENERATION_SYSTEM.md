# AI Media Generation System - Complete Documentation

## Overview

The AI Media Generation System is a production-ready, professional-grade media generation pipeline for e-commerce stores. It can replace content writers and photo shoot agencies by generating high-quality text, images, and videos using AI providers with comprehensive quality assurance.

## System Architecture

### Core Components

1. **Unified Media Service** (`modules/media/unified_media_service.py`)
   - Main orchestrator that connects all components
   - Entry point for all media generation requests
   - Handles request routing, processing, and response formatting

2. **Enhanced Storage Service** (`modules/storage/storage_service.py`)
   - Manages AI-generated media with enhanced metadata
   - Handles EXIF data, SEO metadata, and versioning
   - Provides tenant isolation and CDN distribution

3. **Enhanced Provider Manager** (`modules/media/provider_manager.py`)
   - Manages AI providers with rate limiting and cost tracking
   - Provides health monitoring and automatic failover
   - Handles quotas and usage statistics

4. **AI Template Manager** (`modules/templates/template_service.py`)
   - Manages prompt templates for different media types
   - Provides Jinja2-based template rendering
   - Handles constraint validation and content optimization

5. **Quality Engine** (`modules/media/quality_engine.py`)
   - Comprehensive quality assessment for generated content
   - Safety checks and brand compliance validation
   - Automated approval vs. human review decisions

6. **Context Engine** (`modules/media/context_engine.py`)
   - Provides product, brand, and market context
   - Enriches generation prompts with relevant information

7. **Prompt Engine** (`modules/media/prompt_engine.py`)
   - Professional prompt engineering for different media types
   - Platform-specific optimization
   - Style and tone management

## How It Works

### 1. Request Flow

```
API Request → Unified Service → Provider Manager → AI Provider → Quality Engine → Storage Service → Response
```

### 2. Component Interaction

1. **Request Processing**: Unified service receives request and validates input
2. **Context Generation**: Product data is enriched with brand and market context
3. **Template Rendering**: AI templates are rendered with product-specific data
4. **Provider Selection**: Best available provider is selected based on quotas and health
5. **Content Generation**: AI provider generates content using rendered prompts
6. **Quality Assessment**: Generated content is assessed for quality and safety
7. **Storage**: Assets are stored with enhanced metadata and SEO optimization
8. **Response**: Complete results are returned with quality metrics

### 3. Data Flow

```
Product Data → Context Engine → Template Manager → AI Provider → Quality Engine → Storage → Final Assets
```

## API Usage

### Main Endpoint: `/media/ai-generate`

This is the primary endpoint for generating AI media content.

#### Request Format

```json
POST /media/ai-generate
{
    "product_ids": ["sneaker_001", "dress_001"],
    "media_types": ["text", "image", "video"],
    "content_types": ["seo_title", "description", "hero_image"],
    "platforms": ["website", "instagram"],
    "styles": ["hero", "lifestyle"],
    "custom_config": {
        "quality_threshold": 0.8,
        "auto_approve": true
    }
}
```

#### Parameters

- **product_ids** (required): List of product IDs to generate media for
- **media_types** (required): Types of media to generate
  - `"text"`: SEO content, descriptions, marketing copy
  - `"image"`: Product photos, lifestyle images, social media visuals
  - `"video"`: Product videos, promotional content, social media clips
- **content_types** (optional): Specific content types to generate
  - Text: `"seo_title"`, `"description"`, `"alt_text"`, `"short_description"`
  - Image: `"hero_image"`, `"lifestyle_image"`, `"social_image"`
  - Video: `"product_video"`, `"15_second_hero"`, `"social_clip"`
- **platforms** (optional): Target platforms for optimization
  - `"website"`, `"instagram"`, `"facebook"`, `"tiktok"`, `"email"`
- **styles** (optional): Content styles
  - `"hero"`, `"lifestyle"`, `"social"`, `"minimal"`, `"luxury"`
- **custom_config** (optional): Additional configuration options

#### Response Format

```json
{
    "request_id": "req_1703123456",
    "success": true,
    "total_processing_time": 12.5,
    "total_cost": 0.25,
    "summary": {
        "total_products": 2,
        "successful_products": 2,
        "total_assets": 8,
        "average_quality_score": 0.87,
        "assets_needing_review": 1,
        "cost_breakdown": {
            "text": 0.05,
            "image": 0.15,
            "video": 0.05
        }
    },
    "products": [
        {
            "product_id": "sneaker_001",
            "success": true,
            "processing_time": 6.2,
            "total_cost": 0.12,
            "quality_summary": {
                "average_quality": 0.89,
                "assets_needing_review": 0,
                "total_assets": 4,
                "quality_distribution": {
                    "excellent": 3,
                    "good": 1,
                    "acceptable": 0,
                    "needs_improvement": 0
                }
            },
            "assets": [
                {
                    "asset_id": "sneaker_001_seo_title_1703123456",
                    "media_type": "text",
                    "content_type": "seo_title",
                    "content": "Urban Runner Pro | Nike - Athletic Sneakers",
                    "url": "https://cdn.example.com/assets/text/sneaker_001_seo_title.json",
                    "quality_score": 0.92,
                    "needs_review": false,
                    "metadata": {
                        "qa_score": 0.92,
                        "template_used": "text/seo/seo_title",
                        "constraints_passed": true
                    }
                }
            ]
        }
    ]
}
```

## System Design

### 1. Modular Architecture

The system follows a modular design with clear separation of concerns:

- **Service Layer**: Business logic and orchestration
- **Provider Layer**: AI service integrations with fallback chains
- **Storage Layer**: Asset management with metadata
- **Quality Layer**: Content validation and assessment
- **Template Layer**: Prompt management and rendering

### 2. Scalability Features

- **Async/Await**: Non-blocking operations throughout
- **Provider Fallbacks**: Automatic failover between AI providers
- **Rate Limiting**: Prevents quota exhaustion and manages costs
- **Concurrent Processing**: Multiple products processed in parallel
- **Caching**: Template and context caching for performance

### 3. Quality Assurance

- **Multi-Stage Validation**: Content, safety, brand compliance
- **Automated Scoring**: Quality metrics for each generated asset
- **Human Review Flagging**: Automatic detection of content needing review
- **Brand Alignment**: Ensures content matches brand guidelines

### 4. Cost Management

- **Provider Cost Tracking**: Real-time cost monitoring per provider
- **Budget Controls**: Daily/monthly spending limits
- **Usage Analytics**: Detailed usage and cost reporting
- **Optimization**: Automatic provider selection for cost efficiency

## Component Usage Guide

### 1. Adding New AI Providers

```python
# Create provider in modules/media/providers/new_provider.py
class NewProvider(MediaProviderPlugin):
    async def generate_media(self, request):
        # Implementation
        pass

# Register in providers_config.py
PROVIDER_CONFIGS["new_provider"] = {
    "enabled": True,
    "api_key": "your_api_key",
    "rate_limit": 100
}
```

### 2. Adding New Templates

```python
# Add to modules/templates/template_service.py
self.templates["text"]["new_category"] = {
    "new_template": {
        "template": "Your template with {{product.title}}",
        "constraints": {"max_length": 100},
        "description": "Template description"
    }
}
```

### 3. Extending Quality Checks

```python
# Add to modules/media/quality_engine.py
async def custom_quality_check(self, content, product_data):
    # Custom validation logic
    return quality_score
```

### 4. Custom Storage Metadata

```python
# Extend modules/storage/storage_service.py
async def _generate_enhanced_metadata(self, asset_type, product_data, metadata):
    # Add custom metadata fields
    metadata["custom_field"] = "custom_value"
    return metadata
```

## Configuration

### Environment Variables

```bash
# AI Provider Configuration
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key
BANANA_API_KEY=your_banana_key

# Storage Configuration
STORAGE_PROVIDER=s3
S3_BUCKET_NAME=your_bucket
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key

# Quality Thresholds
MIN_QUALITY_SCORE=0.7
AUTO_APPROVE_THRESHOLD=0.85
```

### Provider Limits

```python
# In providers_config.py
RATE_LIMITS = {
    "openai": {
        "requests_per_minute": 60,
        "requests_per_day": 1000,
        "cost_per_day": 50.0
    }
}
```

## Monitoring and Analytics

### 1. Provider Health

- Real-time status monitoring
- Error rate tracking
- Response time metrics
- Uptime percentage

### 2. Quality Metrics

- Average quality scores
- Content requiring review
- Safety check results
- Brand compliance rates

### 3. Cost Analytics

- Daily/monthly spending
- Cost per asset type
- Provider cost comparison
- Budget utilization

### 4. Usage Statistics

- Requests per provider
- Success/failure rates
- Processing times
- Asset generation volumes

## Best Practices

### 1. Request Optimization

- Batch multiple products in single requests
- Use specific content_types to avoid unnecessary generation
- Set appropriate quality thresholds
- Leverage caching for repeated requests

### 2. Quality Management

- Review assets flagged by quality engine
- Maintain brand guideline templates
- Monitor quality trends over time
- Adjust thresholds based on performance

### 3. Cost Control

- Set daily/monthly budgets
- Monitor provider costs regularly
- Use fallback chains strategically
- Optimize template efficiency

### 4. Performance

- Use async operations throughout
- Implement proper error handling
- Monitor processing times
- Scale providers based on demand

## Troubleshooting

### Common Issues

1. **Provider Quota Exceeded**: Check rate limits and usage stats
2. **Low Quality Scores**: Review templates and product data quality
3. **Storage Failures**: Verify storage configuration and permissions
4. **Template Errors**: Validate template syntax and product data structure

### Debugging

- Check logs for detailed error messages
- Use provider health endpoints
- Monitor quality assessment results
- Verify template rendering with sample data

## Future Enhancements

1. **Machine Learning**: Quality prediction models
2. **A/B Testing**: Template performance comparison
3. **Real-time Analytics**: Live dashboards and alerts
4. **Advanced Personalization**: User behavior-based content
5. **Multi-language Support**: Localized content generation
