# Quick Start Guide - AI Media Generation

## Getting Started in 5 Minutes

### 1. Basic Request

The simplest way to generate AI media is to call the main endpoint:

```bash
curl -X POST "http://localhost:8000/media/ai-generate" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "product_ids": ["sneaker_001"],
    "media_types": ["text"]
  }'
```

### 2. Example Products Available

The system comes with example products you can use immediately:

- `sneaker_001`: Urban Runner Pro (Nike sneakers)
- `dress_001`: Elegant Summer Dress (Zara)
- `laptop_001`: MacBook Pro 16" (Apple)

### 3. Media Types

- **text**: SEO titles, descriptions, marketing copy
- **image**: Product photos, lifestyle images (placeholder)
- **video**: Product videos, promotional content (placeholder)

### 4. Content Types for Text

- `seo_title`: SEO-optimized page titles
- `description`: Product descriptions
- `alt_text`: Image alt text for accessibility
- `short_description`: Brief product summaries

## Complete Example

### Request

```json
POST /media/ai-generate
{
    "product_ids": ["sneaker_001", "dress_001"],
    "media_types": ["text"],
    "content_types": ["seo_title", "description", "alt_text"],
    "platforms": ["website"],
    "styles": ["professional"]
}
```

### Response

```json
{
    "request_id": "req_1703123456",
    "success": true,
    "total_processing_time": 3.2,
    "total_cost": 0.06,
    "summary": {
        "total_products": 2,
        "successful_products": 2,
        "total_assets": 6,
        "average_quality_score": 0.87,
        "assets_needing_review": 0
    },
    "products": [
        {
            "product_id": "sneaker_001",
            "success": true,
            "assets": [
                {
                    "asset_id": "sneaker_001_seo_title_1703123456",
                    "media_type": "text",
                    "content_type": "seo_title",
                    "content": "Urban Runner Pro | Nike - Athletic Sneakers",
                    "quality_score": 0.92,
                    "needs_review": false
                },
                {
                    "asset_id": "sneaker_001_description_1703123456",
                    "media_type": "text",
                    "content_type": "description",
                    "content": "Discover the Urban Runner Pro by Nike. Premium athletic sneaker designed for urban running and everyday comfort. Available in Black, White, Red. Advanced cushioning technology for superior performance.",
                    "quality_score": 0.88,
                    "needs_review": false
                }
            ]
        }
    ]
}
```

## What Happens Behind the Scenes

1. **Request Validation**: System validates product IDs and parameters
2. **Product Data Retrieval**: Fetches product information from examples
3. **Template Rendering**: Uses Jinja2 templates to create AI prompts
4. **Content Generation**: Generates content using templates (currently template-based)
5. **Quality Assessment**: Scores content quality and flags for review if needed
6. **Storage**: Stores assets with enhanced metadata and SEO optimization
7. **Response**: Returns complete results with quality metrics

## System Components Used

### For Text Generation:
- **Template Manager**: Renders prompts with product data
- **Quality Engine**: Assesses content quality and safety
- **Storage Service**: Stores text assets as JSON with metadata
- **Provider Manager**: Manages AI provider selection (placeholder)

### Data Flow:
```
Product Data → Template Rendering → Quality Assessment → Storage → Response
```

## Testing Different Scenarios

### 1. Single Product, Multiple Content Types
```json
{
    "product_ids": ["sneaker_001"],
    "media_types": ["text"],
    "content_types": ["seo_title", "description", "alt_text", "short_description"]
}
```

### 2. Multiple Products, Basic Text
```json
{
    "product_ids": ["sneaker_001", "dress_001", "laptop_001"],
    "media_types": ["text"],
    "content_types": ["seo_title", "description"]
}
```

### 3. With Custom Configuration
```json
{
    "product_ids": ["sneaker_001"],
    "media_types": ["text"],
    "content_types": ["seo_title"],
    "custom_config": {
        "quality_threshold": 0.9,
        "auto_approve": false
    }
}
```

## Understanding the Response

### Success Indicators
- `success: true` - Overall request succeeded
- `product.success: true` - Individual product succeeded
- `quality_score > 0.8` - High quality content
- `needs_review: false` - Content approved automatically

### Quality Metrics
- **0.9-1.0**: Excellent quality
- **0.8-0.9**: Good quality
- **0.6-0.8**: Acceptable quality
- **<0.6**: Needs improvement

### Cost Information
- Text generation: ~$0.01 per asset
- Processing time: 1-5 seconds per product
- Storage: Included in generation cost

## Next Steps

1. **Try Different Products**: Use all three example products
2. **Experiment with Content Types**: Try different combinations
3. **Check Quality Scores**: Monitor what generates high-quality content
4. **Review Storage**: Check stored assets in the storage system
5. **Monitor Costs**: Track generation costs and processing times

## Common Use Cases

### E-commerce SEO
```json
{
    "product_ids": ["sneaker_001"],
    "media_types": ["text"],
    "content_types": ["seo_title", "description", "alt_text"]
}
```

### Product Catalog
```json
{
    "product_ids": ["sneaker_001", "dress_001", "laptop_001"],
    "media_types": ["text"],
    "content_types": ["description", "short_description"]
}
```

### Marketing Content
```json
{
    "product_ids": ["sneaker_001"],
    "media_types": ["text"],
    "content_types": ["description"],
    "styles": ["marketing", "promotional"]
}
```

## Troubleshooting

### Common Issues

1. **Product Not Found**: Use valid product IDs from examples
2. **Invalid Media Type**: Use "text", "image", or "video"
3. **Template Error**: Check that content_types are valid
4. **Low Quality Score**: Content may need manual review

### Debug Information

Check the response for:
- `error_message`: Specific error details
- `quality_score`: Content quality assessment
- `metadata`: Additional generation information
- `processing_time`: Performance metrics

## Production Considerations

When moving to production:

1. **Add Real Products**: Replace example products with database queries
2. **Configure AI Providers**: Set up OpenAI, Anthropic, or other providers
3. **Set Quality Thresholds**: Adjust based on your quality requirements
4. **Monitor Costs**: Set up budget alerts and usage tracking
5. **Review Workflow**: Implement human review for flagged content

This system is designed to scale from simple testing to production e-commerce media generation!
